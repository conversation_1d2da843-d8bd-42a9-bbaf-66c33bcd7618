name: iOS Enhanced CI/CD Pipeline

on:
  push:
    branches: [ "main", "develop", "release/*" ]
  pull_request:
    branches: [ "main", "develop" ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'qa6'
        type: choice
        options:
        - qa6
        - qa7
        - qa8
        - stage
        - production
      build_type:
        description: 'Build type'
        required: true
        default: 'development'
        type: choice
        options:
        - development
        - adhoc
        - appstore
      skip_tests:
        description: 'Skip tests'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18.x'
  XCODE_VERSION: '15.2'
  IOS_SIMULATOR_DEVICE: 'iPhone 15'

jobs:
  # Job 1: Pre-flight checks
  preflight:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.setup.outputs.environment }}
      build_type: ${{ steps.setup.outputs.build_type }}
      should_deploy: ${{ steps.setup.outputs.should_deploy }}
    
    steps:
    - name: Setup build parameters
      id: setup
      run: |
        ENVIRONMENT="${{ github.event.inputs.environment || 'qa6' }}"
        BUILD_TYPE="${{ github.event.inputs.build_type || 'development' }}"
        
        echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
        echo "build_type=$BUILD_TYPE" >> $GITHUB_OUTPUT
        
        # Determine if we should deploy based on build type and environment
        if [[ "$BUILD_TYPE" == "appstore" && "$ENVIRONMENT" == "production" ]]; then
          echo "should_deploy=true" >> $GITHUB_OUTPUT
        else
          echo "should_deploy=false" >> $GITHUB_OUTPUT
        fi
        
        echo "🚀 Building for environment: $ENVIRONMENT"
        echo "📱 Build type: $BUILD_TYPE"

  # Job 2: Web Build and Test
  web-build-test:
    needs: preflight
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        scope: '@citushealth-inc'
        registry-url: 'https://npm.pkg.github.com'
        cache: 'npm'
        
    - name: Cache node modules
      uses: actions/cache@v3
      with:
        path: ~/.npm
        key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-node-
        
    - name: Clean npm cache
      run: npm cache clean --force
      
    - name: Install dependencies
      run: npm install --force
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        
    - name: Install CLI tools
      run: npm install -g @angular/cli @ionic/cli @capacitor/cli
      
    - name: Lint code
      if: github.event.inputs.skip_tests != 'true'
      run: npm run lint
      continue-on-error: true
      
    - name: Run unit tests
      if: github.event.inputs.skip_tests != 'true'
      run: |
        sudo apt-get update
        wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
        sudo apt install ./google-chrome-stable_current_amd64.deb
        npm run test:coverage:ci
        
    - name: Upload test coverage
      if: github.event.inputs.skip_tests != 'true'
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage/
        retention-days: 7
        
    - name: Build web app
      run: |
        ENVIRONMENT="${{ needs.preflight.outputs.environment }}"
        echo "Building for environment: $ENVIRONMENT"
        ionic build --configuration=$ENVIRONMENT
        
    - name: Verify build output
      run: |
        echo "Build output verification:"
        ls -la www/
        echo "Index.html size: $(wc -c < www/index.html) bytes"
        
    - name: Upload web build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: web-build-${{ needs.preflight.outputs.environment }}
        path: www/
        retention-days: 7

  # Job 3: iOS Build
  ios-build:
    needs: [preflight, web-build-test]
    runs-on: macos-14
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        scope: '@citushealth-inc'
        registry-url: 'https://npm.pkg.github.com'
        cache: 'npm'
        
    - name: Download web build artifacts
      uses: actions/download-artifact@v4
      with:
        name: web-build-${{ needs.preflight.outputs.environment }}
        path: www/
        
    - name: Install dependencies
      run: npm install --force
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
        
    - name: Install CLI tools
      run: npm install -g @ionic/cli @capacitor/cli
      
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: ${{ env.XCODE_VERSION }}
        
    - name: Setup Ruby for CocoaPods
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.1'
        bundler-cache: true
        
    - name: Install CocoaPods
      run: |
        gem install cocoapods
        pod --version
        
    - name: Sync Capacitor
      run: |
        echo "Syncing Capacitor for iOS..."
        npx cap sync ios
        
    - name: Install iOS dependencies
      run: |
        cd ios/App
        pod install --repo-update
        
    - name: Build iOS App (Development)
      if: needs.preflight.outputs.build_type == 'development'
      run: |
        cd ios/App
        xcodebuild -workspace App.xcworkspace \
          -scheme App \
          -destination 'platform=iOS Simulator,name=${{ env.IOS_SIMULATOR_DEVICE }},OS=latest' \
          -configuration Debug \
          build
          
    - name: Import Code-Signing Certificates
      if: needs.preflight.outputs.build_type != 'development'
      uses: Apple-Actions/import-codesign-certs@v2
      with:
        p12-file-base64: ${{ secrets.IOS_CERTIFICATES_P12 }}
        p12-password: ${{ secrets.IOS_CERTIFICATES_PASSWORD }}
        
    - name: Build and Archive iOS App
      if: needs.preflight.outputs.build_type != 'development'
      run: |
        cd ios/App
        CONFIGURATION=${{ needs.preflight.outputs.build_type == 'appstore' && 'Release' || 'Debug' }}
        
        xcodebuild -workspace App.xcworkspace \
          -scheme App \
          -configuration $CONFIGURATION \
          -destination 'generic/platform=iOS' \
          -archivePath App.xcarchive \
          archive
          
        # Export IPA
        xcodebuild -exportArchive \
          -archivePath App.xcarchive \
          -exportPath ./build \
          -exportOptionsPlist ExportOptions.plist
          
    - name: Upload IPA Artifact
      if: needs.preflight.outputs.build_type != 'development'
      uses: actions/upload-artifact@v4
      with:
        name: ios-app-${{ needs.preflight.outputs.environment }}-${{ needs.preflight.outputs.build_type }}
        path: ios/App/build/*.ipa
        retention-days: 30

  # Job 4: Notify
  notify:
    needs: [preflight, web-build-test, ios-build]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Build Summary
      run: |
        echo "## 📱 iOS Build Summary"
        echo ""
        echo "**Environment:** ${{ needs.preflight.outputs.environment }}"
        echo "**Build Type:** ${{ needs.preflight.outputs.build_type }}"
        echo "**Commit:** ${{ github.sha }}"
        echo "**Branch:** ${{ github.ref_name }}"
        echo ""
        echo "**Results:**"
        echo "- Web Build: ${{ needs.web-build-test.result }}"
        echo "- iOS Build: ${{ needs.ios-build.result }}"
        echo ""
        if [[ "${{ needs.web-build-test.result }}" == "success" && "${{ needs.ios-build.result }}" == "success" ]]; then
          echo "✅ **Build completed successfully!**"
        else
          echo "❌ **Build failed. Check the logs for details.**"
        fi
