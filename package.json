{"name": "citus-health-ui-mobile-pwa", "version": "0.0.1", "private": true, "description": "Citus Health", "homepage": "http://www.citushealth.com/", "author": "CitusHealth", "scripts": {"build": "ng build", "build:qa6": "ionic build --configuration=qa6", "build:qa7": "ionic build --configuration=qa7", "build:qa8": "ionic build --configuration=qa8", "build:stage": "ionic build --configuration=stage", "build:prod": "ionic build --configuration=production", "ios:build": "ionic cap build ios", "ios:build:qa6": "ionic cap build ios -c=qa6", "ios:sync": "ionic cap sync ios", "ios:open": "ionic cap open ios", "e2e": "ng e2e", "lint": "ng lint", "ng": "ng", "start": "ng serve", "test": "ng test", "test:coverage:ci": "ng test --code-coverage --no-watch --no-progress --browsers=ChromeHeadless", "test:coverage": "ng test --code-coverage"}, "dependencies": {"@angular/cdk": "^17.3.1", "@angular/common": "^17.3.1", "@angular/core": "^17.3.1", "@angular/forms": "^17.3.1", "@angular/platform-browser": "^17.3.1", "@angular/platform-browser-dynamic": "^17.3.1", "@angular/router": "^17.3.1", "@angular/service-worker": "^17.3.1", "@apollo/client": "^3.11.0", "@awesome-cordova-plugins/clipboard": "^6.6.0", "@awesome-cordova-plugins/core": "^6.6.0", "@awesome-cordova-plugins/diagnostic": "^6.6.0", "@awesome-cordova-plugins/document-picker": "^6.6.0", "@awesome-cordova-plugins/in-app-browser": "^6.6.0", "@capacitor-community/file-opener": "^1.0.5", "@capacitor-community/native-audio": "^6.0.0", "@capacitor-community/sqlite": "^7.0.0", "@capacitor/android": "^5.7.4", "@capacitor/app": "^5.0.7", "@capacitor/camera": "^5.0.9", "@capacitor/core": "^5.7.8", "@capacitor/device": "^5.0.7", "@capacitor/filesystem": "^5.2.1", "@capacitor/geolocation": "^5.0.7", "@capacitor/inappbrowser": "^2.1.0", "@capacitor/ios": "^5.7.4", "@capacitor/keyboard": "^5.0.8", "@capacitor/local-notifications": "^5.0.7", "@capacitor/network": "^5.0.7", "@capacitor/push-notifications": "^5.1.1", "@capacitor/share": "6.0", "@capacitor/splash-screen": "^5.0.7", "@capacitor/status-bar": "^5.0.7", "@fullcalendar/angular": "^6.1.10", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@ionic-native/core": "^5.36.0", "@ionic-native/keychain-touch-id": "^5.36.0", "@ionic-native/native-storage": "^5.36.0", "@ionic-native/sqlite": "^5.36.0", "@ionic/angular": "^7.8.1", "@maskito/angular": "^2.2.0", "@maskito/core": "^2.2.0", "@meddv/ngx-pinch-zoom": "^17.0.1", "@ng-idle/core": "^14.0.0", "@ng-idle/keepalive": "^14.0.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@okta/okta-angular": "^6.4.0", "@okta/okta-auth-js": "^7.8.0", "@okta/okta-signin-widget": "7.33.2", "angular16-signaturepad": "^16.0.3", "angular2-draggable": "^16.0.0", "apollo-angular": "^6.0.0", "autolinker": "^4.0.0", "capacitor-document-scanner": "^2.0.0", "capacitor-native-biometric": "^4.2.2", "cordova-clipboard": "^1.3.0", "cordova-documentpicker": "^1.2.2", "cordova-plugin-keychain-touch-id": "^3.2.1", "cordova-plugin-nativestorage": "^2.3.2", "cordova-plugin-screen-orientation": "^3.0.4", "cordova-sqlite-storage": "^6.1.0", "cordova.plugins.diagnostic": "^7.1.4", "core-js": "^3.39.0", "crypto-js": "^4.2.0", "elevio": "^1.3.8", "flag-icons": "^7.2.0", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "ion7-calendar": "^3.6.29", "jetifier": "^2.0.0", "karma-coverage": "^2.2.1", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "ng-otp-input": "^1.9.3", "ng2-pdf-viewer": "^10.0.0", "ngx-countdown": "^17.1.1", "ngx-device-detector": "^7.0.0", "ngx-permissions": "^17.1.0", "ngx-pinch-zoom": "^2.6.2", "pdfjs-dist": "^4.10.38", "rxjs": "^7.8.1", "signature_pad": "^5.0.4", "socket.io-client": "^4.7.5", "tslib": "^2.6.2", "uuid": "^9.0.1", "zone.js": "^0.14.4"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.1", "@angular-eslint/eslint-plugin": "^18.4.1", "@angular-eslint/eslint-plugin-template": "^18.4.1", "@angular-eslint/template-parser": "^18.4.1", "@angular/cli": "^17.3.1", "@angular/compiler": "^17.3.1", "@angular/compiler-cli": "^17.3.1", "@angular/language-service": "^17.3.1", "@capacitor/cli": "^5.7.8", "@ionic/angular-toolkit": "^11.0.1", "@types/jasmine": "^5.1.4", "@types/jasminewd2": "^2.0.13", "@types/jest": "^29.5.14", "@types/node": "^20.11.30", "@typescript-eslint/eslint-plugin": "^8.15.0", "engine.io-parser": "^5.2.2", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-diff": "^2.0.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "jasmine-core": "^5.1.2", "jasmine-spec-reporter": "^7.0.0", "jest": "^29.7.0", "jest-preset-angular": "^14.5.3", "karma": "^6.4.3", "karma-chrome-launcher": "^3.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "protractor": "~7.0.0", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}