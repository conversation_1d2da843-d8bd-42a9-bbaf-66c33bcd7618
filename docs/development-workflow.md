# Development Workflow Guide

This guide outlines the development workflow for the CitusHealth Mobile Application, including local development, testing, and deployment processes.

## 🚀 Quick Start

### Local Development Setup

1. **<PERSON><PERSON> and Install**
   ```bash
   git clone <repository-url>
   cd citushealth-mobile-client
   npm install
   ```

2. **Install Global Dependencies**
   ```bash
   npm install -g @ionic/cli @capacitor/cli @angular/cli
   ```

3. **Start Development Server**
   ```bash
   npm start
   # or
   ionic serve
   ```

## 📱 Building for Different Environments

### Using npm Scripts (Recommended)

```bash
# Web builds
npm run build:qa6          # Build for QA6 environment
npm run build:qa7          # Build for QA7 environment
npm run build:qa8          # Build for QA8 environment
npm run build:stage        # Build for Staging environment
npm run build:prod         # Build for Production environment

# iOS builds
npm run ios:build:qa6       # Build iOS for QA6 (your preferred method)
npm run ios:sync            # Sync Capacitor
npm run ios:open            # Open Xcode
```

### Using Build Scripts

```bash
# Environment-specific builds
./scripts/build-env.sh qa6 ios --clean
./scripts/build-env.sh stage ios --open
./scripts/build-env.sh production ios

# Quick deployment
./scripts/quick-deploy.sh qa6 --ios --clean
./scripts/quick-deploy.sh stage --skip-tests
```

## 🔄 CI/CD Workflows

### Available GitHub Actions Workflows

1. **Main CI/CD Pipeline** (`.github/workflows/ios-ci-cd.yml`)
   - Triggers: Push to main/develop/release branches, PRs
   - Manual trigger with environment selection
   - Full build, test, and deployment pipeline

2. **Enhanced Pipeline** (`.github/workflows/ios-enhanced.yml`)
   - Improved error handling and caching
   - Better artifact management
   - Enhanced notifications

3. **Test Pipeline** (`.github/workflows/ios-test.yml`)
   - Quick testing of build setup
   - Validation before full deployment

### Manual Workflow Triggers

1. Go to GitHub Actions tab
2. Select desired workflow
3. Click "Run workflow"
4. Choose parameters:
   - **Environment**: qa6, qa7, qa8, stage, production
   - **Build Type**: development, adhoc, appstore
   - **Options**: Skip tests, etc.

## 🧪 Testing Strategy

### Local Testing

```bash
# Run unit tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:coverage:ci

# Lint code
npm run lint
```

### iOS Testing

```bash
# Build for iOS simulator
ionic cap build ios -c=qa6

# Open in Xcode for testing
ionic cap open ios

# Run on simulator
# (Use Xcode to run on specific simulators)
```

## 🌍 Environment Configuration

### Available Environments

| Environment | Configuration File | Purpose |
|-------------|-------------------|---------|
| qa6 | `environment.qa6.ts` | QA Environment 6 (Default) |
| qa7 | `environment.qa7.ts` | QA Environment 7 |
| qa8 | `environment.qa8.ts` | QA Environment 8 |
| stage | `environment.stage.ts` | Staging Environment |
| production | `environment.prod.ts` | Production Environment |

### Environment-Specific Features

- Different API endpoints
- Different authentication configurations
- Different feature flags
- Different analytics configurations

## 📋 Development Checklist

### Before Starting Development

- [ ] Pull latest changes from main/develop
- [ ] Install/update dependencies: `npm install`
- [ ] Verify environment configuration
- [ ] Run tests to ensure baseline: `npm test`

### Before Committing

- [ ] Run linting: `npm run lint`
- [ ] Run tests: `npm run test:coverage`
- [ ] Test build for target environment
- [ ] Verify no console errors
- [ ] Update documentation if needed

### Before Deployment

- [ ] All tests passing
- [ ] Code review completed
- [ ] Environment-specific testing completed
- [ ] Performance testing (if applicable)
- [ ] Security review (if applicable)

## 🔧 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clean and rebuild
   rm -rf node_modules package-lock.json
   npm cache clean --force
   npm install
   ```

2. **iOS Build Issues**
   ```bash
   # Clean iOS build
   cd ios/App
   rm -rf Pods Podfile.lock
   pod install --repo-update
   ```

3. **Capacitor Sync Issues**
   ```bash
   # Force sync
   npx cap sync ios --force
   ```

### Getting Help

1. Check the build logs in GitHub Actions
2. Review the troubleshooting section in `docs/ios-ci-cd-setup.md`
3. Contact the development team
4. Check Ionic/Capacitor documentation

## 🚀 Deployment Process

### QA Deployments

1. **Automatic**: Push to develop branch triggers QA6 build
2. **Manual**: Use GitHub Actions with qa6/qa7/qa8 environment
3. **Local**: Use `./scripts/quick-deploy.sh qa6 --ios`

### Staging Deployments

1. Create release branch: `release/v1.x.x`
2. Trigger staging build via GitHub Actions
3. Test thoroughly in staging environment

### Production Deployments

1. Merge release branch to main
2. Create git tag: `v1.x.x`
3. Trigger production build with appstore build type
4. Upload to App Store Connect

## 📊 Monitoring and Analytics

- Build success/failure notifications
- Performance monitoring in each environment
- Crash reporting and analytics
- User feedback collection

## 🔐 Security Considerations

- All secrets stored in GitHub Secrets
- Code signing certificates properly managed
- Environment-specific API keys
- Regular security updates and dependency audits

---

For more detailed information, see:
- [iOS CI/CD Setup Guide](ios-ci-cd-setup.md)
- [README-iOS-CICD.md](../README-iOS-CICD.md)
- Project-specific documentation
