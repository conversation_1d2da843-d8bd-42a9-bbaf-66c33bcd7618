#!/bin/bash

# Environment-specific build script for CitusHealth Mobile App
# Usage: ./scripts/build-env.sh [environment] [platform] [options]
# Example: ./scripts/build-env.sh qa6 ios --clean

set -e

# Default values
ENVIRONMENT="qa6"
PLATFORM="ios"
CLEAN=false
OPEN_IDE=false
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment] [platform] [options]"
    echo ""
    echo "Environments:"
    echo "  qa6        QA Environment 6 (default)"
    echo "  qa7        QA Environment 7"
    echo "  qa8        QA Environment 8"
    echo "  stage      Staging Environment"
    echo "  production Production Environment"
    echo ""
    echo "Platforms:"
    echo "  ios        iOS platform (default)"
    echo "  android    Android platform"
    echo "  web        Web platform"
    echo ""
    echo "Options:"
    echo "  --clean    Clean build (remove node_modules and reinstall)"
    echo "  --open     Open IDE after build (Xcode for iOS)"
    echo "  --verbose  Verbose output"
    echo "  --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 qa6 ios --clean"
    echo "  $0 production ios --open"
    echo "  $0 stage android --verbose"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        qa6|qa7|qa8|stage|production)
            ENVIRONMENT="$1"
            shift
            ;;
        ios|android|web)
            PLATFORM="$1"
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --open)
            OPEN_IDE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
case $ENVIRONMENT in
    qa6|qa7|qa8|stage|production)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        show_usage
        exit 1
        ;;
esac

# Validate platform
case $PLATFORM in
    ios|android|web)
        ;;
    *)
        print_error "Invalid platform: $PLATFORM"
        show_usage
        exit 1
        ;;
esac

print_status "Starting build for environment: $ENVIRONMENT, platform: $PLATFORM"

# Clean build if requested
if [ "$CLEAN" = true ]; then
    print_status "Performing clean build..."
    rm -rf node_modules
    rm -rf package-lock.json
    npm cache clean --force
fi

# Install dependencies
print_status "Installing dependencies..."
if [ "$VERBOSE" = true ]; then
    npm install
else
    npm install --silent
fi

# Install global dependencies if needed
if ! command -v ionic &> /dev/null; then
    print_status "Installing Ionic CLI..."
    npm install -g @ionic/cli
fi

if ! command -v cap &> /dev/null; then
    print_status "Installing Capacitor CLI..."
    npm install -g @capacitor/cli
fi

# Build web app
print_status "Building web app for $ENVIRONMENT..."
if [ "$VERBOSE" = true ]; then
    ionic build --configuration=$ENVIRONMENT
else
    ionic build --configuration=$ENVIRONMENT --quiet
fi

# Platform-specific build
case $PLATFORM in
    ios)
        print_status "Syncing Capacitor for iOS..."
        ionic cap sync ios
        
        print_status "Building iOS app..."
        ionic cap build ios -c=$ENVIRONMENT
        
        if [ "$OPEN_IDE" = true ]; then
            print_status "Opening Xcode..."
            ionic cap open ios
        fi
        ;;
    android)
        print_status "Syncing Capacitor for Android..."
        ionic cap sync android
        
        print_status "Building Android app..."
        ionic cap build android -c=$ENVIRONMENT
        
        if [ "$OPEN_IDE" = true ]; then
            print_status "Opening Android Studio..."
            ionic cap open android
        fi
        ;;
    web)
        print_status "Web build completed."
        ;;
esac

print_success "Build completed successfully!"
print_status "Environment: $ENVIRONMENT"
print_status "Platform: $PLATFORM"

if [ "$PLATFORM" = "ios" ]; then
    print_status "iOS project location: ios/App/"
    print_status "To open in Xcode: ionic cap open ios"
elif [ "$PLATFORM" = "android" ]; then
    print_status "Android project location: android/"
    print_status "To open in Android Studio: ionic cap open android"
fi
