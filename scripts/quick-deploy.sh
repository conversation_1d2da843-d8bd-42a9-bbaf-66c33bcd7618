#!/bin/bash

# Quick Deploy Script for CitusHealth Mobile App
# This script provides shortcuts for common deployment tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    echo "Quick Deploy Script for CitusHealth Mobile App"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  qa6           Quick build and deploy to QA6 environment"
    echo "  qa7           Quick build and deploy to QA7 environment"
    echo "  qa8           Quick build and deploy to QA8 environment"
    echo "  stage         Quick build and deploy to Staging environment"
    echo "  prod          Quick build and deploy to Production environment"
    echo "  test          Run tests only"
    echo "  build-only    Build without deployment"
    echo "  status        Check current deployment status"
    echo ""
    echo "Options:"
    echo "  --ios         Build for iOS (default)"
    echo "  --android     Build for Android"
    echo "  --web         Build for Web only"
    echo "  --clean       Clean build"
    echo "  --skip-tests  Skip running tests"
    echo "  --open        Open IDE after build"
    echo ""
    echo "Examples:"
    echo "  $0 qa6 --ios --clean"
    echo "  $0 stage --android"
    echo "  $0 prod --skip-tests"
    echo "  $0 test"
}

# Default values
ENVIRONMENT=""
PLATFORM="ios"
CLEAN=false
SKIP_TESTS=false
OPEN_IDE=false
BUILD_ONLY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        qa6|qa7|qa8|stage|prod)
            ENVIRONMENT="$1"
            if [ "$1" = "prod" ]; then
                ENVIRONMENT="production"
            fi
            shift
            ;;
        test)
            print_status "Running tests..."
            npm run test:coverage:ci
            print_success "Tests completed!"
            exit 0
            ;;
        build-only)
            BUILD_ONLY=true
            shift
            ;;
        status)
            print_status "Checking deployment status..."
            echo "Current branch: $(git branch --show-current)"
            echo "Last commit: $(git log -1 --oneline)"
            echo "Git status:"
            git status --short
            exit 0
            ;;
        --ios)
            PLATFORM="ios"
            shift
            ;;
        --android)
            PLATFORM="android"
            shift
            ;;
        --web)
            PLATFORM="web"
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --open)
            OPEN_IDE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment is required"
    show_usage
    exit 1
fi

print_status "🚀 Quick Deploy to $ENVIRONMENT environment"
print_status "📱 Platform: $PLATFORM"

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository"
    exit 1
fi

# Check for uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
    print_warning "You have uncommitted changes:"
    git status --short
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
fi

# Run tests if not skipped
if [ "$SKIP_TESTS" = false ]; then
    print_status "Running tests..."
    npm run test:coverage:ci || {
        print_error "Tests failed!"
        read -p "Continue with deployment anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Deployment cancelled"
            exit 1
        fi
    }
fi

# Build the app
print_status "Building application..."
./scripts/build-env.sh $ENVIRONMENT $PLATFORM $([ "$CLEAN" = true ] && echo "--clean") $([ "$OPEN_IDE" = true ] && echo "--open")

if [ "$BUILD_ONLY" = true ]; then
    print_success "Build completed! (build-only mode)"
    exit 0
fi

# Deployment logic (customize based on your deployment strategy)
print_status "Preparing for deployment..."

case $ENVIRONMENT in
    qa6|qa7|qa8)
        print_status "Deploying to QA environment: $ENVIRONMENT"
        # Add your QA deployment logic here
        # Example: Upload to TestFlight, Firebase App Distribution, etc.
        ;;
    stage)
        print_status "Deploying to Staging environment"
        # Add your staging deployment logic here
        ;;
    production)
        print_warning "Deploying to PRODUCTION environment!"
        echo "This will deploy to the production environment."
        read -p "Are you sure you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Production deployment cancelled"
            exit 0
        fi
        # Add your production deployment logic here
        ;;
esac

print_success "🎉 Deployment to $ENVIRONMENT completed successfully!"

# Show next steps
echo ""
print_status "Next steps:"
case $PLATFORM in
    ios)
        echo "  • Check Xcode for any build issues"
        echo "  • Test on iOS Simulator or device"
        echo "  • Upload to TestFlight if ready"
        ;;
    android)
        echo "  • Check Android Studio for any build issues"
        echo "  • Test on Android Emulator or device"
        echo "  • Upload to Play Console if ready"
        ;;
    web)
        echo "  • Test the web application"
        echo "  • Deploy to web hosting if ready"
        ;;
esac
