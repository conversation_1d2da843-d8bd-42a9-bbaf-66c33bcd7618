import { Keepalive } from '@ng-idle/keepalive';
import { APIs } from 'src/app/constants/apis';
import { Idle, IdleExpiry, DEFAULT_INTERRUPTSOURCES } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule, HttpResponse } from '@angular/common/http';
import { Router, RouterModule } from '@angular/router';
import { ActionSheetController, AlertController, IonicModule, ModalController, PopoverController } from '@ionic/angular';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { ChooseRecipientsPage } from 'src/app/pages/message-center/choose-recipients/choose-recipients.page';
import { ChoosePatientComponent } from 'src/app/pages/message-center/choose-patient/choose-patient.component';
import { of, throwError } from 'rxjs';
import { theme } from 'src/theme/theme';
import { Constants } from 'src/app/constants/constants';
import { HttpService } from 'src/app/services/http-service/http.service';
import { DocumentDetailedCountData } from 'src/app/interfaces/document-request-signature';
import { environment } from 'src/environments/environment';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';
import { Config } from 'src/app/constants/config';
import { Socket } from 'src/app/constants/socket';
import { isBlank } from 'src/app/utils/utils';
import { PushNotifications } from '@capacitor/push-notifications';
import { Capacitor } from '@capacitor/core';
import { Urls } from 'src/app/constants/urls';
import { OktaService } from '../okta/okta.service';
import { SocketService } from '../socket-service/socket.service';
import { CommonService } from '../common-service/common.service';

describe('SharedService', () => {
  const { modalSpy, alertSpy, popoverSpy } = TestConstants;
  let alertController: AlertController;
  let service: SharedService;
  let modalController: ModalController;
  let popoverController: PopoverController;
  let idleSpy: Idle;
  let router: Router;
  let common: CommonService;
  let httpService: HttpService;
  let socketService: SocketService;
  let actionSheet: ActionSheetController;
  const actionSpy = TestConstants.actionSheetSpy;

  beforeEach(() => {
    if (Capacitor.isPluginAvailable) {
      // Mock the PushNotifications.addListener method before calling service methods
      spyOn(PushNotifications, 'addListener').and.callFake((eventName, callback) => {
        // Ensure the correct event name is handled
        if (eventName === 'pushNotificationActionPerformed') {
          setTimeout(() => {
            callback({
              actionId: '',
              notification: {
                id: '',
                data: {
                  aps: {
                    'user-data': JSON.stringify({ pushDeepLink: { activeMessage: { chatroomid: 123 } } })
                  }
                }
              }
            });
          }, 0); // Simulating async event triggering
        }

        const handle = {
          remove: () => Promise.resolve()
        };

        return Object.assign(Promise.resolve(handle), handle);
      });
    }

    TestBed.configureTestingModule({
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateTestingModule],
      providers: [
        HttpService,
        SocketService,
        InAppBrowser,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        ModalController,
        PopoverController,
        Keepalive,
        OktaService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    });
    socketService = TestBed.inject(SocketService);
    idleSpy = TestBed.inject(Idle);
    idleSpy.onIdleEnd.emit(1);
    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    spyOn(router, 'navigateByUrl').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    popoverController = TestBed.inject(PopoverController);
    popoverSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();
    popoverSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    spyOn(alertController, 'dismiss').and.stub();
    actionSheet = TestBed.inject(ActionSheetController);
    spyOn(actionSheet, 'create').and.callFake(() => {
      return actionSpy;
    });
    httpService = TestBed.inject(HttpService);
    common = TestBed.inject(CommonService);
    service = TestBed.inject(SharedService);
    Object.defineProperties(service, TestConstants.sharedServiceTestProperties);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
  it('execute fetchUsers', () => {
    service.fetchUsers({});
    expect(service.fetchUsers).toBeTruthy();
  });
  it('execute getVisitDetails', () => {
    service.getVisitDetails(1);
    expect(service.getVisitDetails).toBeTruthy();
  });

  it('execute createChannelsFromSoundList ', () => {
    service.createChannelsFromSoundList();
    expect(service.createChannelsFromSoundList).toBeTruthy();
  });

  it('execute patientNameDisplay', () => {
    const payload = {
      caregiver_userid: null,
      displayname: '',
      caregiver_displayname: '',
      dob: '08/09/1992',
      IdentityValue: '1'
    };
    service.patientNameDisplay(payload);
    expect(service.patientNameDisplay).toBeTruthy();
  });
  it('execute patientNameDisplay: caregiver', () => {
    const payload = {
      caregiver_userid: 1,
      displayname: '',
      caregiver_displayname: '',
      dob: '08/09/1992',
      IdentityValue: '1'
    };
    service.patientNameDisplay(payload);
    expect(service.patientNameDisplay).toBeTruthy();
  });

  it('execute startNewChat', () => {
    service.startNewChat(1);
    expect(service.startNewChat).toBeTruthy();
  });

  it('should emit an event and send a push notification if type and data conditions are met', () => {
    const message = {
      chatroomid: 123,
      chatroom_id: 456,
      sent: 100,
      userid: 789,
      messageType: 1,
      baseId: 2,
      fromName: 'John',
      message_group_id: 3,
      createdby: 4
    };

    const deepLinking = {
      state: 'eventmenu.group-chat',
      stateParams: {
        targetID: 123,
        targetName: 'group-chat'
      },
      activeMessage: {
        sent: 100,
        messageType: 1,
        baseId: 2,
        userid: 789,
        fromName: '"John"',
        message_group_id: 3,
        createdby: 4
      }
    };

    spyOn(socketService, 'emitEvent').and.callFake((eventName, data, callback) => {
      callback(99, 'someType');
    });
    spyOn(service, 'sentPushNotification');
    service.confirmReviewPushNotification(message);
    expect(socketService.emitEvent).toHaveBeenCalledOnceWith(
      'confirmReviewPushNotification',
      {
        chatRoomId: 123,
        messageUserId: 789
      },
      jasmine.any(Function)
    );

    expect(service.sentPushNotification).toHaveBeenCalledWith(789, '0', 'Your message is getting reviewed', '', deepLinking, '', {
      sourceId: '10001',
      sourceCategoryId: '10001_006'
    });
  });

  it('execute newMessageAction : Default', () => {
    service.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
    expect(service.newMessageAction).toBeTruthy();
  });

  it('execute newMessageAction : Patient -Multiple staffs', () => {
    service.userData.group = '3';
    service.userData.roleName = 'Patient';
    spyOn(service, 'startNewChat').and.returnValue(of({ staffLists: [] }));
    service.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
    expect(service.newMessageAction).toBeTruthy();
  });

  it('execute newMessageAction : Patient- Default', () => {
    service.userData.group = '3';
    service.userData.roleName = 'Patient';
    spyOn(service, 'startNewChat').and.returnValue(of({}));
    service.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
    expect(service.newMessageAction).toBeTruthy();
  });

  it('execute newMessageAction : has single associated patient', () => {
    service.userData.group = '3';
    service.userData.roleName = 'Caregiver';
    service.selectedAssociatePatient = '';
    spyOn(service, 'startNewChat').and.returnValue(of({ message: [] }));
    service.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
    expect(service.newMessageAction).toBeTruthy();
  });

  it('execute newMessageAction : has single associated patient: caregiver', () => {
    service.userData.group = '3';
    service.userData.roleName = 'Caregiver';
    service.selectedAssociatePatient = '';
    service.userData.associated_user.push({});
    modalSpy.onDidDismiss.and.resolveTo({ data: { userId: '12' } });
    spyOn(service, 'startNewChat').and.returnValue(of({}));
    service.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
    expect(service.newMessageAction).toBeTruthy();
  });

  it('execute presentChoosePatientModal: Multiple staffs', fakeAsync(() => {
    spyOn(service, 'startNewChat').and.returnValue(of({ staffLists: [] }));
    service.presentChoosePatientModal(ChoosePatientComponent, ChooseRecipientsPage).then(() => {
      expect(service.presentChoosePatientModal).toBeTruthy();
    });
  }));

  it('execute presentChoosePatientModal', fakeAsync(() => {
    spyOn(service, 'startNewChat').and.returnValue(of({ chatRoomId: '1' }));
    service.presentChoosePatientModal(ChoosePatientComponent, ChooseRecipientsPage).then(() => {
      expect(service.presentChoosePatientModal).toBeTruthy();
    });
  }));

  it('execute consoloCheckFormType', () => {
    service.consoloCheckFormType(1);
    expect(service.consoloCheckFormType).toBeTruthy();
  });

  it('execute reminderForForm', () => {
    service.reminderForForm([]);
    expect(service.reminderForForm).toBeTruthy();
  });
  it('execute updateToAppLessVideo', () => {
    service.updateToAppLessVideo([]);
    expect(service.updateToAppLessVideo).toBeTruthy();
  });
  it('execute getSites', () => {
    service.getSites();
    expect(service.getSites).toBeTruthy();
  });
  it('execute reminderForDocument', () => {
    service.reminderForDocument();
    expect(service.reminderForDocument).toBeTruthy();
  });
  it('execute checkAttachmentCount true', () => {
    expect(service.checkAttachmentCount(11, 10)).toBeTrue();
  });
  it('execute setCountryCode append', () => {
    expect(service.setCountryCode('91')).toEqual('+91');
  });
  it('execute setCountryCode with +', () => {
    expect(service.setCountryCode('+91')).toEqual('+91');
  });
  it('execute setCalendarPickerMaxYear', () => {
    expect(service.setCalendarPickerMaxYear()).toBeTruthy();
  });
  it('execute getSiteAppName', () => {
    expect(service.getSiteAppName()).toBe('');
  });
  it('execute getAppName', () => {
    expect(service.getAppName({})).toBe(theme.name);
  });

  it('should return image props when item is an image', () => {
    const item = { name: 'image.jpg', file_path: 'path/' };
    const index = 0;
    const result = service.fetchImageViewerModalProps(item, index);
    expect(result).toEqual({
      url: `${environment.visitScheduleAttachmentUrl}${item.file_path}${item.name}`,
      downloadUrl: `${environment.visitScheduleAttachmentUrl}${item.file_path}${item.name}`,
      type: Constants.documentTypes.image
    });
  });

  it('should return PDF props when item is not an image', () => {
    const item = { name: 'document.pdf', file_path: 'path/' };
    const index = 0;
    const result = service.fetchImageViewerModalProps(item, index);
    expect(result).toBeDefined();
  });

  it('should set href for anchor element when file format is allowed for download', () => {
    const item = { name: 'document.odt', file_path: 'path/' };
    const index = 0;
    const anchorElement = document.createElement('a');
    spyOn(document, 'getElementById').and.returnValue(anchorElement);
    const result = service.fetchImageViewerModalProps(item, index);
    expect(result).toBeUndefined();
    expect(anchorElement.href).toBe(`${environment.visitScheduleAttachmentUrl}${item.file_path}${item.name}`);
  });

  it('should not update anything when messageList is falsy', () => {
    service.messageList = null;
    service.childMessageUpdated(123);
  });

  it('should not update anything when baseId is not found in messageList', () => {
    const messages: any = [];
    service.messageList = messages;
    service.childMessageUpdated(123);
    expect(service.childMessageUpdated).toBeTruthy();
  });

  it('should update messageList and related properties', () => {
    const messages: any = [{ chatroomid: 123, maskedReplyMessages: [{ unreadCount: 2 }, { unreadCount: 3 }], unreadCountMasked: 5 }];
    const res = { message: [{ unreadCount: 3 }, { unreadCount: 4 }] };
    spyOn(service, 'fetchReplyMessagesofMasked').and.returnValue(of(res));
    service.messageList = messages;
    service.childMessageUpdated(123);
    expect(service.childMessageUpdated).toBeTruthy();
  });

  it('execute checkAttachmentCount false', () => {
    expect(service.checkAttachmentCount(1, 10)).toBeFalse();
  });
  it('execute validateEmail valid', () => {
    expect(service.validateEmail('<EMAIL>')).toBeTrue();
  });
  it('execute validateEmail invalid', () => {
    expect(service.validateEmail('abcfgmail.com')).toBeFalse();
  });
  it('execute guidCreate', () => {
    expect(service.guidCreate(111)).toBeTruthy();
  });
  it('execute inAppBrowserExecuteAddOnScript', fakeAsync(() => {
    Object.defineProperty(service, 'browser', {
      value: {
        executeScript: () => {
          return new Promise((resolve) => {
            resolve('acceptCall');
          });
        },
        insertCSS: () => {},
        hide: () => undefined
      }
    });
    service.inAppBrowserExecuteAddOnScript({ from: 'videoCall', message: '' });
    tick(1000);
    expect(service.inAppBrowserExecuteAddOnScript).toBeTruthy();
  }));

  it('should call window.screen.orientation.unlock', () => {
    spyOn(window.screen.orientation, 'unlock');
    service.unlockDevice();
    expect(window.screen.orientation.unlock).toHaveBeenCalled();
  });

  it('should return the correct font size based on the provided height', () => {
    const result = service.getFontSize(String('11'));
    expect(result).toBe(6);
  });

  it('execute setSessionTimeout', () => {
    service.setSessionTimeout();
    expect(service.setSessionTimeout).toBeDefined();
  });

  it('execute setSessionTimeoutCommonCode', () => {
    service.setSessionTimeoutCommonCode();
    expect(service.setSessionTimeoutCommonCode).toBeDefined();
  });

  it('execute showSessionTimeoutModal', () => {
    spyOn(popoverController, 'create').and.returnValue(Promise.resolve(popoverSpy.present));
    service.showSessionTimeoutModal();
    expect(service.showSessionTimeoutModal).toBeDefined();
  });
  it('should execute getDocTypeIntegrationStatus', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.getDocTypeIntegrationStatus({ patientId: '123', messageId: '123' });
    expect(service.getDocTypeIntegrationStatus).toBeDefined();
  });

  it('should execute getViewHistoryDetails', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.getViewHistoryDetails({
      defaultTimeZone: 'IST',
      visit_id: '123'
    });
    expect(service.getViewHistoryDetails).toBeDefined();
  });

  it('should execute uploadAttachment', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.uploadAttachment();
    expect(service.uploadAttachment).toBeDefined();
  });

  it('should execute manageVisit', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.manageVisit({});
    expect(service.manageVisit).toBeDefined();
  });

  it('should execute deleteVisit', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.deleteVisit({});
    expect(service.deleteVisit).toBeDefined();
  });

  it('should execute visitScheduleActivityTrack', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.visitScheduleActivityTrack({});
    expect(service.visitScheduleActivityTrack).toBeDefined();
  });

  it('should execute viewAvailability', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.viewAvailability({});
    expect(service.viewAvailability).toBeDefined();
  });

  it('should execute getResourcesData', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.getResourcesData({});
    expect(service.getResourcesData).toBeDefined();
  });

  it('should execute getVisitTaskDetails', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.getVisitTaskDetails({});
    expect(service.getVisitTaskDetails).toBeDefined();
  });

  it('should execute getAllTimeZones', () => {
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    service.getAllTimeZones();
    expect(service.getAllTimeZones).toBeDefined();
  });

  it('should execute getVisitAdministrationDetails', () => {
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    service.getVisitAdministrationDetails({});
    expect(service.getVisitAdministrationDetails).toBeDefined();
  });

  it('should execute getPayor', () => {
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    service.getPayor({});
    expect(service.getPayor).toBeDefined();
  });

  it('should execute getLocationTypes', () => {
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    service.getLocationTypes();
    expect(service.getLocationTypes).toBeDefined();
  });

  it('should execute getLocationNames', () => {
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    service.getLocationNames();
    expect(service.getLocationNames).toBeDefined();
  });

  it('should return true if value is Constants.configTrue', () => {
    const result = service.isConfigTrue(Constants.configTrue);
    expect(result).toBeTruthy();
  });

  it('should return false if value is Constants.configFalse', () => {
    const result = service.isConfigTrue(Constants.configFalse);
    expect(result).toBeFalsy();
  });

  it('should return Config Value', () => {
    service.userData = TestConstants.userData;
    const result = service.getConfigValue('signature_serverside');
    expect(result).toEqual('1');
  });

  it('should return Site Config Value', () => {
    service.userData = TestConstants.userData;
    const result = service.getSiteConfigValue('branch_start_time');
    expect(result).toEqual('9:0');
  });

  it('should return site config value if present', () => {
    service.userData = TestConstants.userData;
    const result = service.getSiteTenantConfigValue('magiclink_verification_expiry_time');
    expect(result).toBe('10');
  });

  it('should return tenant config value if site config value is blank', () => {
    service.userData = TestConstants.userData;
    const result = service.getSiteTenantConfigValue('magiclink_verification_token_expiration_time');
    expect(result).toBe('10');
  });

  it('should return empty string if both site and tenant config values are blank', () => {
    service.userData = TestConstants.userData;
    const result = service.getSiteTenantConfigValue('magiclink_token_expiration_time');
    expect(result).toBe('');
  });

  it('should return empty string if userData is undefined', () => {
    service.userData = undefined;
    const result = service.getSiteTenantConfigValue('testKey');
    expect(result).toBe('');
  });

  it('should execute fetchAllMessages', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.fetchAllMessages({});
    expect(service.fetchAllMessages).toBeDefined();
  });

  it('should execute fetchReplyMessagesofMasked', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.fetchReplyMessagesofMasked(12345);
    expect(service.fetchReplyMessagesofMasked).toBeDefined();
  });

  it('should execute generateVidyoTocken', () => {
    spyOn(httpService, 'doPostByUrl').and.returnValue(of(''));
    service.generateVidyoTocken({});
    expect(service.generateVidyoTocken).toBeDefined();
  });

  it('should execute getUserTags', () => {
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    service.getUserTags('test', 12345);
    expect(service.getUserTags).toBeDefined();
  });

  it('should execute searchTenantIds', () => {
    service.searchTenantIds();
    expect(service.searchTenantIds).toBeDefined();
  });

  it('should execute sortMessageList', () => {
    const mockMessageList: any = [{ sent: 3 }, { sent: 1 }, { sent: 2 }];
    service.messageList = mockMessageList;
    service.messageCount = 0;
    service.sortMessageList();
    expect(service.sortMessageList).toBeDefined();
  });

  it('should increment messageCount if incrementCount is true', () => {
    const mockMessageList: any = [{ sent: 3 }, { sent: 1 }, { sent: 2 }];
    service.messageList = mockMessageList;
    service.messageCount = 1;
    service.sortMessageList(true);
    expect(service.sortMessageList).toBeDefined();
  });

  it('should execute updateMessageDetailsFromSocket', () => {
    const message: any = {
      message: 'Initial Message',
      sent: '2023-10-18T12:00:00',
      unreadCount: 0,
      userid: '1',
      fromAvatar: 'Avatar1',
      fromName: 'User1',
      role: 'User',
      markAsRead: false,
      chatrommUerCount: undefined
    };
    const data = {
      args: {
        message: 'Updated Message',
        sentTime: '2023-10-18T13:00:00',
        senderUserDetails: [
          {
            userid: '2'
          }
        ]
      }
    };

    service.updateMessageDetailsFromSocket(message, data);
    expect(service.searchTenantIds).toBeDefined();
  });

  describe('getFlagType', () => {
    it('should return "high" for flag type Constants.flagTypes.high', () => {
      const result = service.getFlagType(Constants.flagTypes.high);
      expect(result).toEqual(Constants.flagClass.high);
    });

    it('should return "medium" for flag type Constants.flagTypes.medium', () => {
      const result = service.getFlagType(Constants.flagTypes.medium);
      expect(result).toEqual(Constants.flagClass.medium);
    });

    it('should return "low" for flag type Constants.flagTypes.low', () => {
      const result = service.getFlagType(Constants.flagTypes.low);
      expect(result).toEqual(Constants.flagClass.low);
    });

    it('should return "null" for flag type noFlag', () => {
      const result = service.getFlagType(Constants.flagTypes.noFlag);
      expect(result).toEqual(Constants.flagClass.noFlag);
    });
  });
  describe('updateDocumentDetailedCount', () => {
    it('should update document count when valid data is provided', () => {
      const mockData: DocumentDetailedCountData = {
        mySignatureRequestCount: {
          totalPendingCount: 10,
          totalSignedCount: 20,
          totalArchiveCount: 30
        }
      };
      service.updateDocumentDetailedCount(mockData);
      expect(service.documentCount).toBeDefined();
    });

    it('should not update document count when data is missing', () => {
      const mockData: DocumentDetailedCountData = {
        mySignatureRequestCount: null
      };
      service.updateDocumentDetailedCount(mockData);
      expect(service.documentCount).toBeUndefined();
    });
  });

  describe('getSiteAppName', () => {
    it('should return the app name for a valid site ID with branding enabled', () => {
      const mockUserData = {
        mySites: [
          {
            id: 1,
            enable_support_widget_branding: '1',
            app_name: 'App1'
          },
          {
            id: 2,
            enable_support_widget_branding: '0',
            app_name: 'App2'
          }
        ]
      };
      TestConstants.userData.mySites = mockUserData.mySites;
      service.userData = TestConstants.userData;
      const result = service.getSiteAppName(1);
      expect(result).toBe('App1');
    });
    it('should return an empty string for a valid site ID with branding disabled', () => {
      const mockUserData = {
        mySites: [
          {
            id: 1,
            enable_support_widget_branding: '0',
            app_name: 'App1'
          },
          {
            id: 2,
            enable_support_widget_branding: '0',
            app_name: 'App2'
          }
        ]
      };
      TestConstants.userData.mySites = mockUserData.mySites;
      service.userData = TestConstants.userData;
      const result = service.getSiteAppName(2);
      expect(result).toBe('');
    });

    it('should return an empty string for no user data', () => {
      TestConstants.userData.mySites = [];
      const result = service.getSiteAppName(1);
      expect(result).toBe('');
    });
  });

  it('should execute notPartOfChatRedirect', () => {
    service.notPartOfChatRedirect();
    expect(service.notPartOfChatRedirect).toBeDefined();
  });

  it('should execute showValidationMessageForUserChoice with Text', () => {
    service.showValidationMessageForUserChoice('', 'text');
    expect(service.showValidationMessageForUserChoice).toBeDefined();
  });

  it('should execute showValidationMessageForUserChoice with Sign', () => {
    service.showValidationMessageForUserChoice('', 'sign');
    expect(service.showValidationMessageForUserChoice).toBeDefined();
  });

  it('should execute showValidationMessageForUserChoice with Checkbox', () => {
    service.showValidationMessageForUserChoice('', 'Checkbox');
    expect(service.showValidationMessageForUserChoice).toBeDefined();
  });

  it('should execute fetchImageViewerModalProps', () => {
    service.fetchImageViewerModalProps({ name: '' }, 1);
    expect(service.fetchImageViewerModalProps).toBeDefined();
  });

  it('should execute presentPdfFromLink', () => {
    const response = new HttpResponse<ArrayBuffer>();
    spyOn(httpService, 'doFetchFileBuffer').and.returnValue(of(response));
    service.presentPdfFromLink({ url: 'test' });
    expect(service.presentPdfFromLink).toBeDefined();
  });

  it('should execute presentPdfFromLink: throw error', () => {
    spyOn(httpService, 'doFetchFileBuffer').and.returnValue(throwError(''));
    service.presentPdfFromLink({ url: 'test' });
    expect(service.presentPdfFromLink).toBeDefined();
  });

  it('should execute inAppBrowserOptions', () => {
    service.inAppBrowserOptions();
    expect(service.inAppBrowserOptions).toBeDefined();
  });

  it('should execute fetchFileFromBlob', () => {
    service.fetchFileFromBlob({ blob: new Blob(), fileName: '' });
    expect(service.fetchFileFromBlob).toBeDefined();
  });

  it('should execute openFile', () => {
    service.openFile({ url: 'dfgsd' });
    expect(service.openFile).toBeDefined();
  });

  it('should execute webPushNotificationPermission', () => {
    service.webPushNotificationPermission();
    expect(service.webPushNotificationPermission).toBeDefined();
  });

  it('should execute checkPermission', () => {
    service.checkPermission();
    expect(service.checkPermission).toBeDefined();
  });

  it('should execute requestPermission', () => {
    service.requestPermission();
    expect(service.requestPermission).toBeDefined();
  });

  it('should execute registerPush', () => {
    service.registerPush();
    expect(service.registerPush).toBeDefined();
  });

  it('should execute emitUserPushRegistrationEvent', () => {
    service.emitUserPushRegistrationEvent({}, true);
    expect(service.emitUserPushRegistrationEvent).toBeDefined();
  });

  it('should execute userPushDeviceRegistration', () => {
    service.userPushDeviceRegistration({}, true);
    expect(service.userPushDeviceRegistration).toBeDefined();
  });

  it('should execute nativePushNotificationEnable', () => {
    service.nativePushNotificationEnable();
    expect(service.nativePushNotificationEnable).toBeDefined();
  });

  describe('validationMessages', () => {
    it('should call validationMessages with role field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.role);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with siteId field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.siteId);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with firstName field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.firstName);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with lastName field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.lastName);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with staffId field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.staffId);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with contact field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.contact);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with dob field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.dob);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with mrnForPatients field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.mrnForPatients);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with delegatedRole field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.delegatedRole);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with password field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.password);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with confirmPassword field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.confirmPassword);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with userId field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.userId);
      expect(service.validationMessages).toBeDefined();
    });

    it('should call validationMessages with relation field', () => {
      service.validationMessages(Constants.inviteUserValidationFields.relation);
      expect(service.validationMessages).toBeDefined();
    });
  });

  it('should call executeSaveAsDraft call', () => {
    service.executeSaveAsDraft();
    expect(service.executeSaveAsDraft).toBeDefined();
  });
  it('should call enableFormPolling call', () => {
    service.enableFormPolling();
    expect(service.enableFormPolling).toBeDefined();
  });
  it('should call enableDocumentPolling call', () => {
    service.enableDocumentPolling();
    expect(service.enableDocumentPolling).toBeDefined();
  });
  it('should call addNewChatFromAPI call', () => {
    service.addNewChatFromAPI('11416');
    expect(service.addNewChatFromAPI).toBeDefined();
  });
  it('should call trackLoginActivity call', () => {
    service.trackLoginActivity(1);
    expect(service.trackLoginActivity).toBeDefined();
  });
  it('should call enableConfigSocket call', () => {
    service.enableConfigSocket();
    expect(service.enableConfigSocket).toBeDefined();
  });
  it('should call enablePushNotificationSocket call', () => {
    service.enablePushNotificationSocket();
    expect(service.enablePushNotificationSocket).toBeDefined();
  });
  it('should call presentActionSheet call', () => {
    service.presentActionSheet({ message: { chatroomId: '1212' }, index: 1, flagApiType: 'msg' }, () => {});
    expect(service.presentActionSheet).toBeDefined();
  });
  it('should call presentCountryPopover call', () => {
    service.presentCountryPopover({}, () => undefined);
    expect(service.presentCountryPopover).toBeDefined();
  });
  it('should call getCountryDetails call', () => {
    service.getCountryDetails('91', 'Ind');
    expect(service.getCountryDetails).toBeDefined();
  });
  it('should call doFlag call', () => {
    service.doFlag({ message: { id: '12', chatroomId: '23323' }, i: 1, flagType: 1, flagApiType: 'thread' });
    const response = new HttpResponse<ArrayBuffer>();
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    expect(service.doFlag).toBeDefined();
  });
  it('should return false if user is not a partner', () => {
    service.userData.group = '40';
    const result = service.loggedUserIsPartner();
    expect(result).toBeFalsy();
  });
  it('should return false if userData is not defined', () => {
    service.userData = undefined;
    const result = service.loggedUserIsPartner();
    expect(result).toBeFalsy();
  });
  it('should call getTenantRolesByPrivilege with allowEnrollmentInitiation', () => {
    spyOn(httpService, 'doGet').and.returnValue(of([{ id: 'role1' }, { id: 'role2' }]));
    service.getTenantRolesByPrivilege(Constants.privilegeKey.allowEnrollmentInitiation);
    expect(service.getTenantRolesByPrivilege).toBeDefined();
  });
  it('should call getTenantRolesByPrivilege with allowDelegation', () => {
    spyOn(httpService, 'doGet').and.returnValue(of([{ id: 'role1' }, { id: 'role2' }]));
    service.getTenantRolesByPrivilege(Constants.privilegeKey.allowDelegation);
    expect(service.getTenantRolesByPrivilege).toBeDefined();
  });
  it('should call setPatientSearchReqBody with params', () => {
    service.setPatientSearchReqBody('Test', 0, 20, 'search', '');
    expect(service.setPatientSearchReqBody).toBeDefined();
  });
  it('should return the correct number of minutes', () => {
    const currentTime = '14:30';
    const scheduleInterval = {};
    const result = service.getMinutes(currentTime, scheduleInterval);
    // 14 * 60 + 30 = 870
    expect(result).toBe(870);
  });
  it('should call scheduleSelectionFilter', () => {
    service.userData = TestConstants.userData;
    service.scheduleSelectionFilter({ userId: 123 });
    expect(service.scheduleSelectionFilter).toBeDefined();
  });
  it('should call scheduleSelectionFilter with schedulerData', () => {
    service.userData = TestConstants.userData;
    service.scheduleSelectionFilter({ userId: 123 });
    expect(service.scheduleSelectionFilter).toBeDefined();
  });
  it('should call scheduleSelectionFilter masterSchedulerData', () => {
    service.userData = TestConstants.userData;
    service.scheduleSelectionFilter({ userId: 123 }, true);
    expect(service.scheduleSelectionFilter).toBeDefined();
  });
  it('should call getUsersByTenantRoleWithTagId', () => {
    const response = new HttpResponse<ArrayBuffer>();
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    service.getUsersByTenantRoleWithTagId({
      isTenantRoles: true,
      roleId: '123',
      pageCount: 1,
      tagsId: 123,
      formRecipients: 'Test',
      searchKeyword: 'Test Demo',
      siteIds: '123,1234',
      needVirtualPatients: false,
      nursingAgencies: 'demo',
      reoleSearchNeeded: false,
      status: 'Done',
      admissionId: undefined
    });
    expect(service.getUsersByTenantRoleWithTagId).toBeDefined();
  });
  it('should call getAssociatedPatientsByTagId', () => {
    const response = new HttpResponse<ArrayBuffer>();
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    service.getAssociatedPatientsByTagId({ tagId: 123 });
    expect(service.getAssociatedPatientsByTagId).toBeDefined();
  });
  it('should call setRootActivity', () => {
    service.setRootActivity(123);
    expect(service.setRootActivity).toBeDefined();
  });
  it('should call setParentActivity', () => {
    service.setParentActivity(123);
    expect(service.setParentActivity).toBeDefined();
  });
  it('should call resetSelectedDateFilterData', () => {
    service.resetSelectedDateFilterData();
    expect(service.resetSelectedDateFilterData).toBeDefined();
  });
  it('should call resetActivity', () => {
    service.resetActivity();
    expect(service.resetActivity).toBeDefined();
  });
  it('should call pageAccess', () => {
    service.pageAccess();
    expect(service.pageAccess).toBeDefined();
  });
  it('should call errorHandler', () => {
    service.errorHandler({});
    expect(service.isLoading).toBe(false);
  });
  it('should call getConfiguration', () => {
    const response = new HttpResponse<ArrayBuffer>();
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    service.getConfiguration();
    expect(service.getConfiguration).toBeDefined();
  });

  it('should call getConfiguration : throw error', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    service.getConfiguration();
    expect(service.getConfiguration).toBeTruthy();
  });
  it('should call getConfigurationLocal', () => {
    service.getConfigurationLocal();
    expect(service.getConfigurationLocal).toBeDefined();
  });
  it('should call setPermissions', () => {
    service.setPermissions('test, text1');
    expect(service.setPermissions).toBeDefined();
  });
  it('should call resetSessionProfileData', () => {
    service.resetSessionProfileData(TestConstants.userData);
    expect(service.resetSessionProfileData).toBeDefined();
  });
  it('should call setidleTimeStamp', () => {
    service.setidleTimeStamp();
    expect(service.setidleTimeStamp).toBeDefined();
  });
  it('should call resume', () => {
    service.resume();
    expect(service.resume).toBeDefined();
  });
  it('should call checkBranchHours', () => {
    service.checkBranchHours(true);
    expect(service.checkBranchHours).toBeDefined();
  });
  it('check permission and call requestPermission for iOS', () => {
    spyOn(service.platform, 'is').and.returnValue(true);
    spyOn(service, 'requestPermission').and.stub();
    service.checkPermission();
    expect(service.requestPermission).toHaveBeenCalled();
  });
  it('should call addRouteLink if pushDeepLink exists in data', () => {
    const jsonData = { pushDeepLink: 'your-deep-link' };
    service.addRouteLink(jsonData);
    expect(service.addRouteLink).toBeDefined();
  });
  describe('deepLinkRedirect', () => {
    it('should call deepLinkRedirect with state groupChat', () => {
      const jsonData = { stateParams: { targetID: '123' }, state: 'eventmenu.group-chat' };
      service.deepLinkRedirect(jsonData);
      expect(service.deepLinkRedirect).toBeDefined();
    });

    it('should call deepLinkRedirect with state formsCenter', () => {
      const jsonData = { stateParams: { targetID: '123' }, state: 'eventmenu.forms' };
      service.deepLinkRedirect(jsonData);
      expect(service.deepLinkRedirect).toBeDefined();
    });

    it('should call deepLinkRedirect with state documentCenter', () => {
      const jsonData = { stateParams: { targetID: '123' }, state: 'eventmenu.obtain-sign' };
      service.deepLinkRedirect(jsonData);
      expect(service.deepLinkRedirect).toBeDefined();
    });

    it('should call deepLinkRedirect with state deliveryCenter', () => {
      const jsonData = { stateParams: { targetID: '123' }, state: 'eventmenu.delivery-details' };
      service.deepLinkRedirect(jsonData);
      expect(service.deepLinkRedirect).toBeDefined();
    });

    it('should call deepLinkRedirect with state deliveryCenter with Ticketnumber and id', () => {
      const jsonData = { stateParams: { targetID: '123', Ticketnumber: '124', uid: '2323' }, state: 'eventmenu.delivery-details' };
      service.deepLinkRedirect(jsonData);
      expect(service.deepLinkRedirect).toBeDefined();
    });

    it('should call deepLinkRedirect with state scheduleCenter', () => {
      const jsonData = { stateParams: { targetID: '' }, state: 'schedule-center' };
      service.deepLinkRedirect(jsonData);
      expect(service.deepLinkRedirect).toBeDefined();
      expect(service.setRouterLink).toEqual('/schedule-center/visits');
    });
    it('should call deepLinkRedirect with state scheduleCenter to individual view', () => {
      const jsonData = { stateParams: { targetID: 'qwe' }, state: 'schedule-center' };
      service.deepLinkRedirect(jsonData);
      expect(service.deepLinkRedirect).toBeDefined();
      expect(service.setRouterLink.replace(/\/[^\\/]+$/, '')).toEqual('/schedule-center/visits/view/qwe');
    });
  });
  it('should call pushLocalNotification', () => {
    service.pushLocalNotification({
      title: 'text',
      id: '12345',
      data: undefined
    });
    expect(service.pushLocalNotification).toBeDefined();
  });
  it('should call closeInAppSession', () => {
    service.closeInAppSession();
    expect(service.closeInAppSession).toBeDefined();
  });
  it('should call isOfflineFormsEnabled', () => {
    service.isOfflineFormsEnabled();
    expect(service.isOfflineFormsEnabled).toBeDefined();
  });
  describe('getMessageFormCounts', () => {
    it('should call getMessageFormCounts with messages', () => {
      const response = {
        messages: 10
      };
      spyOn(httpService, 'doGet').and.returnValue(of(response));
      service.getMessageFormCounts();
      expect(service.getMessageFormCounts).toBeDefined();
    });
    it('should call getMessageFormCounts with forms', () => {
      const response = {
        forms: {
          pending: 20,
          completed: 15,
          archived: 10,
          draft: 5
        }
      };
      spyOn(httpService, 'doGet').and.returnValue(of(response));
      service.getMessageFormCounts();
      expect(service.getMessageFormCounts).toBeDefined();
    });
    it('should call getMessageFormCounts with error', () => {
      spyOn(service, 'errorHandler').and.stub();
      spyOn(httpService, 'doGet').and.returnValue(throwError(''));
      service.getMessageFormCounts();
      expect(service.getMessageFormCounts).toBeDefined();
      expect(service.errorHandler).toHaveBeenCalled();
    });
  });
  describe('doFlag', () => {
    it('should call doFlag call with flagType = low and flagApiType=thread', () => {
      const response = { status: 1 };
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      service.doFlag({ message: { id: '12', chatroomId: '23323' }, i: 1, flagType: 1, flagApiType: 'thread' });
      expect(service.doFlag).toBeDefined();
    });
    it('should call doFlag call with flagType = noFlag  and flagApiType=thread and status=1', () => {
      const response = { status: 1 };
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      service.doFlag({ message: { id: '12', chatroomId: '23323' }, i: 1, flagType: 0, flagApiType: 'thread' });
      expect(service.doFlag).toBeDefined();
    });
    it('should call doFlag call with flagType = low and flagApiType=msg and status=1', () => {
      const response = { status: 1 };
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      service.doFlag({ message: { id: '12', chatroomId: '23323' }, i: 1, flagType: 1, flagApiType: 'msg' });
      expect(service.doFlag).toBeDefined();
    });
    it('should call doFlag call with flagType = noFlag  and flagApiType=msg and status=1', () => {
      const response = { status: 1 };
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      service.doFlag({ message: { id: '12', chatroomId: '23323' }, i: 1, flagType: 0, flagApiType: 'msg' });
      expect(service.doFlag).toBeDefined();
    });
    it('should call doFlag call with flagType = noFlag  and flagApiType=thread and status=0', () => {
      const response = { status: 0 };
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      service.doFlag({ message: { id: '12', chatroomId: '23323' }, i: 1, flagType: 0, flagApiType: 'thread' });
      expect(service.doFlag).toBeDefined();
    });
    it('should call doFlag call with flagType = low and flagApiType=msg and status=0', () => {
      const response = { status: 0 };
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      service.doFlag({ message: { id: '12', chatroomId: '23323' }, i: 1, flagType: 1, flagApiType: 'msg' });
      expect(service.doFlag).toBeDefined();
    });
  });
  it('should call clearAllDeliveredNotifications', () => {
    spyOn(service.platform, 'is').and.returnValue(true);
    service.clearAllDeliveredNotifications();
    expect(service.clearAllDeliveredNotifications).toBeDefined();
  });
 
  describe('showWarningMessages', () => {
    it('should call  showWarningMessages for staffIdMRNMissing', () => {
      service.showWarningMessages('', '', 'document');
      expect(service.showWarningMessages).toBeDefined();
    });
    it('should call  showWarningMessages for patientMRNMissing: staffId', () => {
      service.showWarningMessages('', '124', 'document');
      expect(service.showWarningMessages).toBeDefined();
    });
    it('should call  showWarningMessages for patientMRNMissing: patientIdentity', () => {
      service.showWarningMessages('123', '', 'document');
      expect(service.showWarningMessages).toBeDefined();
    });
  });
  it('should call checkAllowEditForm', () => {
    const response = { ALLOWEDIT: 1, FROMID: 123 };
    const payload = {
      formid: '123',
      patientid: '123'
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.checkAllowEditForm(payload, { displayName: '', recipientName: '' });
    expect(service.checkAllowEditForm).toBeDefined();
  });

  it('should call sendFormToRecipients', () => {
    const response = {
      success: true
    };
    const payload = {
      formid: '123',
      patientid: '123',
      resend: true
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.sendFormToRecipients(payload);
    expect(service.sendFormToRecipients).toBeDefined();
  });
  it('should call formPolling', () => {
    const response = {
      send_to: ['123', '1234']
    };
    service.formPolling(response);
    expect(service.formPolling).toBeDefined();
  });

  it('should call fetchVisitUserResourceData', () => {
    const response = {
      success: true
    };
    const payload = {
      type: 'staffs',
      pageCount: 0,
      searchKeyword: '',
      offset: 0,
      limit: 20
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.fetchVisitUserResourceData(payload);
    expect(service.fetchVisitUserResourceData).toBeDefined();
  });

  it('should call fetchAvailability', () => {
    const response = {
      success: true
    };
    const payload = {
      page: 'document',
      isNew: true,
      pageLoad: true,
      userId: '12345'
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.fetchAvailability(payload);
    expect(service.fetchAvailability).toBeDefined();
  });
  it('should call getVisit', () => {
    const response = {
      success: true
    };
    const payload = {
      calendarType: 'week',
      pageLoad: true
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.getVisit(payload);
    expect(service.getVisit).toBeDefined();
  });
  it('should call getVisitById', () => {
    const response = {
      success: true
    };
    const payload = {
      id: '1234'
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.getVisitById(payload);
    expect(service.getVisitById).toBeDefined();
  });
  it('should call updateVisitSchedule', () => {
    const response = {
      success: true
    };
    const payload = {
      id: '1234'
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.updateVisitSchedule(payload);
    expect(service.updateVisitSchedule).toBeDefined();
  });
  it('should call getAllVisits', () => {
    const response = {
      success: true
    };
    const payload = {
      calendarType: 'week',
      selectedUserId: '1234',
      isNew: true,
      pageLoad: true
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.getAllVisits(payload);
    expect(service.getAllVisits).toBeDefined();
  });
  it('should call getvisitLocations', () => {
    const response = {
      success: true
    };
    const payload = {
      isFrom: 'schedule-center'
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.getvisitLocations(payload);
    expect(service.getvisitLocations).toBeDefined();
  });
  it('should call getVisitAvailableUserResource', () => {
    const response = {
      success: true
    };
    const payload = {
      isFrom: 'schedule-center'
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.getVisitAvailableUserResource(payload);
    expect(service.getVisitAvailableUserResource).toBeDefined();
  });

  it('should call fetchPatientData', () => {
    const response = {
      success: true
    };
    const payload = {
      type: 'patient',
      tenantId: '123',
      pageCount: 0
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    service.fetchPatientData(payload);
    expect(service.fetchPatientData).toBeDefined();
  });

  it('should call inAppBrowserVideoButtonActionExecute', () => {
    service.inAppBrowserVideoButtonActionExecute();
    expect(service.inAppBrowserVideoButtonActionExecute).toBeDefined();
  });

  it('should call setDocFilename', () => {
    const fileNameDetails = {
      resultDocument: {
        displayText: { text: 'demo-text', document: '' },
        type: { metaTagFileNameFormatText: '{tagName}{documentName}', fileSavingFormatText: '{tagName}{documentName}' },
        typeName: ''
      },
      esi: '',
      typeName: '1221',
      associatePatient: 'patientDataList'
    };
    service.setDocFilename(fileNameDetails);
    expect(service.setDocFilename).toBeDefined();
  });
  it('should subscribe to obtainSignPolling event if enableDocCenter config is enabled', () => {
    spyOn(service, 'isEnableConfig').and.returnValue(true);
    spyOn(service.socketService, 'subscribeEvent').and.callThrough();
    service.enableDocumentPolling();
    expect(service.isEnableConfig).toHaveBeenCalledWith(Config.enableDocCenter);
    expect(service.socketService.subscribeEvent).toHaveBeenCalledWith(Socket.obtainSignPolling);
  });

  it('should not subscribe to obtainSignPolling event if enableDocCenter config is disabled', () => {
    spyOn(service, 'isEnableConfig').and.returnValue(false);
    spyOn(service.socketService, 'subscribeEvent');
    service.enableDocumentPolling();
    expect(service.isEnableConfig).toHaveBeenCalledWith(Config.enableDocCenter);
    expect(service.socketService.subscribeEvent).not.toHaveBeenCalled();
  });

  it('should update documentCountUpdated if value contains a message or signatureStatus is SIGNED', () => {
    spyOn(service, 'isEnableConfig').and.returnValue(true);
    spyOn(service.socketService, 'subscribeEvent').and.returnValue(of([{ message: 'test' }]));
    spyOn(service.documentCountUpdated, 'next');
    service.enableDocumentPolling();
    expect(service.documentCountUpdated.next).toHaveBeenCalledWith(0);
  });

  it('should return a promise that resolves to true when "Continue Without Contact" is confirmed', async () => {
    spyOn(common, 'showCustomAlert').and.returnValue(Promise.resolve(true));
    const result = await service.confirmWithoutContact();
    expect(result).toBeTruthy();
  });

  it('should resolve with false when return to edit is selected', (done) => {
    spyOn(common, 'showCustomAlert').and.returnValue(Promise.resolve(false));
    service.confirmWithoutContact().then((result) => {
      expect(result).toBe(false);
      done();
    });
  });

  it('should not update documentCountUpdated if value does not contain a message or signatureStatus is not SIGNED', () => {
    spyOn(service, 'isEnableConfig').and.returnValue(true);
    spyOn(service.socketService, 'subscribeEvent').and.returnValue(of([{}]));
    spyOn(service.documentCountUpdated, 'next');
    service.enableDocumentPolling();
    expect(service.documentCountUpdated.next).not.toHaveBeenCalled();
  });

  it('should return true if no chat thread filter is applied', () => {
    spyOn(window.sessionStorage, 'getItem').and.returnValue(JSON.stringify({}));
    const eventData = { chatThreadType: undefined };
    const result = service.updateInboxOnThreadTypeFilter(eventData);
    expect(result).toBeTrue();
  });

  it('should return true if chat thread filter is applied and event data chat thread type is in filter', () => {
    const storedValue = {
      [Constants.storageKeys.activeMessageFilterKeys]: {
        chatThreadTypes: [3, 2]
      }
    };
    spyOn(window.sessionStorage, 'getItem').and.returnValue(JSON.stringify(storedValue));
    const eventData = { chatThreadType: 'pdg' };
    const result = service.updateInboxOnThreadTypeFilter(eventData);
    expect(result).toBeTrue();
  });

  it('should return true if chat thread filter is applied and event data chat thread type is not in filter and window is not active inbox', () => {
    const storedValue = {
      [Constants.storageKeys.activeMessageFilterKeys]: {
        chatThreadTypes: [1, 2]
      }
    };
    spyOn(window.sessionStorage, 'getItem').and.returnValue(JSON.stringify(storedValue));
    const eventData = { chatThreadType: 'staff' };
    const result = service.updateInboxOnThreadTypeFilter(eventData);
    expect(result).toBeTrue();
  });

  it('should call fetchUsers and pass nursing_agencies if present in userData', () => {
    const reqData = { searchKeyword: 'test' };
    const response = { data: 'test' };
    service.userData = { nursing_agencies: 'agency1' } as any;
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    spyOn(service, 'isEnableConfig').and.returnValue(true);

    service.fetchUsers(reqData).subscribe((res) => {
      expect(res).toEqual(response);
    });

    expect(httpService.doGet).toHaveBeenCalledWith({
      endpoint: APIs.getUsersByTenantRole,
      extraParams: jasmine.objectContaining({
        nursingAgencies: 'agency1'
      }),
      loader: true
    });
  });

  it('should call fetchUsers and pass empty nursing_agencies if not present in userData', () => {
    const reqData = { searchKeyword: 'test' };
    const response = { data: 'test' };
    service.userData = {} as any;
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    spyOn(service, 'isEnableConfig').and.returnValue(true);

    service.fetchUsers(reqData).subscribe((res) => {
      expect(res).toEqual(response);
    });

    expect(httpService.doGet).toHaveBeenCalledWith({
      endpoint: APIs.getUsersByTenantRole,
      extraParams: jasmine.objectContaining({
        nursingAgencies: ''
      }),
      loader: true
    });
  });
  it('should call common.showToast if pushRegComplete is present in response', () => {
    const payload = { deviceToken: 'mockToken' };
    spyOn(httpService, 'doPostByUrl').and.returnValue(of({ status: 'success', pushRegComplete: true }));
    spyOn(service, 'emitUserPushRegistrationEvent').and.stub();
    spyOn(service, 'trackActivity').and.stub();
    spyOn(common, 'showToast').and.stub();

    service.userPushDeviceRegistration(payload);

    expect(common.showToast).toHaveBeenCalled();
    expect(service.emitUserPushRegistrationEvent).not.toHaveBeenCalled();
  });
  it('should call httpService.doPostByUrl with correct parameters and handle success response', () => {
    const payload = { deviceToken: 'mockToken' };
    const apiUrl = `${environment.pushServerAPIUrl}${Urls.createAWSPlatformUserEndpoint}`;
    spyOn(httpService, 'doPostByUrl').and.returnValue(of({ status: 'success' }));
    spyOn(service, 'emitUserPushRegistrationEvent').and.stub();
    spyOn(service, 'trackActivity').and.stub();
    spyOn(common, 'showToast').and.stub();

    service.userPushDeviceRegistration(payload);

    expect(httpService.doPostByUrl).toHaveBeenCalledWith({ payload, apiUrl }, true);
  });
  it('should call emitUserPushRegistrationEvent if the API response status is success and pushRegComplete is not present', () => {
    const payload = { deviceToken: 'mockToken' };
    const mockResponse = {
      status: Constants.success.toLowerCase()
    };

    spyOn(localStorage, 'getItem').and.callFake((key: string) => {
      if (key === Constants.storageKeys.deviceID) return 'mock-device-id';
      return null;
    });

    Object.defineProperty(localStorage, 'pushNotificationToken', {
      value: 'mock-token',
      configurable: true
    });
    Object.defineProperty(localStorage, 'userId', {
      value: 'mock-user-id',
      configurable: true
    });

    spyOn(httpService, 'doPostByUrl').and.returnValue(of(mockResponse));
    spyOn(service, 'emitUserPushRegistrationEvent');

    service.userPushDeviceRegistration(payload, false);

    expect(service.emitUserPushRegistrationEvent).toHaveBeenCalledWith(
      jasmine.objectContaining({
        uniqueDeviceId: 'mock-device-id',
        registrationId: 'mock-token',
        userId: 'mock-user-id',
        platform: service.platformValue
      }),
      false
    );
  });

  it('should call updateChatRoomId when roomId is not blank', () => {
    const component = { roomId: 5, updateChatRoomId: jasmine.createSpy('updateChatRoomId') }; // Valid roomId
    const chatroomid = 10;

    if (!isBlank(component.roomId)) {
      component.updateChatRoomId(Number(component.roomId), chatroomid);
    }

    expect(component.updateChatRoomId).toHaveBeenCalledWith(5, 10);
  });

  it('should not call updateChatRoomId when roomId is null', () => {
    const component = { roomId: null, updateChatRoomId: jasmine.createSpy('updateChatRoomId') }; // Null roomId
    const chatroomid = 10;

    if (!isBlank(component.roomId)) {
      component.updateChatRoomId(Number(component.roomId), chatroomid);
    }

    expect(component.updateChatRoomId).not.toHaveBeenCalled();
  });

  it('should not update anything when messageList is falsy', () => {
    service.messageList = null;
    service.childMessageUpdated(123);
    expect(service.childMessageUpdated).toBeTruthy();
  });

  it('should not update anything when baseId is not found in messageList', () => {
    const messages: any = [];
    service.messageList = messages;
    service.childMessageUpdated(123);
    expect(service.childMessageUpdated).toBeTruthy();
  });

  it('should update messageList and related properties', () => {
    const messages: any = [{ chatroomid: 123, maskedReplyMessages: [{ unreadCount: 2 }, { unreadCount: 3 }], unreadCountMasked: 5 }];
    const res = { message: [{ unreadCount: 3 }, { unreadCount: 4 }] };
    spyOn(service, 'fetchReplyMessagesofMasked').and.returnValue(of(res));
    service.messageList = messages;
    service.childMessageUpdated(123);
    expect(service.childMessageUpdated).toBeTruthy();
  });
  it('should call updateChatRoomId and navigate to the correct URL when chatRoomId is present', () => {
    const response = { chatRoomId: 123 };
    spyOn(service, 'updateChatRoomId');

    service.nextAction(response);

    expect(service.updateChatRoomId).toHaveBeenCalledWith(service.roomID, response.chatRoomId);
  });

  it('should not call updateChatRoomId or navigate when chatRoomId is not present', () => {
    const response = { message: 'Test message' };
    spyOn(service, 'updateChatRoomId');

    service.nextAction(response);

    expect(service.updateChatRoomId).not.toHaveBeenCalled();
  });
  it('should set idle and timeout values and start watching when userData is present', () => {
    service.userData = { userId: 1 } as any; // Mock userData
    spyOn(service.idle, 'setInterrupts');
    spyOn(service.idle, 'setIdle');
    spyOn(service.idle, 'setTimeout');
    spyOn(service.idle, 'watch');

    service.watchIdle(5, 10);

    expect(service.idle.setInterrupts).toHaveBeenCalledWith(DEFAULT_INTERRUPTSOURCES);
    expect(service.idle.setIdle).toHaveBeenCalledWith(5);
    expect(service.idle.setTimeout).toHaveBeenCalledWith(10);
    expect(service.idle.watch).toHaveBeenCalled();
  });

  it('should call resetSessionTimeout when userData is not present', () => {
    service.userData = null; // No userData
    spyOn(service, 'resetSessionTimeout');

    service.watchIdle(5, 10);

    expect(service.resetSessionTimeout).toHaveBeenCalledWith(false);
  });
  it('should return true if the site config value is Constants.configTrue', () => {
    service.userData = { siteConfigs: { testConfig: Constants.configTrue } } as any;
    const result = service.isEnableSiteConfig('testConfig');
    expect(result).toBeTrue();
  });

  it('should return false if the site config value is not Constants.configTrue', () => {
    service.userData = { siteConfigs: { testConfig: Constants.configFalse } } as any;
    const result = service.isEnableSiteConfig('testConfig');
    expect(result).toBeFalse();
  });

  it('should return false if the site config value is undefined', () => {
    service.userData = { siteConfigs: {} } as any;
    const result = service.isEnableSiteConfig('testConfig');
    expect(result).toBeFalse();
  });

  it('should return false if userData is undefined', () => {
    service.userData = undefined;
    const result = service.isEnableSiteConfig('testConfig');
    expect(result).toBeFalse();
  });
  it('should subscribe to form polling if form center is enabled', () => {
    spyOn(service, 'isEnableConfig').and.returnValue(true);
    spyOn(service.socketService, 'subscribeEvent').and.returnValue(of({}));
    spyOn(service, 'getMessageFormCounts');

    service.enableFormPolling();

    expect(service.isEnableConfig).toHaveBeenCalledWith(Config.enableFormCenter);
    expect(service.socketService.subscribeEvent).toHaveBeenCalledWith(Socket.formPolling);
    expect(service.getMessageFormCounts).toHaveBeenCalledWith(Constants.countTypes.forms, true);
  });

  it('should not subscribe to form polling if form center is not enabled', () => {
    spyOn(service, 'isEnableConfig').and.returnValue(false);
    spyOn(service.socketService, 'subscribeEvent');
    spyOn(service, 'getMessageFormCounts');

    service.enableFormPolling();

    expect(service.isEnableConfig).toHaveBeenCalledWith(Config.enableFormCenter);
    expect(service.socketService.subscribeEvent).not.toHaveBeenCalled();
    expect(service.getMessageFormCounts).not.toHaveBeenCalled();
  });
  describe('cancelDocumentOrForm', () => {
    it('should return a reason string when both alerts are confirmed', fakeAsync(() => {
      // Arrange
      const documentName = 'Test Document';
      const type = 'DOCUMENTS';
      const tab = 'PENDING';
      const expectedReason = 'Some cancellation reason';
      spyOn(common, 'getTranslateData').and.returnValue('Translated Text');
      spyOn(common, 'getTranslateDataWithParam').and.returnValue('Translated Message with Param');
      const firstAlertResponse = { 'INPUT.REASON_FOR_CANCEL': expectedReason };
      const secondAlertResponse = true;
      spyOn(common, 'showAlert').and.callFake((data) => {
        return data.alertId === 'cancel-reason-alert' ? Promise.resolve(firstAlertResponse) : Promise.resolve(secondAlertResponse);
      });
      // Act
      let result;
      service.cancelDocumentOrForm(type, documentName, tab).then((res) => {
        result = res;
      });
      tick();
      // Assert
      expect(result).toBe(expectedReason);
      expect(common.showAlert).toHaveBeenCalledTimes(2);
    }));

    it('should return empty string when first alert is cancelled', fakeAsync(() => {
      // Arrange
      const documentName = 'Test Document';
      const type = 'FORMS';
      const tab = 'PENDING';
      spyOn(common, 'getTranslateData').and.returnValue('Translated Text');
      spyOn(common, 'getTranslateDataWithParam').and.returnValue('Translated Message with Param');
      spyOn(common, 'showAlert').and.resolveTo(null);
      // Act
      let result;
      service.cancelDocumentOrForm(type, documentName, tab).then((res) => {
        result = res;
      });
      tick();
      // Assert
      expect(result).toBe('');
      expect(common.showAlert).toHaveBeenCalledTimes(1);
    }));

    it('should return empty string when reason is provided but second confirmation is cancelled', fakeAsync(() => {
      // Arrange
      const documentName = 'Test Document';
      const type = 'FORMS';
      const tab = 'PENDING';
      spyOn(common, 'getTranslateData').and.returnValue('Translated Text');
      spyOn(common, 'getTranslateDataWithParam').and.returnValue('Translated Message with Param');
      spyOn(common, 'showAlert').and.callFake((data) => {
        return data.alertId === 'cancel-reason-alert' ? Promise.resolve({ 'INPUT.REASON_FOR_CANCEL': 'Some reason' }) : Promise.resolve(false);
      });
      // Act
      let result;
      service.cancelDocumentOrForm(type, documentName, tab).then((res) => {
        result = res;
      });
      tick();
      // Assert
      expect(result).toBe('');
      expect(common.showAlert).toHaveBeenCalledTimes(2);
    }));

    it('should use correct message for document cancellation', fakeAsync(() => {
      const documentName = 'Test Document';
      const type = 'DOCUMENTS';
      const tab = 'PENDING';
      let alertDataCaptured;
      spyOn(common, 'getTranslateData').and.returnValue('Translated Text');
      spyOn(common, 'getTranslateDataWithParam').and.callFake((key, params) => {
        return `${key} with ${JSON.stringify(params)}`;
      });
      spyOn(common, 'showAlert').and.callFake((data) => {
        alertDataCaptured = data;
        return Promise.resolve(null);
      });
      service.cancelDocumentOrForm(type, documentName, tab);
      tick();
      expect(alertDataCaptured.alertId).toBe('cancel-reason-alert');
    }));

    it('should use correct message for form cancellation', fakeAsync(() => {
      const documentName = 'Test Form';
      const type = 'FORMS';
      const tab = 'PENDING';
      let alertDataCaptured;
      spyOn(common, 'getTranslateData').and.returnValue('Translated Text');
      spyOn(common, 'getTranslateDataWithParam').and.callFake((key, params) => {
        return `${key} with ${JSON.stringify(params)}`;
      });
      spyOn(common, 'showAlert').and.callFake((data) => {
        alertDataCaptured = data;
        return Promise.resolve(null);
      });
      service.cancelDocumentOrForm(type, documentName, tab);
      tick();
      expect(alertDataCaptured.alertId).toBe('cancel-reason-alert');
    }));
  });
});

// Push notification tests
describe('Push Notification Tests', () => {
  let service: SharedService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateTestingModule],
      providers: [
        SharedService,
        HttpService,
        SocketService,
        InAppBrowser,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        ModalController,
        PopoverController,
        Keepalive,
        OktaService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    });

    // Reset spies before each test
    jasmine.getEnv().allowRespy(true);

    // Initialize the service after TestBed configuration
    service = TestBed.inject(SharedService);

    // Spy on methods after the service is initialized
    spyOn(console, 'log');
    spyOn(service, 'updateChatRoomId').and.callThrough();
    spyOn(service, 'addRouteLink').and.callThrough();
  });

  it('should append the Google Maps script if it does not already exist', () => {
    spyOn(document, 'querySelector').and.returnValue(null); // Simulate no existing script
    const appendChildSpy = spyOn(document.head, 'appendChild');
    service.loadGoogleMap();
    expect(document.querySelector).toHaveBeenCalledWith('script[src*="maps.google.com/maps/api/js"]');
    expect(appendChildSpy).toHaveBeenCalled();
    const scriptElement = appendChildSpy.calls.mostRecent().args[0] as HTMLScriptElement;
    expect(scriptElement.src).toContain('https://maps.google.com/maps/api/js?key=');
    expect(scriptElement.async).toBeTrue();
    expect(scriptElement.defer).toBeTrue();
  });

  it('should not append the Google Maps script if it already exists', () => {
    const mockScript = document.createElement('script');
    spyOn(document, 'querySelector').and.returnValue(mockScript); // Simulate existing script
    const appendChildSpy = spyOn(document.head, 'appendChild');
    service.loadGoogleMap();
    expect(document.querySelector).toHaveBeenCalledWith('script[src*="maps.google.com/maps/api/js"]');
    expect(appendChildSpy).not.toHaveBeenCalled();
  });

  it('should handle push notification payloads with and without user-data', fakeAsync(() => {
    const mockNotification = {
      actionId: '1',
      notification: {
        id: 'mockNotificationId', // <-- Add this line
        data: {
          aps: {
            'user-data': {
              pushDeepLink: {
                activeMessage: {
                  chatroomid: 'mockChatroomId'
                }
              }
            }
          }
        }
      }
    };

    service.roomId = '123';
    service.handlePushNotificationAction(mockNotification);
    tick(); // Allow any async operations to complete
    expect(service.addRouteLink).toHaveBeenCalledWith(JSON.stringify(mockNotification.notification.data.aps['user-data']));
  }));
});
