import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ActionSheetController, IonicModule, ModalController, PopoverController } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { of, throwError } from 'rxjs';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { Apollo } from 'apollo-angular';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { formatDate } from '@angular/common';
import { ProfilePage } from './profile.page';
import { SignUpComponent } from '../../auth/sign-up/sign-up.component';

describe('ProfilePage', () => {
  let component: ProfilePage;
  let fixture: ComponentFixture<ProfilePage>;
  let sharedService: SharedService;
  let commonService: CommonService;
  let httpService: HttpService;
  let popoverController: PopoverController;
  let modalController: ModalController;
  let actionSheetController: ActionSheetController;
  let graphqlService: GraphqlService;
  const { popoverSpy, actionSheetSpy, modalSpy } = TestConstants;
  let router: Router;
  const userUpdateForm = new UntypedFormGroup({
    displayName: new UntypedFormControl(),
    firstName: new UntypedFormControl(),
    lastName: new UntypedFormControl(),
    phone: new UntypedFormControl(),
    zip: new UntypedFormControl(),
    dobDate: new UntypedFormControl()
  });
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ProfilePage],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        ModalController,
        HttpService,
        PopoverController,
        ActionSheetController,
        CommonService,
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        Apollo,
        GraphqlService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    sharedService = TestBed.inject(SharedService);
    graphqlService = TestBed.inject(GraphqlService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    sharedService.userData.group = '3';
    popoverController = TestBed.inject(PopoverController);
    modalController = TestBed.inject(ModalController);
    actionSheetController = TestBed.inject(ActionSheetController);
    commonService = TestBed.inject(CommonService);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popoverSpy;
    });
    popoverSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    spyOn(actionSheetController, 'create').and.callFake(() => {
      return actionSheetSpy;
    });
    actionSheetSpy.present.and.stub();
    spyOn(actionSheetController, 'dismiss').and.stub();
    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    modalSpy.onWillDismiss.and.resolveTo({});
    spyOn(commonService, 'showMessage').and.stub();
    spyOn(sharedService, 'trackActivity').and.stub();
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    Object.defineProperty(sharedService, 'configValuesUpdated', { value: of({}) });
    fixture = TestBed.createComponent(ProfilePage);
    component = fixture.componentInstance;
    component.patientGroupId = '3';
    component.patientNameDisplay = '1';
    component.countryId = '1';
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute presentCountryPopover', () => {
    popoverSpy.onDidDismiss.and.resolveTo({
      data: {
        dialCode: '1',
        code: 'abcd'
      }
    });
    component.presentCountryPopover({});
    expect(component.presentCountryPopover).toBeTruthy();
  });
  it('execute presentChooseNotificationsModal', () => {
    component.presentChooseNotificationsModal();
    expect(component.presentChooseNotificationsModal).toBeTruthy();
  });
  it('execute chooseOptionsForUpload', () => {
    component.chooseOptionsForUpload();
    expect(component.chooseOptionsForUpload).toBeTruthy();
  });
  it('execute deletePhotoModal', fakeAsync(() => {
    const response = { status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    component.deletePhotoModal();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.deletePhotoModal).toBeTruthy();
  }));
  it('execute loadImage : Invalid type', () => {
    const data = {
      target: {
        files: [
          {
            name: '',
            size: 32
          }
        ]
      }
    };
    component.loadImage(data);
    expect(component.loadImage).toBeTruthy();
  });
  it('execute loadImage: Size exceeded', () => {
    const data = {
      target: {
        files: [
          {
            name: 'image.png',
            size: 10485761
          }
        ]
      }
    };
    component.loadImage(data);
    expect(component.loadImage).toBeTruthy();
  });
  it('execute loadImage', () => {
    const data = {
      target: {
        files: [
          {
            name: 'image.png',
            size: 32
          }
        ]
      }
    };
    spyOn(httpService, 'fileUpload').and.returnValue(of('abc_sds'));
    component.loadImage(data);
    expect(component.loadImage).toBeTruthy();
  });
  it('execute loadImage: Upload error', () => {
    const data = {
      target: {
        files: [
          {
            name: 'image.png',
            size: 32
          }
        ]
      }
    };
    spyOn(httpService, 'fileUpload').and.returnValue(throwError(''));
    component.loadImage(data);
    expect(component.loadImage).toBeTruthy();
  });
  it('execute updateUserDetails', () => {
    const response = {
      data: {
        updateUser: {
          updateStatus: 'success'
        }
      }
    };
    component.displayNameFieldStatus = true;
    const data = {
      displayName: 'ds',
      firstName: 'df',
      name: '',
      lastName: 'ff',
      phone: '4353',
      zip: '43',
      dobDate: '3/4/1992'
    };
    component.userUpdateForm = userUpdateForm;
    component.userUpdateForm.patchValue(data);
    spyOn(graphqlService, 'updateUser').and.returnValue(of(response));
    component.updateUserDetails();
    fixture.detectChanges();
    expect(component.updateUserDetails).toBeTruthy();
  });
  it('execute updateUserDetails: update failed', () => {
    const response = {
      data: {
        updateUser: {
          updateStatus: 'failed'
        }
      }
    };
    const value = { displayName: 'ds', firstName: 'df', lastName: 'ff', phone: '4353', zip: '43', dobDate: '3/4/1992' };
    component.displayNameFieldStatus = true;
    component.userUpdateForm = userUpdateForm;
    component.userUpdateForm.patchValue(value);
    spyOn(graphqlService, 'updateUser').and.returnValue(of(response));
    component.updateUserDetails();
    fixture.detectChanges();
    expect(component.updateUserDetails).toBeTruthy();
  });

  it('execute updateUserDetails: throw error', () => {
    const value = { displayName: 'ds', firstName: 'df', lastName: 'ff', phone: '4353', zip: '43', dobDate: '3/4/1992' };
    component.displayNameFieldStatus = true;
    component.userUpdateForm = userUpdateForm;
    component.userUpdateForm.patchValue(value);
    spyOn(graphqlService, 'updateUser').and.returnValue(throwError(''));
    component.updateUserDetails();
    fixture.detectChanges();
    expect(component.updateUserDetails).toBeTruthy();
  });

  it('execute validateForm: Invalid displayName', () => {
    const value = { displayName: '', firstName: 'df', lastName: 'ff', phone: '4353', zip: '43', dobDate: '3/4/1992' };
    component.displayNameFieldStatus = true;
    component.userUpdateForm = userUpdateForm;
    component.userUpdateForm.patchValue(value);
    component.validateForm();
    expect(component.validateForm).toBeTruthy();
  });
  it('execute validateForm: Invalid firstName', () => {
    const value = { displayName: 'fwef', firstName: '', lastName: 'ff', phone: '4353', zip: '43', dobDate: '3/4/1992' };
    component.displayNameFieldStatus = true;
    component.userUpdateForm = userUpdateForm;
    component.userUpdateForm.patchValue(value);
    component.validateForm();
    fixture.detectChanges();
    expect(component.validateForm).toBeTruthy();
  });
  it('execute validateForm: Invalid lastName', () => {
    const value = { displayName: 'fwef', firstName: 're', lastName: '', phone: '4353', zip: '43', dobDate: '3/4/1992' };
    component.displayNameFieldStatus = true;
    component.userUpdateForm = userUpdateForm;
    component.userUpdateForm.patchValue(value);
    component.validateForm();
    fixture.detectChanges();
    expect(component.validateForm).toBeTruthy();
  });

  it('execute presentSignatureModal', fakeAsync(() => {
    component.isiPad = false;
    modalSpy.onDidDismiss.and.resolveTo({ data: {} });
    spyOn(sharedService.platform, 'is').and.returnValue(true);
    component.presentSignatureModal();
    tick(2000);
    expect(component.presentSignatureModal).toBeTruthy();
  }));
  it('should show signature info', () => {
    spyOn(commonService, 'showToast').and.stub();
    component.showSignatureInfo();
    expect(commonService.showToast).toHaveBeenCalledWith({
      message: commonService.getTranslateData('MESSAGES.MANAGE_SIGNATURE_INFO'),
      color: 'dark',
      duration: 4000
    });
  });
  it('should show toast message when user does not have permission to edit', () => {
    const event = new Event('click');
    const field = 'displayName';
    spyOn(commonService, 'showToast').and.stub();
    component.checkPermission(event, field);
    expect(commonService.showToast).toHaveBeenCalled();
  });

  it('execute validateForm: Invalid phone', () => {
    const value = { displayName: 'fwef', firstName: 're', lastName: 're', phone: '' };
    component.displayNameFieldStatus = true;
    component.userUpdateForm = userUpdateForm;
    component.userUpdateForm.patchValue(value);
    component.validateForm();
    fixture.detectChanges();
    expect(component.validateForm).toBeTruthy();
  });
  it('execute validateForm: controls Invalid phone', () => {
    const value = { displayName: 'dName', firstName: 'fName', lastName: 'lName', phone: 'aaa' };
    component.displayNameFieldStatus = true;
    component.userUpdateForm.patchValue(value);
    component.validateForm();
    fixture.detectChanges();
    expect(component.validateForm).toBeTruthy();
  });
  it('execute validateForm:  Invalid Zip and caregiver_userid', () => {
    const value = { displayName: 'dName', firstName: 'fName', lastName: 'lName', phone: '123456789', zip: '', caregiver_userid: '' };
    component.displayNameFieldStatus = false;
    component.userUpdateForm.patchValue(value);
    component.validateForm();
    fixture.detectChanges();
    expect(component.validateForm).toBeTruthy();
  });
  it('execute validateForm:  Invalid dobDate and caregiver_userid', () => {
    const value = { displayName: 'dName', firstName: 'fName', lastName: 'lName', phone: '123456789', zip: '123', caregiver_userid: '1', dobDate: '' };
    component.displayNameFieldStatus = false;
    component.userUpdateForm.patchValue(value);
    component.validateForm();
    fixture.detectChanges();
    expect(component.validateForm).toBeTruthy();
  });
  it('should set date value in the form control', () => {
    const mockEvent = { detail: { value: '2023-11-09' } };
    component.setDateValue(mockEvent);
    expect(component.userUpdateForm.controls.dobDate.value).toBe('11/09/2023');
  });
  it('should change date format', () => {
    component.changeDateFormat('2023-11-09', 'DD-MM-YYYY');
    expect(component.changeDateFormat).toBeTruthy();
  });
  it('should handle empty date value when changing format', () => {
    const result = component.changeDateFormat('', 'DD-MM-YYYY');
    expect(result).toBe('');
  });
  it('should execute createItem', () => {
    component.createItem([]);
    expect(component.createItem).toBeDefined();
  });
  it('execute ionViewDidEnter', () => {
    component.userInputElement = '' as any;
    Object.defineProperty(component.userInputViewChild, 'nativeElement' as any, { value: 'test' });
    component.ionViewDidEnter();
    expect(component.ionViewDidEnter).toBeTruthy();
  });

  it('should set date value correctly', () => {
    const event = { detail: { value: '2023-11-08' } };
    spyOn(component.userUpdateForm.controls['dobDate'], 'setValue').and.callThrough();
    component.setDateValue(event);
    const formattedDate = formatDate('2023-11-08', 'MM/dd/yyyy', 'en-US');
    expect(component.userUpdateForm.controls['dobDate'].setValue).toHaveBeenCalledWith(formattedDate);
  });

  it('should return formatted date', () => {
    const value = '2023-11-08';
    const format = 'YYYY-MM-DD';
    const result = component.changeDateFormat(value, format);
    expect(result).toEqual(value);
  });
  it('should return empty string if value is falsy', () => {
    const value = '';
    const format = 'YYYY-MM-DD';
    const result = component.changeDateFormat(value, format);
    expect(result).toEqual('');
  });
  it('execute filterSitesData', () => {
    const item = [123, 124];
    component.filterSitesData(item);
    expect(component.filterSitesData).toBeTruthy();
  });

  it('execute initialSiteData', () => {
    const item = [123, 124];
    component.initialSiteData(item);
    expect(component.filterSitesData).toBeTruthy();
  });
  describe('passwordMatchValidator', () => {
    let formGroup: UntypedFormGroup;

    beforeEach(() => {
      formGroup = new UntypedFormGroup({
        newPassword: new UntypedFormControl(''),
        confirmPassword: new UntypedFormControl('')
      });
    });

    it('should return null if passwords match', () => {
      formGroup.get('newPassword').setValue('password123');
      formGroup.get('confirmPassword').setValue('password123');
      const result = component.passwordMatchValidator(formGroup);
      expect(result).toBeNull();
      expect(formGroup.get('confirmPassword').errors).toBeNull();
    });

    it('should set mismatch error if passwords do not match', () => {
      formGroup.get('newPassword').setValue('password123');
      formGroup.get('confirmPassword').setValue('password456');
      const result = component.passwordMatchValidator(formGroup);
      expect(result).toEqual({ mismatch: true });
      expect(formGroup.get('confirmPassword').errors).toEqual({ mismatch: true });
    });
  });
  it('should call modalController.create with correct arguments', async () => {
    await component.deleteUserAccountModal();

    expect(modalController.create).toHaveBeenCalledWith({
      component: SignUpComponent,
      initialBreakpoint: 0.3,
      breakpoints: [0.3, 0.5, 1],
      componentProps: { isDeleteAccount: true }
    });
  });

  it('execute setSelectedSites', () => {
    const item = [123, 124];
    component.setSelectedSites(item);
    expect(component.setSelectedSites).toBeTruthy();
  });
});
