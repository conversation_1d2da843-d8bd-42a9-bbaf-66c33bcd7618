import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import {
  UntypedFormGroup,
  Validators,
  UntypedFormBuilder,
  UntypedFormArray,
  UntypedFormControl,
  FormGroup,
  ValidatorFn,
  AbstractControl
} from '@angular/forms';
import { HttpService } from 'src/app/services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { Activity } from 'src/app/constants/activity';
import { ActionSheetController, ModalController } from '@ionic/angular';
import { Country } from 'src/app/interfaces/common-interface';
import { isBlank, formatDate, isPresent, compareWithIsbefore, formatTime, convertTimeZoneDateTimeToUTCIso } from 'src/app/utils/utils';
import { environment } from 'src/environments/environment';
import { Urls } from 'src/app/constants/urls';
import { ChooseNotificationComponent } from 'src/app/pages/user/profile/choose-notification/choose-notification.component';
import { Config } from 'src/app/constants/config';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { LoginResponse } from 'src/app/interfaces/login';
import * as moment from 'moment';
import { SignatureComponent } from 'src/app/components/signature/signature.component';
import { Signature } from 'src/app/constants/signature';
import { SignUpComponent } from '../../auth/sign-up/sign-up.component';
import { IDateTime } from 'src/app/interfaces/schedule-center';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss']
})
export class ProfilePage implements OnInit {
  webNotificationHideBehaviour: boolean;
  staffEnableEmail: boolean;
  patientEnableEmail: boolean;
  staffmessageNotification: boolean;
  patientmessageNotification: boolean;
  @ViewChild('userInput') userInputViewChild: ElementRef;
  userInputElement: HTMLInputElement;
  userUpdateForm: UntypedFormGroup;
  resetPasswordForm: UntypedFormGroup;
  userDetails: LoginResponse;
  userProfileUpdateStatus: number;
  displayNameFieldStatus = true;
  countryId: string;
  defaultCountryId = Constants.defaultCountryId;
  errorImage = Urls.noImage;
  countryDetails: Country;
  isWeb: boolean;
  patientGroupId: string;
  patientNameDisplay: any;
  platformValue: any;
  file: any;
  imgName: any;
  selectedNotification;
  constants = Constants;
  maxDate = moment().format(Constants.dateFormat.yyyMMDDT);
  title = this.common.getTranslateData('BUTTONS.EDIT_PHOTO');
  private readonly apiRoot: string = environment.apiBase;
  isWorkFlowAlternate: boolean;
  selectedSiteIds = [];
  showSiteSelection = false;
  enableIDM = localStorage.getItem(Constants.storageKeys.oktaLogin) && this.sharedService.isEnableConfig(Config.enableIDM);
  isiPad = false;
  selectedSegment = 'profile-info';
  isShowPassword = false;
  enableSaveSignatureToProfile: boolean;
  encodedUserSignatureData: string;
  statusMessage: string;
  useSavedSignData: boolean;
  enableOutOfOfficeStatus: false;
  selectedPatientForAdmission: number;
  defaultTime: any;
  todayDateTime: IDateTime;
  dateFormat = Constants.dateFormat;
  controlNames = Constants.controlNames;
  maxYear: string;
  associatedUsers;
  constructor(
    public readonly sharedService: SharedService,
    private readonly httpService: HttpService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly resetPasswordformBuilder: UntypedFormBuilder,
    private readonly common: CommonService,
    private readonly modalController: ModalController,
    private readonly actionSheetController: ActionSheetController,
    private readonly graphqlService: GraphqlService
  ) {
    if (this.sharedService.platform.is('ipad') || this.sharedService.platform.is('tablet')) {
      this.isiPad = true;
    }
    this.todayDateTime = {
      date: formatDate(new Date(), Constants.dateFormat.ymd),
      time: formatDate(new Date(), this.dateFormat.hhmm0)
    };
    this.defaultTime = this.timeCalculator(`${this.todayDateTime.date} ${this.todayDateTime.time}`);
    this.checkConfigValues();
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.checkConfigValues();
    });
    this.userDetails = this.sharedService.userData;
    this.isWorkFlowAlternate = this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact;
    this.associatedUsers = this.isWorkFlowAlternate ? this.userDetails?.alternate_contact_patient : this.userDetails.associated_user;
    if (this.sharedService.showAdmissionTabInProfile) {
      this.selectedPatientForAdmission = isPresent(this.associatedUsers) ? this.associatedUsers[0].patientId : this.userDetails.userId;
    }
    const controls = this.associatedUsers?.map((value: any) => this.createItem(value)) || [];
    this.countryId = this.userDetails?.countryCode ? this.userDetails.countryCode : this.defaultCountryId;
    this.encodedUserSignatureData = isPresent(this.userDetails?.savedUserSignature) ? `${Signature.signReplaceValue}${this.userDetails?.savedUserSignature}` : '';
    this.useSavedSignData = this.userDetails.useSavedSign;
    this.enableOutOfOfficeStatus = isPresent(this.userDetails?.oooInfo?.isOutOfOffice)
      ? this.userDetails?.oooInfo.isOutOfOffice
      : false;
    this.statusMessage = isPresent(this.userDetails?.oooInfo?.message) ? this.userDetails?.oooInfo.message : '';
    const convertedStartDate = this.convertUtcToTimeZoneDateTime(this.userDetails?.oooInfo?.startDateTime);
    const convertedEndDate = this.convertUtcToTimeZoneDateTime(this.userDetails?.oooInfo?.endDateTime);
    this.userUpdateForm = this.formBuilder.group({
      displayName: [this.userDetails?.displayName, Validators.required],
      firstName: [this.userDetails?.firstName, Validators.required],
      lastName: [this.userDetails?.secondName, Validators.required],
      name: [this.userDetails?.username, Validators.pattern(Constants.validationPattern.emailPattern)],
      phone: [this.userDetails?.mobile, [Validators.pattern(Constants.validationPattern.phoneNumberPattern)]],
      enableSmsNotification: [this.userDetails?.enable_sms_notifications === '1'],
      enableAutoHideWebNotification: [this.userDetails?.enable_auto_hide_web_notifications === '1'],
      enableEmailNotification: [this.userDetails?.enable_email_notifications === '1'],
      dobDate: [
        formatDate(this.userDetails?.dob, Constants.dateFormat.mdy),
        [Validators.required, Validators.pattern(Constants.validationPattern.dateOfBirthPattern)]
      ],
      zip: [this.userDetails?.zip],
      useSavedSign: [this.useSavedSignData],
      encodedUserSignature: [this.encodedUserSignatureData],
      enableOutOfOffice: [this.enableOutOfOfficeStatus],
      statusMessage: [isPresent(this.statusMessage) || '', [Validators.maxLength(180)]],
      startDate: [isPresent(convertedStartDate.date) ? convertedStartDate.date : null],
      startTime: [isPresent(convertedStartDate.time) ? convertedStartDate.time : null],
      endDate: [isPresent(convertedEndDate.date) ? convertedEndDate.date : null],
      endTime: [isPresent(convertedEndDate.time) ? convertedEndDate.time : null],
      associatedUser: new UntypedFormArray(controls)
    });
    this.resetPasswordForm = this.resetPasswordformBuilder.group(
      {
        currentPassword: ['', [Validators.required]],
        newPassword: ['', [Validators.required, this.passwordValidator()]],
        confirmPassword: ['', [Validators.required]]
      },
      { validators: this.passwordMatchValidator }
    );
  }
  convertUtcToTimeZoneDateTime(dateTime: string): { date: string, time: string } {
    const timeZone = moment.tz.guess(); 
    if (dateTime && timeZone) {
      const localTime = moment.utc(dateTime).tz(timeZone);
      const date = localTime.format(this.dateFormat.mdy);
      const time = localTime.format(this.dateFormat.h2ma);
      return { date, time };
    }
    return { date: '', time: '' };
  }
  timeCalculator(time: string): string {
    const selectMinute = moment(time).minute();
    const selectHour = moment(time).hour();
    let dateTime;
    if (selectMinute >= 30) {
      dateTime = new Date(`${moment(time).format(Constants.dateFormat.mdy)} ${selectHour}:30`);
    } else {
      dateTime = new Date(`${moment(time).format(Constants.dateFormat.mdy)} ${selectHour}:00`);
    }
    return dateTime;
  }

  clearStatusMessage() {
    this.userUpdateForm.get('statusMessage')?.setValue('');
  }

  clearDateTime(key: string) {
    if (key === 'start') {
      this.userUpdateForm.get('startDate')?.setValue('');
      this.userUpdateForm.get('startTime')?.setValue('');
    } else {
      this.userUpdateForm.get('endDate')?.setValue('');
      this.userUpdateForm.get('endTime')?.setValue('');
    }
  }

  compareEndDateTimeWithStart(): boolean {
    return compareWithIsbefore(
      [this.userUpdateForm.value.startDate, this.userUpdateForm.value.startTime].join(' '),
      [this.userUpdateForm.value.endDate, this.userUpdateForm.value.endTime].join(' ')
    );
  }

  setStartEndTime(data: string | string[], key?): any {
    if (data) {
      const start = formatTime(data, Constants.dateFormat.hhmm0, this.dateFormat.h2ma);
      if (key) {
        this.userUpdateForm.patchValue({
          [key]: start
        });
        this.validateEndTimeErrors();
      }
      return data;
    }
    return '00:00:00';
  }

  changeStartEndDateFormat(value: string | string[], format: string, key?): string {
    if (isBlank(value)) {
      return formatDate(this.defaultTime, format);
    }
    if (key) {
      const date = formatDate(value, this.dateFormat.mdy);
      let controlName = null;
      if (key === this.controlNames.startDate) {
        controlName = 'startDate';
      } else if (key === this.controlNames.endDate) {
        controlName = 'endDate';
      }
      if (controlName) {
        this.userUpdateForm.patchValue({ [controlName]: date });
      }
      this.validateEndTimeErrors();
    }

    return formatDate(value, format);
  }

  validateEndTimeErrors(): void {
    if (!this.compareEndDateTimeWithStart() && isPresent(this.userUpdateForm.value.startTime) && isPresent(this.userUpdateForm.value.endTime)) {
      this.userUpdateForm.controls.endTime.setErrors({ incorrect: true });
    } else {
      this.userUpdateForm.controls.endTime.setErrors(null);
    }
  }

  passwordMatchValidator(formGroup: FormGroup) {
    const newPassword = formGroup.get('newPassword');
    const confirmPassword = formGroup.get('confirmPassword');
    if (newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ mismatch: true });
      return { mismatch: true };
    }
    confirmPassword.setErrors(null);
    return null;
  }

  resetPassword(): void {
    if (this.resetPasswordForm.valid) {
      const payload = {
        password: this.resetPasswordForm.value.newPassword,
        oldPassword: this.resetPasswordForm.value.currentPassword,
        userId: this.sharedService.userData.userId,
        tenantId: this.sharedService.userData.tenantId,
        tenantKey: this.sharedService.userData.tenantKey,
        username: this.sharedService.userData.userName
      };
      this.httpService.doPost({ endpoint: APIs.resetPassword, payload, hasOktaAuthToken: true }).subscribe((response) => {
        // TODO: Update success response check after correcting api response
        if (response.status === 0) {
          this.sharedService.trackActivity({
            type: Activity.userAccess,
            name: Activity.resetPassword,
            des: {
              data: {
                userName: this.sharedService.userData.displayName,
                userId: this.sharedService.userData.userId
              },
              desConstant: Activity.resetPasswordDes
            }
          });
          const resetSuccessMessage = this.common.getTranslateData('SUCCESS_MESSAGES.OKTA_PASSWORD_RESET_SUCCESSFUL');
          this.common.showMessage(resetSuccessMessage);
          this.resetPasswordForm.reset({ currentPassword: '', newPassword: '', confirmPassword: '' });
          this.sharedService.logout();
        } else {
          const resetFailureMessage = this.common.getTranslateData(response.errorMessage);
          this.common.showMessage(resetFailureMessage);
        }
      });
    }
  }

  getErrorText(fieldName: string): string {
    const control = this.resetPasswordForm.get(fieldName);
    if (control?.hasError('required')) {
      return this.common.getTranslateData('ERROR_MESSAGES.REQUIRED_TEXT');
    }
    if (this.resetPasswordForm.errors?.mismatch && fieldName === 'confirmPassword') {
      this.resetPasswordForm.get('confirmPassword')?.setErrors({ mismatch: true });
      return this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_MISMATCH');
    }
    if (control?.hasError('passwordRequirements')) {
      return this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_REQUIREMENTS');
    }
    return '';
  }

  passwordValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const { value } = control;
      const { username, firstName, secondName: lastName } = this.userDetails;

      const lowerValue = value?.toLowerCase();
      const usernameParts = username?.split(Constants.validationPattern.usernameDelimiters)?.map((part) => part?.toLowerCase());
      const containsUsername = usernameParts?.some((part) => lowerValue?.includes(part));
      const containsFirstName = lowerValue.includes(firstName?.toLowerCase());
      const containsLastName = lowerValue.includes(lastName?.toLowerCase());
      const isValidPassword = Constants.validationPattern.oktaPasswordPattern.test(value);

      const isValid =
        isValidPassword &&
        !containsUsername &&
        !containsFirstName &&
        !containsLastName;

      return isValid ? null : { passwordRequirements: true };
    };
  }

  showHidePassword(event: any, inputId: string): void {
    event.preventDefault();
    const inputElement = document.getElementById(inputId) as HTMLInputElement;
    if (inputElement) {
      inputElement.type = inputElement.type === 'password' ? 'text' : 'password';
      const iconName = inputElement.type === 'password' ? 'eye-off' : 'eye';
      const iconElement = event.target;
      if (iconElement.tagName.toLowerCase() === 'ion-icon') {
        iconElement.setAttribute('name', iconName);
      }
    }
  }

  setDateValue(event: any) {
    this.userUpdateForm.controls.dobDate.setValue(formatDate(event.detail.value, Constants.dateFormat.mdy));
  }

  changeDateFormat(value: string, format: string): string {
    return value ? formatDate(value, format) : '';
  }

  createItem(controlValue: any): UntypedFormGroup {
    return this.formBuilder.group({
      displayName: new UntypedFormControl({
        value: controlValue.displayname,
        disabled: true
      }),
      firstName: new UntypedFormControl({
        value: this.isWorkFlowAlternate ? controlValue.firstName : controlValue.firstname,
        disabled: true
      }),
      lastName: new UntypedFormControl({
        value: this.isWorkFlowAlternate ? controlValue.lastName : controlValue.lastname,
        disabled: true
      }),
      name: new UntypedFormControl({
        value: controlValue.name,
        disabled: true
      }),
      email: new UntypedFormControl({
        value: this.isWorkFlowAlternate ? controlValue.email : controlValue.username,
        disabled: true
      }),
      mobile: new UntypedFormControl({
        value: controlValue.mobile,
        disabled: true
      }),
      dob: new UntypedFormControl({
        value: formatDate(controlValue.dob, Constants.dateFormat.mdy),
        disabled: true
      }),
      relation: new UntypedFormControl({
        value: this.isWorkFlowAlternate ? controlValue.relation : '',
        disabled: true
      })
    });
  }
  ngOnInit(): void {
    this.countryDetails = this.sharedService.getCountryDetails(this.countryId, this.sharedService.userData.countryIsoCode);
    this.countryId = this.countryDetails?.dialCode || Constants.defaultCountryId;
    this.userProfileUpdateStatus = Constants.userProfileUpdateStatus;
    this.patientGroupId = String(Constants.patientGroupId);
    this.patientNameDisplay = Constants.patientnameDisplay;
    this.platformValue = Constants.platform.web;
    this.isWeb = this.sharedService.platformValue === this.platformValue;
    if (
      this.userDetails.group === this.patientGroupId &&
      this.sharedService.getConfigValue(Config.patientNameDisplay) === String(this.patientNameDisplay)
    ) {
      this.displayNameFieldStatus = false;
    }

    this.showSiteSelection =
      this.sharedService.isEnableConfig(Config.enableMultiSite) &&
      this.userDetails.group !== this.patientGroupId &&
      this.userDetails.mySites.length > 1;
    this.maxYear = this.sharedService.setCalendarPickerMaxYear();
  }

  async deleteUserAccountModal() {
    const modal = await this.modalController.create({
      component: SignUpComponent,
      initialBreakpoint: 0.3,
      breakpoints: [0.3, 0.5, 1],
      componentProps: { isDeleteAccount: true }
    });
    modal.present();
  }

  checkConfigValues(): void {
    this.webNotificationHideBehaviour = this.sharedService.isEnableConfig(Config.webNotificationHideBehaviour);
    this.patientmessageNotification = this.sharedService.isEnableConfig(Config.patientMessageNotification);
    this.staffmessageNotification = this.sharedService.isEnableConfig(Config.staffMsgSmsNotification);
    this.patientEnableEmail = this.sharedService.isEnableConfig(Config.enableEmailPatient);
    this.staffEnableEmail = this.sharedService.isEnableConfig(Config.enableEmailStaff);
    this.enableSaveSignatureToProfile = this.sharedService.isEnableConfig(Config.enableSaveSignatureToProfile);
  }
  presentCountryPopover(ev: any): void {
    this.countryDetails = this.sharedService.selectedCountry;
    this.sharedService.presentCountryPopover(ev, (callback: Country) => {
      this.countryDetails = callback;
      this.countryId = this.countryDetails.dialCode;
    });
  }
  async presentSignatureModal(): Promise<void> {
    if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
      this.sharedService.lockDevice(Constants.deviceOrientation.landscapePrimary);
    }
    const modal = await this.modalController.create({
      component: SignatureComponent
    });
    modal.onDidDismiss().then(({ data }) => {
      if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
        this.sharedService.unlockDevice();
        if (this.sharedService.platform.is('ios')) {
          this.sharedService.lockDevice(Constants.deviceOrientation.portrait);
          this.sharedService.unlockDevice();
        }
      }
      if (data) {
        this.encodedUserSignatureData = data.imageData;
      }
    });
    return modal.present();
  }
  showSignatureInfo(): void {
    this.common.showToast({
      message: this.common.getTranslateData('MESSAGES.MANAGE_SIGNATURE_INFO'),
      color: 'dark',
      duration: 4000
    });
  }
  checkPermission(event: Event, field: string) {
    if (this.userDetails.group === this.patientGroupId) {
      event.preventDefault();
      this.common.showToast({
        message: `${this.common.getTranslateData('MESSAGES.CHECK_PERMISSION')} ${this.common.getTranslateData(field)}.`,
        color: 'dark'
      });
    }
  }

  async presentChooseNotificationsModal(): Promise<void> {
    const modal = await this.modalController.create({
      component: ChooseNotificationComponent,
      componentProps: {
        selectedNotification: this.selectedNotification
      },
      backdropDismiss: false
    });
    modal.onWillDismiss().then(({ data }) => {
      this.selectedNotification = data.selectedNotification;
    });
    return modal.present();
  }

  async chooseOptionsForUpload(): Promise<void> {
    const chooseButtons = [
      {
        text: this.common.getTranslateData('OPTIONS.DELETE_PHOTO'),
        handler: () => {
          this.deletePhotoModal();
        }
      },
      {
        text: this.common.getTranslateData('OPTIONS.CHOOSE_PHOTO'),
        handler: () => {
          this.userInputElement.click();
        }
      },
      {
        text: this.common.getTranslateData('BUTTONS.CANCEL'),
        role: 'cancel'
      }
    ];
    const actionSheet = await this.actionSheetController.create({
      mode: 'ios',
      buttons: this.userDetails.avatar ? chooseButtons : chooseButtons.slice(1, 3)
    });
    await actionSheet.present();
  }
  deletePhotoModal(): void {
    this.common
      .showAlert({
        message: 'MESSAGES.DELETE_PHOTO',
        header: 'MESSAGES.ARE_YOU_SURE'
      })
      .then((confirmation) => {
        if (confirmation) {
          this.httpService
            .doPost({
              endpoint: `${APIs.deleteAvatar}`,
              payload: { userId: this.userDetails.userId }
            })
            .subscribe((res) => {
              const profilePicByRole = this.userDetails.group === this.patientGroupId ? Constants.patientImage : Constants.otherUserImage;
              if (res.status === 1) {
                const deleteSuccessMessage = this.common.getTranslateData('SUCCESS_MESSAGES.DELETE_PHOTO_SUCCESS_MESSAGE');
                this.common.showMessage(deleteSuccessMessage);
                this.userDetails.profileImageUrl = `${environment.apiServer}${Urls.profilePicPath}${Constants.profilePic}-${profilePicByRole}.png`;
                this.userDetails.profileImageThumbUrl = `${environment.apiServer}${Urls.profilePicPath}${Constants.profilePic}-${profilePicByRole}.png`;
                this.userDetails.avatar = '';
                this.sharedService.resetSessionProfileData(this.userDetails);
                const data = {
                  displayName: this.userDetails.displayName
                };
                this.sharedService.trackActivity({
                  type: Activity.manageUser,
                  name: Activity.deleteProfilePhoto,
                  des: {
                    data,
                    desConstant: Activity.deleteProfilePhotoDescription
                  }
                });
              }
            });
        }
      });
  }
  loadImage(data: any): void {
    this.file = data.target.files;
    const fileName = this.file[0].name;
    const fileSize = this.file[0].size;
    const getFileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    const pos = Constants.allowedImageFileTypes.indexOf(getFileExt);
    if (pos < 0) {
      const uploadErrorMessage = this.common.getTranslateData('ERROR_MESSAGES.PROFILE_FILE');
      this.common.showMessage(uploadErrorMessage);
      const desData = {
        userName: this.userDetails.userName,
        fileExtension: getFileExt
      };
      this.sharedService.trackActivity({
        type: Activity.manageUser,
        name: Activity.updateProfilePhotoFailed,
        des: {
          data: desData,
          desConstant: Activity.updateProfilePhotoFailedDes
        }
      });
    } else if (fileSize > Constants.profileImageMaxSize) {
      const sizeErrorMessage = this.common.getTranslateDataWithParam('ERROR_MESSAGES.PROFILE_IMAGE_UPLOAD', {
        maxFileSize: Constants.defaultLimit
      });
      this.common.showMessage(sizeErrorMessage);
    } else {
      this.httpService
        .fileUpload({
          endpoint: `${APIs.profileImageUpload}?userId=${this.userDetails.userId}`,
          payload: this.file,
          field: 'myfile',
          id: this.userDetails.userId,
          page: 'profile',
          responseType: 'text'
        })
        .subscribe(
          (res) => {
            this.imgName = res.split('_');
            this.userDetails.profileImageUrl = `${environment.apiServer}${Urls.profilePicPath}${this.imgName[0]}`;
            this.userDetails.profileImageThumbUrl = `${environment.apiServer}${Urls.profilePicPath}${Constants.profileThumbUrl}/${this.imgName[0]}`;
            this.userDetails.avatar = this.imgName[0];
            this.sharedService.resetSessionProfileData(this.userDetails);
            const desData = {
              userName: this.userDetails.userName
            };
            this.sharedService.trackActivity({
              type: Activity.manageUser,
              name: Activity.updateProfilePhoto,
              des: {
                data: desData,
                desConstant: Activity.updateProfilePhotoDes
              }
            });
          },
          (error) => {
            this.sharedService.errorHandler(error);
          }
        );
    }
  }
  updateUserDetails(): void {
    if (this.validateForm()) {
      const localSelectedSites = this.selectedSiteIds;
      const userProfileData = this.userUpdateForm.value;
      const startDateTime  = userProfileData?.startDate ? convertTimeZoneDateTimeToUTCIso(formatDate(userProfileData?.startDate, this.dateFormat.ymd),formatTime(userProfileData?.startTime, this.dateFormat.h2ma, this.dateFormat.hhmm0), moment.tz.guess(), '', '') : '';
      const endDateTime = userProfileData?.endDate ? convertTimeZoneDateTimeToUTCIso(formatDate(userProfileData?.endDate, this.dateFormat.ymd),formatTime(userProfileData?.endTime, this.dateFormat.h2ma, this.dateFormat.hhmm0), moment.tz.guess(), '', '') : '';
      const outOfOfficeInfo = {
        isOutOfOffice: userProfileData.enableOutOfOffice,
        message: userProfileData.statusMessage,
        startDateTime,
        endDateTime
      };
      let { displayName } = userProfileData;
      if (!this.displayNameFieldStatus) {
        displayName = `${userProfileData.firstName} ${userProfileData.lastName}`;
      }
      const encodedSignature = this.encodedUserSignatureData.replace(Signature.signReplaceValue, '');
      const payload = {
        displayName,
        firstName: userProfileData.firstName,
        lastName: userProfileData.lastName,
        mobile: userProfileData.phone ? String(userProfileData.phone) : '',
        countryCode: this.countryId,
        countryIsoCode: this.countryDetails.code,
        languages: this.userDetails.languages,
        enableAutoHideWebNotifications: userProfileData.enableAutoHideWebNotification ? 1 : 0,
        enableSmsNotifications: userProfileData.enableSmsNotification ? 1 : 0,
        enableEmailNotifications: userProfileData.enableEmailNotification ? 1 : 0,
        name: userProfileData.name,
        notificationSoundName: this.selectedNotification?.toLowerCase(),
        defaultSitesFilter: isBlank(localSelectedSites) ? '' : localSelectedSites.toString(),
        encodedUserSignature:
          encodedSignature === this.userDetails?.savedUserSignature && !isBlank(this.userDetails?.savedUserSignature) ? '' : encodedSignature,
        useSavedSign: this.userDetails.useSavedSign === userProfileData.useSavedSign ? undefined : userProfileData.useSavedSign,
        outOfOfficeInfo: outOfOfficeInfo
      };
      this.setSelectedSites(this.selectedSiteIds);
            this.sharedService.isLoading = true;
      this.graphqlService.updateUser(payload)?.subscribe(
        (result) => {
          this.sharedService.isLoading = false;
          if (
            isPresent(result.data) &&
            isPresent(result.data.updateUser) &&
            result.data.updateUser.updateStatus === Constants.success.toLowerCase()
          ) {
            const successAlertMesaage = this.common.getTranslateData('SUCCESS_MESSAGES.UPDATE_USER_DETAILS');
            const desData = {
              displayName: this.userDetails.displayName
            };
            this.sharedService.trackActivity({
              type: Activity.manageUser,
              name: Activity.updateUserProfile,
              des: {
                data: desData,
                desConstant: Activity.updateUserProfileDescription
              }
            });

            if(isPresent(result.data.updateUser.savedUserSignature)){
              this.userDetails.savedUserSignature = payload.encodedUserSignature;
            }
            if(isPresent(payload.useSavedSign)){
              this.userDetails.useSavedSign = payload.useSavedSign;
            }
            if(isPresent(payload.outOfOfficeInfo)){
              this.userDetails.oooInfo = payload.outOfOfficeInfo;
            }

            this.common.showMessage(successAlertMesaage);
            this.userDetails.firstName = payload.firstName;
            this.userDetails.secondName = payload.lastName;
            this.userDetails.displayName = payload.displayName;
            this.userDetails.mobile = payload.mobile;
            this.userDetails.countryCode = payload.countryCode;
            this.userDetails.username = payload.name;
            this.userDetails.enable_auto_hide_web_notifications = String(payload.enableAutoHideWebNotifications);
            this.userDetails.enable_sms_notifications = String(payload.enableSmsNotifications);
            this.userDetails.enable_email_notifications = String(payload.enableEmailNotifications);
            this.userDetails.notificationSoundName = payload.notificationSoundName;
            this.sharedService.resetSessionProfileData(this.userDetails);
          } else {
            const desData = {
              displayName: this.userDetails.displayName
            };
            this.sharedService.trackActivity({
              type: Activity.manageUser,
              name: Activity.updateUserProfileFailed,
              des: {
                data: desData,
                desConstant: Activity.updateUserProfileFailedDes
              }
            });
            const errorMessage = result.data?.updateUser?.updateStatus;
            const errorAlertMessage = `${this.common.getTranslateData('ERROR_MESSAGES.UPDATE_USER_DETAILS_FAILED_DUE_TO')}${errorMessage}`;
            this.common.showMessage(errorAlertMessage);
          }
        },
        () => {
          this.sharedService.isLoading = false;
        }
      );
    }
  }

  validateForm(): boolean {
    const alertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.REQUIRED_FIELDS');
    if (isBlank(this.userUpdateForm.value.displayName)) {
      this.common.showMessage(alertMessage);
      return false;
    }
    if (isBlank(this.userUpdateForm.value.firstName)) {
      this.common.showMessage(alertMessage);
      return false;
    }
    if (isBlank(this.userUpdateForm.value.lastName)) {
      this.common.showMessage(alertMessage);
      return false;
    }
    if (!this.userUpdateForm.controls.phone.valid) {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.PLEASE_ENTER_VALID_MOBILE_NUMBER'));
      return false;
    }
    if (!this.displayNameFieldStatus && isBlank(this.userUpdateForm.value.zip) && isBlank(this.userDetails.caregiver_userid)) {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.INVALID_ZIP'));
      return false;
    }
    if (
      !this.displayNameFieldStatus &&
      isBlank(this.userDetails.caregiver_userid) &&
      (isBlank(this.userUpdateForm.value.dobDate) || (this.userUpdateForm.controls.dobDate.value && !this.userUpdateForm.controls.dobDate.valid))
    ) {
      if (isBlank(this.userUpdateForm.value.dobDate)) {
        this.common.showMessage(alertMessage);
      }
      return false;
    }
    if (!this.selectedSiteIds.length && this.showSiteSelection) {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.SITE_REQUIRED'));
      return false;
    }
    if (
      (this.enableOutOfOfficeStatus || this.userUpdateForm.value.statusMessage) &&
      (this.userUpdateForm.controls.endTime.errors ||
        (isPresent(this.userUpdateForm.value.startDate) && isBlank(this.userUpdateForm.value.startTime)) ||
        (isPresent(this.userUpdateForm.value.endDate) && isBlank(this.userUpdateForm.value.endTime)))
    ) {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.OUT_OF_OFFICE_REQUIRED'));
      return false;
    }
    return true;
  }
  ionViewDidEnter(): void {
        this.userInputElement = this.userInputViewChild.nativeElement;
    this.selectedNotification = !isBlank(this.sharedService.userData.notificationSoundName)
      ? this.userDetails.notificationSoundName.charAt(0).toUpperCase() + this.userDetails.notificationSoundName.slice(1)
      : Constants.defaultNotificationSound;
  }

  filterSitesData(data: any): void {
    this.selectedSiteIds = data;
  }

  initialSiteData(data: any): void {
    this.selectedSiteIds = data;
  }

  setSelectedSites(siteIds: any): void {
    localStorage.setItem(Constants.storageKeys.selectedSites, JSON.stringify(siteIds));
  }
}
