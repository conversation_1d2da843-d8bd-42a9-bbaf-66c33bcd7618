import { ConfigValues } from 'src/assets/config/config';
import { VideoCallPermissionService } from 'src/app/services/videocall-permission/video-call-permission.service';
import { Component, OnInit, OnDestroy, ViewChild, Renderer2, ChangeDetectorRef, ElementRef } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalController, PopoverController, ActionSheetController, IonContent, IonAccordionGroup } from '@ionic/angular';
import { InviteComponent } from 'src/app/pages/message-center/invite/invite.component';
import { ForwardComponent } from 'src/app/pages/message-center/forward/forward.component';
import { AssetSource, CHAR_CODES, Constants, MessageDeliveryStatus, MessagePriority, ParticipantType, UserGroup } from 'src/app/constants/constants';
import { ForwardBehaviourComponent } from 'src/app/pages/message-center/forward-behaviour/forward-behaviour.component';
import { SignatureComponent } from 'src/app/components/signature/signature.component';
import { environment } from 'src/environments/environment';
import { ChatRoomUsersComponent } from 'src/app/pages/message-center/chat/chatroom-users/chatroom-users.component';
import { TagComponent } from 'src/app/pages/message-center/tag/tag.component';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { EmojiPopoverComponent } from 'src/app/pages/message-center/chat/emoji-popover/emoji-popover.component';
import { VideoCallService } from 'src/app/services/video-call/video-call.service';
import { ReadPopoverComponent } from 'src/app/pages/message-center/chat/read-popover/read-popover.component';
import { Urls } from 'src/app/constants/urls';
import { Socket } from 'src/app/constants/socket';
import { SubjectPopoverComponent } from 'src/app/pages/message-center/chat/subject-popover/subject-popover.component';
import { VideoCall } from 'src/app/constants/video-call';
import { convertUTCToTimeZoneDateTimeSplit, deepCopyJSON, deepParseJSON, getFileReader, isBlank, isPresent, messageCenterGUID, subtractCount } from 'src/app/utils/utils';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import {
  ChatMessageResponse,
  ChatRoomParticipant,
  ChatRoomUsersAndRoles,
  ChatRoomUsersAndRolesData,
  MessageContent,
  RoleParticipant,
  UpdateChatroomParticipantsResponse,
  UpdateChatroomParticipantStatus,
  UserMessagetoServer
} from 'src/app/interfaces/messages';
import { Config } from 'src/app/constants/config';
import { Activity } from 'src/app/constants/activity';
import * as moment from 'moment';
import { SendBulkMessageService } from 'src/app/services/send-bulk-message-service/send-bulk-message.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';
import { MsgInfoModalComponent } from 'src/app/pages/message-center/chat/msg-info-modal/msg-info-modal.component';
import { ChooseRecipientsPage } from 'src/app/pages/message-center/choose-recipients/choose-recipients.page';
import { ChoosePatientComponent } from 'src/app/pages/message-center/choose-patient/choose-patient.component';
import { Clipboard } from '@awesome-cordova-plugins/clipboard/ngx';
import { UnicodeConvertPipe } from 'src/app/pipes/unicodeConvert/unicode-convert.pipe';
import { SessionService } from 'src/app/services/session-service/session.service';
import { MessageCenterService } from 'src/app/services/message-center/message-center.service';
import { Subscription } from 'rxjs';
import { theme } from 'src/theme/theme';
import { getValueFromSession } from 'src/app/utils/storage-utils';
import { StaticDataService } from 'src/app/services/static-data-service/static-data.service';
import { getIonFlagClass, getPriority } from 'src/app/components/search-bar/search-bar.component';
import { AdvancedSelectComponent } from 'src/app/components/advanced-select/advanced-select.component';
import { AdvancedViewerComponent } from 'src/app/components/advanced-viewer/advanced-viewer.component';
import { PageRoutes } from 'src/app/constants/page-routes';
import { AdmissionDetails, MessageDeliveredUsers } from 'src/app/interfaces/common-interface';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.page.html',
  styleUrls: ['./chat.page.scss'],
  providers: [UnicodeConvertPipe]
})
export class ChatPage implements OnInit, OnDestroy {
  @ViewChild(IonContent, { static: true }) content: IonContent;
  @ViewChild(IonAccordionGroup, { static: true }) accordionGroup: IonAccordionGroup;
  appMinimizedSubscription: Subscription;
  socketStatusSubscription: Subscription;
  userCountSubscription: Subscription;
  failedMessageResendCompletedSubscription: Subscription;
  roomId: number;
  chatMessages: MessageContent[];
  currentUser: number;
  typingTimeout: ReturnType<typeof setTimeout>;
  title: string;
  isLoadingMore = false;
  messageLoadLimit = 20;
  roomUsers: ChatRoomParticipant[] = [];
  roleParticipants: RoleParticipant[] = [];
  chatParticipants: ChatRoomParticipant[] = [];
  filteredMentionUsers = [];
  mentionUsers = [];
  enableFlagging = false;
  enableTagging = false;
  enableSign = false;
  showLoadMore = false;
  isArchivedMessage: boolean;
  filterOptions: any;
  imageUrlPrefix: string;
  forwardData: any;
  pageNumber = 0;
  message = '';
  multiTagSelectionIsActive = false;
  filesInMemory = [];
  messageType: number;
  typingText: any;
  joinChat = false;
  joined = false;
  falseAsString = 'false';
  trueAsString = 'true';
  messageListIndex: number;
  isChatUpdated = false;
  messageReplyTimeout = 0;
  isRestrictToBranchHour = false;
  isWorkingHours: any;
  groupSubject: string;
  showInviteFeature = false;
  showForwardFeature = false;
  showVideoCallFeature = false;
  attachedFile: any = [];
  resData: any = [];
  selectedFileNames: any = [];
  cmisFileUploadData: any = [];
  messageData = '';
  allowedFileTypes: string[];
  fileFormat: boolean;
  fileSize: boolean;
  canReplyForMaskedMessage: boolean;
  createdUser: string;
  maskedMessageType = Number(Constants.messageListTypes.masked);
  baseId: any;
  messageGroupId: string;
  tagHistory: any;
  toggleTranslationCheck: boolean;
  toggleTranslate: boolean;
  traslatedData: any = [];
  beforeTranslation: string;
  chatPageType: string;
  isPdg = false;
  isMsgGrp = false;
  patientSiteId: any = [];
  msgGrpSites: any = [];
  getSelectedTagCount;
  createdBy: any;
  clinicianName: string;
  selectedMessageData: any;
  isEnableDoubleVerification: boolean;
  allowPatientToChat24Hour: boolean;
  videoCallRoomData: any;
  messageFilterOptions = Constants.messageFilterOptions;
  setTimeDiffValue: number;
  doNotReplyStatus = false;
  isShowDoubleVerification = false;
  showDoubleVerificationStatus = Number(Constants.configFalse);
  doubleVerificationConfirmed = false;
  isEnabledDoNotReplySection = false;
  isChatHasClose = false;
  isMessageFetching = false;
  constants = Constants;
  applessMessaging: boolean;
  lastUserActivity: number;
  isEmojiPopupOpen = false;
  enableApplessMode = false;
  messageDataLocal = '';
  messageLocal = '';
  filesInMemoryOffline = [];
  chatWithUserType: string;
  appMinimized = false;
  messageReplyTimeoutTimer;
  branchHourSwitchTimer;
  patientId = 0;
  chatRoomPatientData: any = {};
  isiPad = false;
  chatAutoTranslate: boolean;
  showMoreActions = false;
  priorityFilterValue = 0;
  flagFilterValue = 0;
  messagePriority = MessagePriority.NORMAL;
  MESSAGE_PRIORITY = MessagePriority;
  USER_GROUP = UserGroup;
  getPriority = getPriority;
  getIonFlagClass = getIonFlagClass;
  allTags;
  filteredTags = [];
  isPatient: boolean;
  isPartner: boolean;
  messagePriorityData = this.staticDataService.getPriorities();
  showMentionList = false;
  showMentionMessages = false;
  TYPE = {
    PRIORITY: 'priority',
    FLAG: 'flag'
  };
  savedRange = null;
  handleSelectionChange;
  allowChatPermission: boolean;
  deletedUsers: ChatRoomParticipant[] = [];
  chatUsersListUpdatedSubscription: Subscription;
  allowIntegrationTags = true;
  videoCallEndByInitiator: Subscription;
  allParticipants: ChatRoomParticipant[];
  showOooMessage: boolean = false;
  oooMessage: string;
  offlineStatus : boolean = false;
  isMultipleOfflineUser = false;
  isVirtualUserExist = false;
  recipient = null;
  private messageDeleteRestoreSubscription: Subscription;
  constructor(
    public readonly sharedService: SharedService,
    private readonly commonService: CommonService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly httpService: HttpService,
    private readonly modalController: ModalController,
    private readonly popoverController: PopoverController,
    private readonly actionSheetController: ActionSheetController,
    private readonly socketService: SocketService,
    private readonly videoCallService: VideoCallService,
    private readonly bulkMessageService: SendBulkMessageService,
    private readonly renderer2: Renderer2,
    private readonly graphqlService: GraphqlService,
    private readonly permissionService: PermissionService,
    private readonly videoCallPermissionService: VideoCallPermissionService,
    private browser: InAppBrowser,
    private staticDataService: StaticDataService,
    private clipboard: Clipboard,
    private cd: ChangeDetectorRef,
    private unicodeConvertPipe: UnicodeConvertPipe,
    public messageCenterService: MessageCenterService,
    public sessionService: SessionService,
    private elRef: ElementRef,
  ) {
    if (this.sharedService.platform.is('ipad') || this.sharedService.platform.is('tablet')) {
      this.isiPad = true;
    }
    this.route.queryParamMap.subscribe((params) => {
      this.createdBy = params.get('createdBy');
      this.clinicianName = params.get('displayName') || '';
    });
    this.route.paramMap.subscribe((paramMap) => {
      this.sharedService.roomID = Number(paramMap.get('chatRoomId'));
      this.roomId = Number(paramMap.get('chatRoomId'));
      this.chatPageType = paramMap.get('type');
      this.isArchivedMessage = paramMap.get('type') === Constants.messageTypes.archived;
      this.messageListIndex = this.sharedService.getChatroomIndex(this.roomId);
      this.initSocketEvents();
      this.fetchChatMessages();
    });
    this.currentUser = Number(this.sharedService.userData?.userId);
    this.imageUrlPrefix = `${environment.apiServer}${Urls.writableFilePath}`;
    this.sharedService.renderer2 = this.renderer2;
    this.filterOptions = this.sharedService?.getConfigValue(Config.defaultCategoryOfMessageDisplay);
    this.sharedService.emojiValueGet.subscribe((data) => {
      if (data) {
        this.setMessage(data);
      }
    });
    this.sharedService.disableSideMenu = false;
    this.chatUsersListUpdatedSubscription = this.sharedService.chatUsersListUpdated.subscribe((data: any) => {
      if (data.refetch) {
        this.fetchRoomUsers();
      }
    });
  }

  ngOnInit(): void {
    this.isPatient = this.sharedService.loggedUserIsPatient();
    this.isPartner = this.sharedService.loggedUserIsPartner();
    this.allowChatPermission = this.permissionService.showNewMessage();
    if (!this.appMinimizedSubscription) {
      this.appMinimizedSubscription = this.sharedService.appMinimized.subscribe((appMinimized: boolean) => {
        this.appMinimized = appMinimized;
      if (appMinimized) {
        this.leaveChatRoom();
      } else {
        this.initSocketEvents();
        this.fetchChatMessages(false, false);
      }
      });
    }
    this.socketStatusSubscription = this.socketService.socketStatusUpdated.subscribe(() => {
      if (this.socketService.status) {
        this.fetchChatMessages(false, false);
      }
    });
    this.messageListIndex = this.sharedService.getChatroomIndex(this.roomId);
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.updateConfigPermissions({ resetDoubleVerification: true, updateConfigFromPolling: true });
      this.setBranchHourSwitchTimer();
      this.checkTimeOutMessageIfUserNotAvailable();
    });
    this.userCountSubscription = this.sharedService.chatroomUserCountUpdated.subscribe((data: { chatRoomId: number }) => {
      if (isPresent(data.chatRoomId) && data.chatRoomId === this.roomId) {
        this.fetchRoomUsers();
      }
    });

    const isConfigVideoCallEnable = this.sharedService?.isEnableConfig(Config.enableVideoChat);
    const isRoleVideoCallEnable = this.permissionService.userHasPermission(Permissions.activateVideoChat);

    this.showVideoCallFeature = !this.sharedService.loggedUserIsPatient() && isConfigVideoCallEnable && isRoleVideoCallEnable;
    this.patientSiteId = [];
    this.sharedService.tempPatientSiteId = this.patientSiteId;
    this.sharedService.disableSiteFilter = false;
    this.failedMessageResendCompletedSubscription = this.messageCenterService.FailedMessageResendCompleted.subscribe((resendFailed) => {
      if (resendFailed) {
        this.sharedService.isLoading = false;
        this.fetchChatMessages(false, false);
        this.initSocketEvents();
      }
    });

    this.handleSelectionChange = () => {
      const sel = window.getSelection && window.getSelection();
      if (sel && sel.rangeCount > 0) {
        this.savedRange = sel.getRangeAt(0);
      }
    };
    document.addEventListener('selectionchange', this.handleSelectionChange);
    this.updateChatRoomParticipant();
    this.videoCallEndByInitiator = this.videoCallService.videoCallEndByInitiator.subscribe((data) => {
      if (data && +data.chatroomId === this.roomId) {
        this.joinChat = false;
        this.joined = false;
      }
    });
  }

  updateConfigPermissions(params = { resetDoubleVerification: false, updateConfigFromPolling: false }): void {
    const { resetDoubleVerification, updateConfigFromPolling } = params;
    this.applessMessaging = this.sharedService.isEnableConfig(Config.enableApplessMessaging);
    this.enableFlagging = this.sharedService?.isEnableConfig(Config.enableMessageFlagging);
    this.enableTagging =
      this.sharedService?.isEnableConfig(Config.enableMessageTagging) && this.permissionService?.userHasPermission(Permissions.tagMessage);
    this.enableSign = this.permissionService?.userHasPermission(Permissions.signMessage);
    this.messageReplyTimeout = Number(this.sharedService?.getConfigValue(Config.messageReplyTimeout));
    this.isRestrictToBranchHour = this.sharedService?.isEnableConfig(Config.restrictToBranchHour);
    this.isWorkingHours = this.sharedService?.checkBranchHours(false)?.isWorkingHours;
    this.showInviteFeature = !this.sharedService?.loggedUserIsPatient() && this.permissionService?.userHasPermission(Permissions.inviteToChat);
    this.showForwardFeature = !this.sharedService?.loggedUserIsPatient() && this.permissionService?.userHasPermission(Permissions.messageForwarding);
    const isInitialConfigFetch = isBlank(this.toggleTranslate);
    const translateConfigBeforeFetch = this.isTranslationEnabled;
    this.toggleTranslate = this.sharedService.isEnableConfig(Config.toggleChatTranslation);
    this.chatAutoTranslate = this.sharedService.isEnableConfig(Config.chatAutoTranslate);
    if (this.chatMessages && ((this.isTranslationEnabled && (!translateConfigBeforeFetch || isInitialConfigFetch)) || updateConfigFromPolling)) {
      this.enableTranslation(true);
    }
    this.isEnableDoubleVerification = this.sharedService.isEnableConfig(Config.enableDoubleVerification);
    this.allowPatientToChat24Hour = this.sharedService.isEnableConfig(Config.workingHour);
    if (resetDoubleVerification) {
      this.doubleVerificationConfirmed = false;
    }
    if (
      !this.isRestrictToBranchHour &&
      this.timeDiff >= this.messageReplyTimeout * 60 &&
      Number(this.sharedService?.userData.group) === Constants.patientGroupId
    ) {
      this.doNotReplyStatus = true;
      if (resetDoubleVerification) {
        this.isShowDoubleVerification = false;
        this.showDoubleVerificationStatus = Number(Constants.configFalse);
      }
    } else {
      if (this.isRestrictToBranchHour && Number(this.sharedService?.userData.group) === Constants.patientGroupId) {
        if (this.isWorkingHours) {
          this.doNotReplyStatus = false;
        } else {
          this.doNotReplyStatus = true;
          if (resetDoubleVerification) {
            this.showDoubleVerificationStatus = Number(Constants.configFalse);
            this.isShowDoubleVerification = false;
          }
        }
      } else {
        this.doNotReplyStatus = false;
        if (
          !this.isWorkingHours &&
          this.sharedService.getSiteConfigValue(Config.workingHour) === '1' &&
          this.isEnableDoubleVerification &&
          Number(this.sharedService?.userData.group) === Constants.patientGroupId &&
          isPresent(this.selectedMessageData) &&
          this.isGeneralMessageOrGroup &&
          resetDoubleVerification
        ) {
          this.isShowDoubleVerification = true;
        } else {
          this.showDoubleVerificationStatus = Number(Constants.configFalse);
          this.isShowDoubleVerification = false;
        }
      }
    }

    if (this.selectedMessageData.messageType === this.maskedMessageType) {
      this.doNotReplyStatus = false;
    }
    this.isEnabledDoNotReplySection = this.isEnabledDoNotReplyFunction;
  }

  get isEnabledDoNotReplyFunction(): boolean {
    let isEnabled = false;
    const lastMessage = this.getLastMessage();
    const baseID = lastMessage.baseId ? lastMessage.baseId : this.selectedMessageData.baseId || 0;
    const isDoNotReplyNoBaseID = this.doNotReplyStatus && parseInt(baseID) === 0;
    if (this.isGeneralMessageOrGroup && isDoNotReplyNoBaseID && !this.isArchivedMessage && !this.sessionService.applessMessagingFlow) {
      isEnabled = true;
    }
    return isEnabled;
  }

  get isChatHasCloseFunction(): boolean {
    const isEnabled = this.roomUsers?.length <= 1 && this.isGeneralMessageOrGroup && Number(this.sharedService?.userData.group) === UserGroup.PATIENT;
    return isEnabled;
  }

  get timeDiff(): number {
    return isPresent(this.selectedMessageData) && isPresent(this.selectedMessageData.sent)
      ? moment().diff(moment(Number(this.selectedMessageData?.sent) * 1000), 'minutes')
      : 0;
  }

  get showChatFilterOption(): boolean {
    return !this.sharedService.loggedUserIsPatient();
  }

  get showFooterButtons(): boolean {
    return !this.isArchivedMessage && this.isGeneralMessageOrGroup && !this.isMaskedChild;
  }
  get isGeneralMessageOrGroup(): boolean {
    return [Constants.messageListTypes.general, Constants.messageListTypes.pdg, Constants.messageListTypes.messageGroup].includes(
      String(this.messageType)
    );
  }
  leaveChatRoom(): void {
    if (this.sharedService.roomID === this.roomId) {
      this.socketService.emitEvent(Socket.leaveChatRoom, this.roomId);
      this.socketService.subscribeEvent(Socket.userLeave);
    }
  }
  ngOnDestroy(): void {
    this.chatUsersListUpdatedSubscription.unsubscribe();
    this.leaveChatRoom();
    if (this.messageReplyTimeoutTimer) {
      clearTimeout(this.messageReplyTimeoutTimer);
    }
    if (this.branchHourSwitchTimer) {
      clearTimeout(this.branchHourSwitchTimer);
    }
    if (this.sharedService.messageList && this.messageListIndex > -1) {
      const unreadCount = +this.sharedService.messageList[this.messageListIndex]?.unreadCount || 0;
      this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, unreadCount);
      if (this.sharedService.messageList[this.messageListIndex]?.unreadCount) {
        this.sharedService.messageList[this.messageListIndex].unreadCount = 0;
      }
    } else if (this.messageListIndex === -1) {
      this.sharedService.childMessageUpdated(Number(this.baseId));
    }
    if (this.isChatUpdated) {
      this.sharedService.messageListUpdated.next({ chatRoomId: this.roomId });
    }
    this.sharedService.trackActivity({
      type: Activity.messaging,
      name: Activity.endChatSession,
      des: {
        data: { chatroomId: this.roomId },
        desConstant: Activity.endChatSessionDes
      },
      linkageId: this.roomId
    });
    if (this.isTranslationEnabled) {
      this.httpService
        .doPost({
          endpoint: APIs.translationLogs,
          payload: this.traslatedData,
          loader: false
        })
        .subscribe(() => {
          // do nothing
        });
    }
    document.removeEventListener('selectionchange', this.handleSelectionChange);
    this.roomId = undefined;
    this.userCountSubscription.unsubscribe();
    this.appMinimizedSubscription.unsubscribe();
    this.socketStatusSubscription.unsubscribe();
    this.failedMessageResendCompletedSubscription.unsubscribe();
    this.videoCallEndByInitiator.unsubscribe();
    if (this.messageDeleteRestoreSubscription) {
      this.messageDeleteRestoreSubscription.unsubscribe();
    }
  }
  failedMessageResend(): void {
    if (this.filesInMemoryOffline.length > 0) {
      this.fileUpload();
    }
  }
  initSocketEvents(): void {
    this.roomId = Number(this.sharedService.roomID || this.roomId);
    this.socketService.emitEvent(
      Socket.joinToChatroom,
      {
        user: this.sharedService.userData?.userId,
        room: this.roomId ? this.roomId.toString() : this.sharedService.roomID.toString(), // use sharedService chatRoomId if roomId not found
        name: this.sharedService.userData?.displayName,
        avatar: this.sharedService.userData?.profileImageThumbUrl,
        citusRole: this.sharedService.userData?.group,
        patientReminderCheckingType: this.sharedService?.getConfigValue(Config.patientReminderCheckingType),
        sites: this.sharedService.userData.mySites,
        tenantId: this.sharedService.userData.tenantId,
        tenantKey: this.sharedService.userData.tenantKey,
        tenantName: this.sharedService.userData.tenantName,
        eventSource: JSON.stringify({
          environment: this.sharedService.platformValue,
          device: this.commonService.getDeviceType(),
          component: Constants.eventSourceComponent.chatPage
        })
      },
      (data) => {
        if (data === this.trueAsString) {
          this.failedMessageResend();
        }
      }
    );
    this.socketService.subscribeEvent(Socket.lastUserActivity).subscribe((data) => {
      this.lastUserActivity = data[0];
    });
    this.socketService.subscribeEvent(Socket.userMessage).subscribe((data: any) => {
      let message = data[0];
      const displayName = data[1];
      message.showDownloadOptions =
        message?.message &&
        (message.message.includes('showCmisPdfOrDocs') || message.message.includes('showPdfOrDocs') || message.message.includes('showCmisImage'));
      if (message.showDownloadOptions) {
        message = this.showDownloadIcon(message);
      }
      if (this.roomId === Number(data[0]?.chatroomId)) {
        if (
          this.messageListIndex === -1 &&
          this.messageCenterService.isFirstChat(this.chatMessages) &&
          Number(message.userId) !== Constants.administratorId
        ) {
          /**
           * Considering this as a new chat message
           */
          this.sharedService.addNewChatFromAPI(this.roomId.toString());
        }
        const index = this.chatMessages ? this.chatMessages.findIndex((item) => item.uniqueId === message.uniqueId) : -1;
        if (this.messageCenterService.showIncomingMessage(message, this.filterOptions) && index === -1) {
          this.chatMessages.push({
            message: message.data,
            avatar: message.avatar,
            uniqueId: message.id,
            id: message.id,
            sent: message.time,
            sign: this.falseAsString,
            userid: message.userId,
            readUsers: [],
            displayName,
            priorityId: message.priorityId,
            showMention: message.mentionedUsers && message.mentionedUsers.indexOf(this.currentUser) !== -1,
            repliedTo: message.repliedTo
          });
        }
        this.scrollToBottom();
        this.addActiveMention();
        this.isChatUpdated = true;
        if (this.sharedService.messageList && this.messageListIndex > -1) {
          this.messageCenterService.updateChatMessageListDetails(this.sharedService.messageList[this.messageListIndex], message);
          if (
            String(this.messageType) === Constants.messageListTypes.masked &&
            this.sharedService.messageList[this.messageListIndex].maskedReplyMessages
          ) {
            this.sharedService.messageList[this.messageListIndex].maskedReplyMessages.forEach((childMessage: any) => {
              this.messageCenterService.updateChatMessageListDetails(childMessage, message);
            });
          }
        }
        if (this.isTranslationEnabled) {
          this.messageTranslate(message);
        }
      }
    });

    this.socketService.subscribeEvent(Socket.updateUserMessage).subscribe((data: any) => {
      const chat = data[0];
      if (this.roomId === Number(chat.chatroomId)) {
        const unique = this.messageCenterService.fetchMessageUniqueId(chat);
        const index = this.messageCenterService.getMessageChatIndex(this.chatMessages, unique);
        this.messageCenterService.removeFailedMessage(this.roomId, unique);
        if (index > -1 && chat.status !== Constants.duplicate) {
          this.chatMessages[index].id = chat.id;
          this.chatMessages[index].sent = chat.sent;
          this.beforeTranslation = JSON.stringify([...JSON.parse(this.beforeTranslation), this.chatMessages[index]]);
          this.selectedMessageData.sent = chat?.sent ? chat.sent : this.selectedMessageData.sent;
          this.resetMessageReplyTimeoutOnNewmessage(this.chatMessages[index]);
          if (this.chatMessages[index].userid !== this.sharedService.userData.userId) {
            if (!this.appMinimized) {
              this.messageCenterService.updateDeliveryOrReadStatus(this.roomId).subscribe();
            }
          } else {
            setTimeout(() => {
              this.sharedService
                .getMessageDeliveredUsers({ messageId: chat.id, deliveryStatus: MessageDeliveryStatus.ALL, showLoader: false })
                .subscribe(({ readUsers }) => {
                  this.chatMessages[index].readUsers = readUsers;
                });
            }, 500);
          }
        }
        this.addActiveMention();
      }
    });

    this.messageDeleteRestoreSubscription = this.sharedService.$messageDeleteRestore.subscribe((data: any) => {
      const chat = data[0];
      if (this.roomId === Number(chat.chatroomId) && chat.messageId) {
        const url = `${APIs.getChatMessages}?room=${chat.chatroomId}&id=${chat.messageId}`;
        const chatIndex = this.chatMessages.findIndex((item) => item.id === chat.messageId);
        this.httpService.doGet({ endpoint: url }).subscribe((res: any) => {
          if (chatIndex > -1 && res && res && res.content && res.content.length) {
            let chatMessage = res.content[0];
            chatMessage.showDownloadOptions =
              chatMessage?.message &&
              (chatMessage.message.includes('showCmisPdfOrDocs') ||
                chatMessage.message.includes('showPdfOrDocs') ||
                chatMessage.message.includes('showCmisImage'));
            if (chatMessage.showDownloadOptions) {
              chatMessage = this.showDownloadIcon(chatMessage);
            }
            this.chatMessages[chatIndex] = chatMessage;
          }
        });
      }
    });

    this.socketService.subscribeEvent(Socket.signatureFromUser).subscribe((data: any) => {
      this.setSignatureValueAfterSearch(data[0]);
    });

    this.socketService.subscribeEvent(Socket.removesignatureFromUser).subscribe((data: any) => {
      const signData = data[0];
      signData.sign = this.falseAsString;
      this.setSignatureValueAfterSearch(signData);
    });

    this.socketService.subscribeEvent(Socket.userTyping).subscribe((data: any) => {
      this.typingText = data[0];
    });

    this.socketService.subscribeEvent(Socket.exitVideoChat).subscribe(() => {
      this.joinChat = false;
      this.videoCallService.sharedService.videoCall = false;
    });

    this.socketService.subscribeEvent(Socket.updateUserMessageReadStatus).subscribe(([readData]) => {
      if (this.chatMessages) {
        this.chatMessages.forEach((chat) => {
          if (Number(chat.userid) === this.currentUser) {
            if (!readData.id) {
              if (chat.readUsers && chat.readUsers.length) {
                const chatReadUserIndex = chat.readUsers.findIndex((item: any) => Number(item.userid) === Number(readData.readUsers.userid));
                if (chatReadUserIndex === -1) {
                  chat.readUsers.push(readData.readUsers);
                }
              } else {
                chat.readUsers.push(readData.readUsers);
              }
            } else if (Number(chat.id) === Number(readData.id)) {
              chat.readUsers = readData.readUsers;
            }
          }
        });
      }
    });

    this.socketService.subscribeEvent(Socket.onGoingVideoCall).subscribe((data) => {
      this.joinChat = true;
      if (data[0].roomData) {
        this.videoCallRoomData = data[0].roomData;
      }
      this.videoCallService.onGoingChatroom = data[0].roomId;
      if (!isBlank(this.videoCallService.onGoingChatroom)) {
        this.videoCallService.participant = true;
      } else {
        this.videoCallService.participant = false;
      }
      if (String(this.videoCallService.onGoingChatroom) === String(this.roomId)) {
        if (String(this.roomId) !== this.videoCallService.callInitiatedRoomId) {
          this.joined = true;
          this.joinChat = true;
        } else {
          this.joined = false;
          this.joinChat = false;
        }
      } else {
        this.joined = false;
        this.joinChat = false;
      }
    });

    this.socketService.subscribeEvent(Socket.updateMessageTags).subscribe((tagsData) => {
      const data = tagsData[0];
      if (data && this.sharedService.userData.userId !== data.updatedBy) {
        for (let i = 0; i < this.chatMessages.length; i += 1) {
          if (data.messageIds.includes(this.chatMessages[i].id) && isPresent(data.assosiatedPatient)) {
            this.chatRoomPatientData = { ...data.assosiatedPatient, displayName: data.assosiatedPatient?.displayname };
            this.chatMessages[i].pdisplayname = data.assosiatedPatient?.displayname;
            this.chatMessages[i].patient = data.assosiatedPatient?.userId;
            if (+data?.patientTagInfo?.patientId > 0) {
              this.chatMessages[i].patientTagInfo = data?.patientTagInfo;
            }
          }
          if (data.messageIds.indexOf(this.chatMessages[i].id) > -1) {
            this.chatMessages[i].tag = this.trueAsString;
            this.chatMessages[i].tagedItems = this.chatMessages[i]?.tagedItems
              ? [...this.chatMessages[i].tagedItems, ...data.tagNames]
              : data.tagNames;
            if (data.removedTagId) {
              this.chatMessages[i].tagedItems = this.chatMessages[i].tagedItems?.filter((tag) => Number(tag.id) !== Number(data.removedTagId));
              this.removePatientAssociationFromMessage(i);
            }
            this.chatMessages[i].tagSign = data.autoApprove ? this.trueAsString : this.falseAsString;
          }
        }
      }
    });

    this.sharedService.messageListUpdated.subscribe(() => {
      this.messageListIndex = this.sharedService.getChatroomIndex(this.roomId);
    });
  }
  async showMessageFailedPopover(): Promise<void> {
    this.commonService.showCustomAlert({
      message: this.commonService.getTranslateData('MESSAGES.SENDING_DISCONNECTED_MESSAGE'),
      cssClass: 'common-alert'
    });
  }

  userTyping(): void {
    this.messageCenterService.userTyping();
  }

  messageTranslate(message?: any): void {
    let messageExistsToTranslate = false;
    let messageToBeTranslated = '';
    if (isPresent(message)) {
      if (message.userId) {
        if (message.language && message.language.split('-')[0] !== navigator.language.split('-')[0]) {
          messageExistsToTranslate = true;
        } else if (!message.language) {
          messageExistsToTranslate = true;
        }
      }
      messageToBeTranslated += `${encodeURIComponent(this.unicodeConvertPipe.transform(message.data))}&q=`;
    } else {
      this.chatMessages.forEach((elem) => {
        if (elem.userid) {
          if (elem.language && elem.language.split('-')[0] !== navigator.language.split('-')[0]) {
            messageExistsToTranslate = true;
          } else if (!elem.language) {
            messageExistsToTranslate = true;
          }
        }
        messageToBeTranslated += `${encodeURIComponent(this.unicodeConvertPipe.transform(elem.message))}&q=`;
      });
    }
    if (messageExistsToTranslate) {
      const data = {
        chatroomMessages: messageToBeTranslated.substring(0, messageToBeTranslated.length - 3),
        ccLang: navigator.language.split('-')[0]
      };
      this.httpService
        .doPost({
          endpoint: APIs.messageTranslate,
          payload: data,
          extraParams: { room: this.roomId },
          loader: false
        })
        .subscribe(
          (res) => {
            this.sharedService.isLoading = false;
            if (res.data.translations) {
              this.translatedMessageList(res.data.translations, message);
            }
          },
          (error) => {
            this.sharedService.errorHandler(error);
            const msg = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.NO_TRANSLATION_AVAILABLE', {
              appName: theme.name
            });
            this.commonService.showMessage(msg);
          }
        );
    }
  }
  setSignatureValueAfterSearch(incomingChatData: any): void {
    const chatIndex = this.chatMessages.findIndex((item: any) => Number(item.id) === Number(incomingChatData.id));
    if (chatIndex !== -1) {
      this.chatMessages[chatIndex].sign = incomingChatData.sign;
    }
  }

  isSameUser(x: any): boolean {
    return Number(this.sharedService?.userData?.userId) === Number(x.userid);
  }

  fetchChatMessages(isloadMore = false, showLoader = true): void {
    this.messageCenterService.updateDeliveryOrReadStatus(this.roomId).subscribe();
    const getPath = window.location.pathname;
    if (!this.roomId && getPath.includes('/chat')) {
      const getRoomID = getPath.split('chat/');
      this.roomId = getRoomID.length === 2 ? Number(getRoomID[1]) : Constants.defaultChatRoomId;
    }
    const params = {
      room: this.roomId ? this.roomId.toString() : this.sharedService.roomID.toString(),
      userId: this.sharedService.userData.userId,
      messagePriority: this.priorityFilterValue,
      flagVlaue: this.flagFilterValue,
      messageFilter: this.filterOptions,
      tagIds: this.filteredTags.map((tag) => tag.id).join(','),
      page: this.pageNumber,
      last: isloadMore ? Number(this.chatMessages[0].id) : 0,
      bundleIdentifier: ConfigValues.bundleIdentifier,
      action: Constants.fetchMessageAction,
      mentionedUsers: this.showMentionMessages ? [this.currentUser] : []
    };
    if (isloadMore) {
      this.isLoadingMore = true;
    }
    this.messageCenterService.fetchChatMessages(params, showLoader).subscribe(
      (response: ChatMessageResponse) => {
        this.sharedService.isLoading = false;
        this.selectedMessageData = response;
        this.isMessageFetching = response.success ? false : true;
        const content = response.content ? response.content : [];
        this.enableApplessMode = response.appLessMode;
        const setExtraParams = {
          sent: '',
          displayName: ''
        };
        if (isPresent(content)) {
          setExtraParams.displayName = response['displayName'] || '';
          if (this.pageNumber === 0) {
            setExtraParams.sent = content[0].sender_time;
          }
        }

        this.selectedMessageData = {
          ...this.selectedMessageData,
          ...setExtraParams
        };
        this.messageType = Number(response.messageType);
        this.groupSubject = response.subject;
        this.title = response.title;
        this.createdUser = response.createdBy;
        this.baseId = response.baseId;
        this.messageGroupId = response.messageGroupId;
        this.chatMessages = isloadMore ? [...content.reverse(), ...this.chatMessages] : content.reverse();
        if (!this.isArchivedMessage) {
          this.chatMessages = [...this.chatMessages, ...this.messageCenterService.getFailedMessages(this.roomId)];
        }

        this.chatMessages.forEach((message) => {
          message.showDownloadOptions = message.message.includes('showCmisPdfOrDocs') || message.message.includes('showPdfOrDocs') || message.message.includes('showCmisImage');
          if (message.showDownloadOptions) {
            message = this.showDownloadIcon(message);
          }
          const mentionedUsers = message.mentionedUsers ? message.mentionedUsers : [];
          message.showMention = mentionedUsers.indexOf(this.currentUser) !== -1;
        });
        if ((isloadMore || !showLoader) && this.isTranslationEnabled) {
          this.messageTranslate();
        }
        this.beforeTranslation = JSON.stringify(isloadMore ? [...content.reverse(), ...JSON.parse(this.beforeTranslation)] : content);

        this.canReplyForMaskedMessage =
          this.createdBy !== this.sharedService.userData.userId && String(this.messageType) === Constants.messageListTypes.masked;
        this.showLoadMore = content.length >= Constants.messageLoadLimit;
        this.isLoadingMore = false;
        if (this.pageNumber === 0) {
          this.updateConfigPermissions({ resetDoubleVerification: !this.doubleVerificationConfirmed, updateConfigFromPolling: false });
          this.setBranchHourSwitchTimer();
          this.checkTimeOutMessageIfUserNotAvailable();
        }
        if (!isloadMore) {
          if (!this.isBroadcast) {
            this.fetchRoomUsers();
          } else {
            this.isChatHasClose = this.isChatHasCloseFunction;
          }
          this.scrollToBottom();
          const result = deepCopyJSON(response);
          const patientData = this.getPatientData(result);
          this.chatRoomPatientData = isPresent(patientData) ? patientData : {};
          if (result && 'isPdg' in result && result.isPdg && result.isPdg === 1) {
            this.isPdg = true;
            this.setPatientSiteData(result);
          } else if (result && result.isPdg === 0 && result.messageGroupSites) {
            this.isMsgGrp = true;
            this.msgGrpSites = [];
            this.msgGrpSites = result.messageGroupSites.split(',');
            this.msgGrpSites.forEach((elements) => {
              this.patientSiteId.push(parseInt(elements));
              this.sharedService.tempPatientSiteId = this.patientSiteId;
            });
            this.sharedService.disableSiteFilter = true;
          } else {
            this.isMsgGrp = false;
            this.isPdg = false;
            this.setPatientSiteData(result);
          }
        } else {
          this.isChatHasClose = this.isChatHasCloseFunction;
        }
        this.addActiveMention();
      },
      () => {
        this.router.navigate([this.isArchivedMessage ? PageRoutes.archivedMessage : PageRoutes.activeMessages]);
      }
    );
  }
  setPatientSiteData(result: any) {
    const patientData = result?.patient_data?.[0];
    if (patientData?.roleId && +patientData.roleId === UserGroup.PATIENT) {
      const siteId = +this.patientData.siteId;
      if (!this.patientSiteId.includes(siteId)) {
        this.patientSiteId.push(siteId);
      }
      this.sharedService.tempPatientSiteId = this.patientSiteId;
      this.sharedService.disableSiteFilter = true;
    }
  }
  getPatientData(data) {
    let patientData;
    if (isPresent(data?.patient_data) && isPresent(data.patient_data[0])) {
      [patientData] = data.patient_data;
    } else if (isPresent(data?.patientTagInfo?.patient_data)) {
      const { patientTagInfo } = data;
      patientData = {
        ...patientTagInfo,
        ...patientTagInfo?.patient_data,
        userId: patientTagInfo?.patientId
      };
    }
    return patientData;
  }

  checkTimeOutMessageIfUserNotAvailable() {
    if (this.messageReplyTimeoutTimer) {
      clearTimeout(this.messageReplyTimeoutTimer);
    }

    const convertToHours = Number(this.messageReplyTimeout) * 60;
    if (!this.isRestrictToBranchHour && this.timeDiff < this.messageReplyTimeout * 60 && this.timeDiff >= 0) {
      const timeoutRefresh = convertToHours > this.timeDiff ? convertToHours - this.timeDiff : 0;
      this.setMessageReplyTimeout(Number(timeoutRefresh));
    }
  }

  getLastMessage() {
    let lastMessage: any = {};
    const getAllMessages = this.chatMessages.filter((item) => item.userid);
    if (getAllMessages.length > 0) {
      lastMessage = getAllMessages[getAllMessages.length - 1];
    }
    return lastMessage;
  }

  get isBroadcast(): boolean {
    return this.messageType === Number(Constants.messageListTypes.broadcast);
  }

  get isMaskedChild(): boolean {
    return Number(this.baseId) !== 0;
  }

  chatItemClick(event: any, message = null): void {
    const target = event.target || event.srcElement || event.currentTarget;
    const imageElement = target.getAttribute('ng-click');
    if (imageElement) {
      const showDocumentOption = imageElement.indexOf('showDocumentOptionForMobile') > -1;
      if (imageElement.indexOf('showCmisImage') > -1 || imageElement.indexOf('showImage') > -1) {
        let url = target.getAttribute('src') || target.getAttribute('data-viewsrc');
        url = `${url.split('?')[0]}?type=image`;
        this.presentAdvancedViewerModal({ url, type: Constants.documentTypes.image, component: AdvancedViewerComponent });
      } else if (imageElement.indexOf('showCmisPdfOrDocs') > -1 || imageElement.indexOf('showPdfOrDocs') > -1 || showDocumentOption) {
        let url = target.getAttribute('data-src') || target.getAttribute('data-viewsrc');
        if (url?.includes('<a href=')) {
          url = url.replace('<a href="', '');
        }
        url = url?.replace('?type=pdf', '');
        if (imageElement.indexOf('showCmisPdfOrDocs') > -1 || imageElement.indexOf('showPdfOrDocs') > -1) {
          url = target.getAttribute('data-mediatype') === 'pdf' ? url : `${url}?type=pdf`;
        }
        this.sharedService.isLoading = true;
        this.sharedService
          .presentPdfFromLink({ url, source: AssetSource.CMIS, isDownloadDoc: showDocumentOption })
          .then((data: any) => {
            this.sharedService.isLoading = false;
            if (data.status && isPresent(data.url)) {
              if (this.sharedService.platform.is('ios') && this.sharedService.platform.is('capacitor')) {
                this.browser.create(data.url, '_blank', this.sharedService.inAppBrowserOptions());
              } else {
                this.presentAdvancedViewerModal({ url: data.url, type: Constants.documentTypes.pdf, component: AdvancedViewerComponent });
              }
            } else if (this.sharedService.platform.is('capacitor')) {
              this.browser.create(url, '_system');
            }
          })
          .catch(() => {
            this.sharedService.isLoading = false;
          });
      }
    }
    // TODO Scroll to the available Message 
    // if (message.repliedTo && event.target.parentElement.className.includes('callout')) {
    //   document.querySelectorAll(`[message-id="${message.repliedTo}"]`).forEach((element: HTMLElement) => {
    //     this.content.scrollToPoint(0, (<HTMLElement> element.offsetParent).offsetTop, 500);
    //     setTimeout(() => {
    //       this.animationCtrl
    //       .create()
    //       .addElement(element.getElementsByClassName('msg-content')[0])
    //       .fill('none')
    //       .duration(800)
    //       .keyframes([
    //         { offset: 0, transform: 'scale(1)', opacity: '1' },
    //         { offset: 0.5, transform: 'scale(1.1)', opacity: '0.3' },
    //         { offset: 1, transform: 'scale(1)', opacity: '1' },
    //       ]).play();
    //     })
    //   })
    // }
  }
  async presentAdvancedViewerModal(componentProps) {
    const modal = await this.modalController.create({
      component: AdvancedViewerComponent,
      componentProps
    });
    return modal.present();
  }
  /**
   * Fetches the list of users in the chat room.
   *
   * @param selectionOption - An optional selection option. values: forward and inviteSuccess
   * @param extraParams - An object containing additional parameters.
   * */
  fetchRoomUsers(selectionOption?: 'forward' | 'inviteSuccess', extraParams?: { inviteResponseData?: any }): void {
    const { inviteResponseData } = extraParams || {};
    if (!this.roomId) {
      return;
    }
    const url = APIs.getRoomUsers;
    const params: any = {
      targetId: this.roomId,
      status: Constants.notRejected,
      includeArchivedUsers: true
    };
    if (selectionOption === 'forward') {
      params.tenantId = this.sharedService?.userData?.tenantId;
      params.userid = this.sharedService?.userData?.userId;
    }
    if (!this.sharedService.isEnableConfig(Config.enableMultiSite)) {
      params.siteIds = Constants.configFalse;
    }
    this.httpService.doGet({ endpoint: url, extraParams: params }).subscribe({
      next: ({ data }: ChatRoomUsersAndRoles) => {
        this.usersList({ chatRoomData: data, selectionOption, inviteResponseData });
        this.allParticipants = data.allParticipants;
        this.handleAppLessAndInAppMode(data);
      }
    });
  }
  /**
   * Updates the list of users in the chat room.
   *
   * @param data - An object containing the chat room data and an optional selection option.
   * @param data.chatRoomData - The data of the chat room, including the participants and roles.
   * @param data.selectionOption - An optional selection option.
   */
  usersList(data: { chatRoomData: ChatRoomUsersAndRolesData; selectionOption?: 'forward' | 'inviteSuccess'; inviteResponseData?: any }) {
    const { chatRoomData, selectionOption, inviteResponseData } = data;
    const { allParticipants, chatParticipants, roleParticipants } = chatRoomData;
    this.roleParticipants = roleParticipants;
    this.chatParticipants = chatParticipants;
    let roomUsersBeforeInvite = [];
    const notificationToIds = [];
    if (selectionOption === 'inviteSuccess') {
      roomUsersBeforeInvite = this.roomUsers.map((user) => +user.userId);
    }
    this.roomUsers = [];
    this.deletedUsers = [];
    this.mentionUsers = [];
    this.filteredMentionUsers = [];
    let virtualUserCount = 0;
    let enrolledUserCount = 0;
    let siteId = 0;
    let isDisabledSite = false;
    const roomUsers = [];
    const roomUsersDeleted = [];
    const rolesWithDeletedUsers = {};
    allParticipants?.forEach((user) => {
      let userIndex =
        user.deleted === Constants.configTrue
          ? roomUsersDeleted.findIndex((item) => item === user.userId)
          : roomUsers.findIndex((item) => item === user.userId);
      if (user.roleId === Constants.roleId.patient) {
        if (user.roleName === Constants.roleName.patient) {
          this.patientId = +user.userId;
        } else if (user.roleName === Constants.roleName.alternateContact && user.caregiver_userid && Number(user.caregiver_userid) > 0) {
          this.patientId = +user.caregiver_userid;
        }
      }
      if (isBlank(this.clinicianName) && user.roleId !== Constants.roleId.patient && allParticipants.length === 2) {
        this.clinicianName = user.displayName;
      }
      if (user.deleted === Constants.configTrue && userIndex === -1) {
        this.deletedUsers.push(user);
        roomUsersDeleted.push(user.userId);
        userIndex = roomUsersDeleted.length - 1;
        if (!rolesWithDeletedUsers[user.participantRole]) {
          rolesWithDeletedUsers[user.participantRole] = 1;
        } else {
          rolesWithDeletedUsers[user.participantRole] += 1;
        }
      } else if (userIndex === -1) {
        if (!roomUsersBeforeInvite.includes(+user.userId) && selectionOption === 'inviteSuccess') {
          notificationToIds.push({ userid: user.userId, roomid: this.roomId, name: user.displayName });
        }
        if (Number(user.userId) !== this.currentUser) {
          this.mentionUsers.push(user);
          this.filteredMentionUsers.push(user);
        }
        roomUsers.push(user.userId);
        this.roomUsers.push(user);
        userIndex = roomUsers.length - 1;
        if (isBlank(user.passwordStatus)) {
          virtualUserCount += 1;
        } else {
          enrolledUserCount += 1;
        }
      }
      if (user.participantType === ParticipantType.ROLE) {
        if (user.deleted === Constants.configTrue) {
          if (isBlank(this.deletedUsers[userIndex].assignedRoles)) {
            Object.assign(this.deletedUsers[userIndex], { assignedRoles: [+user.participantRole] });
          } else if (!this.deletedUsers[userIndex].assignedRoles.includes(+user.participantRole)) {
            this.deletedUsers[userIndex].assignedRoles.push(+user.participantRole);
          }
        } else if (isBlank(this.roomUsers[userIndex].assignedRoles)) {
          Object.assign(this.roomUsers[userIndex], { assignedRoles: [+user.participantRole] });
        } else if (!this.roomUsers[userIndex].assignedRoles.includes(+user.participantRole)) {
          this.roomUsers[userIndex].assignedRoles.push(+user.participantRole);
        }
      }
    });
    if (this.patientData && this.patientData.siteId) {
      if (!this.isPdg && !this.isMsgGrp) {
        if (!this.patientSiteId.includes(+this.patientData.siteId)) {
          this.patientSiteId.push(+this.patientData.siteId);
        }
        this.sharedService.tempPatientSiteId = this.patientSiteId;
        this.sharedService.disableSiteFilter = true;
      }
      siteId = +this.patientData.siteId;
      isDisabledSite = !!siteId;
    }
    this.roleParticipants = this.roleParticipants.map((role) => ({
      ...role,
      deletedUsersCount: rolesWithDeletedUsers[role.id] || 0
    }));
    this.isEnabledDoNotReplySection = this.isEnabledDoNotReplyFunction;
    this.isChatHasClose = this.isChatHasCloseFunction;
    this.chatWithUserType = virtualUserCount > 0 && enrolledUserCount === 1 ? Constants.virtualUser : Constants.enrolledUser;
    if (selectionOption === 'forward') {
      this.presentForwardModal({ siteId, isDisabledSite });
    }
    if (notificationToIds.length > 0 && selectionOption === 'inviteSuccess') {
      this.socketService.emitEvent(Socket.userMessagetoServer, {
        data: inviteResponseData.response,
        insert: false,
        notificationToId: notificationToIds,
        tenantId: this.sharedService.userData?.tenantId,
        tenantName: this.sharedService.userData?.tenantName
      });
      if (
        this.sharedService.isEnableConfig(Config.showPushNotificationForChatRoomInvite) &&
        this.sharedService.isEnableConfig(Config.showChatHistoryToNewParticipant)
      ) {
        const notificationData = {
          sourceId: Constants.sourceId.other.toString(),
          sourceCategoryId: Constants.sourceCategoryId.userIniviteNotification
        };
        this.sentPushNotification(
          this.commonService.getTranslateData('MESSAGES.CHAT_INVITE_NOTIFICATION'),
          notificationToIds,
          Constants.chatRoomActionType.invite,
          undefined,
          notificationData
        );
      }
    }
    if (!roomUsers.includes(this.sharedService.userData.userId.toString()) && !this.isArchivedMessage) {
      this.sharedService.notPartOfChatRedirect();
    }
    if (!this.isPdg && !this.isMsgGrp && !this.isMaskedChild) {
      const titlePartedIndex = this.title.lastIndexOf(' + ');
      const titleWithOutCount = titlePartedIndex === -1 ? this.title : this.title.slice(0, titlePartedIndex);
      const titleCount = roomUsers.length - 2;
      if (this.roomUsers.length > 2) {
        this.title = `${titleWithOutCount} + ${titleCount}`;
      } else {
        this.title = titleWithOutCount;
      }
    }
    this.sharedService.chatUsersListUpdated.next(this.userListComponentProps);
  }
  getOooInfo(userId) {
    const matchingParticipant = this.allParticipants?.find((participant) => +participant.userId === +userId);
    return matchingParticipant && matchingParticipant.oooInfo ? matchingParticipant.oooInfo : null;
  }
  async presentInviteModal(): Promise<void> {
    const modal = await this.modalController.create({
      component: InviteComponent,
      componentProps: {
        chatRoomId: this.roomId,
        messageGroupId: this.messageGroupId ? this.messageGroupId : 0,
        patientId: this.patientId,
        roomUsers: this.roomUsers
      }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data && data.response) {
        if (this.filterOptions !== '1') {
          this.chatMessages.push({
            id: Math.floor(Math.random() * 1000000).toString(),
            message: data.response,
            userid: '0',
            sign: null,
            sent: (new Date().getTime() / 1000).toString(),
            displayName: null,
            avatar: ''
          });
        }
      }
      if (isPresent(data?.invitedUsers) || isPresent(data?.invitedRoles)) {
        this.fetchRoomUsers('inviteSuccess', { inviteResponseData: data });
      }
    });
    return await modal.present();
  }

  /**
   * Pass params to forward list popup to selected siteId and enable/disable site dropdown
   * @param siteParams siteId, isDisableSite
   * @returns
   */
  async presentForwardModal(siteParams: any): Promise<void> {
    const modal = await this.modalController.create({
      component: ForwardComponent,
      componentProps: { passSiteParams: siteParams }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.forwardData = {};
        this.forwardData.toId = data.toId;
        this.forwardData.forwardUser = data.fromUser;
        if (this.sharedService.getConfigValue(Config.messageForwardingBehaviour) === Constants.messageForwardingBehaviourOptions.userPreference) {
          this.presentForwardBehaviourModal();
        } else {
          this.forwardData.rerouteBehaviour = this.sharedService.getConfigValue(Config.messageForwardingBehaviour);
          this.doForward();
        }
      }
    });
    return await modal.present();
  }

  async presentForwardBehaviourModal(): Promise<void> {
    const modal = await this.modalController.create({
      component: ForwardBehaviourComponent
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.forwardData.rerouteBehaviour = data.forwardBehaviour;
        this.doForward();
      }
    });
    return await modal.present();
  }

  async presentSignatureModal(message: any, i: number): Promise<void> {
    if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
      this.sharedService.lockDevice(Constants.deviceOrientation.landscapePrimary);
    }
    const modal = await this.modalController.create({
      component: SignatureComponent
    });
    modal.onDidDismiss().then(({ data }) => {
      if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
        this.sharedService.unlockDevice();
        if (this.sharedService.platform.is('ios')) {
          this.sharedService.lockDevice(Constants.deviceOrientation.portrait);
          this.sharedService.unlockDevice();
        }
      }
      if (data) {
        this.doSignWithImage(message, i, data.imageData);
      }
    });
    return await modal.present();
  }

  doSignWithImage(message: any, i: number, imageData: string): void {
    const payload = {
      id: message.id,
      sign: imageData
    };
    this.httpService
      .doPost({
        endpoint: APIs.chatSignatureAction,
        payload,
        extraParams: { action: 'uploadSignature' }
      })
      .subscribe((response) => {
        if (response.success) {
          this.chatMessages[i].sign = response.content;
          this.socketService.emitEvent(Socket.signatureToUser, {
            id: message.id,
            sign: this.fullImageUrl(response.content),
            userId: this.sharedService.userData.userId,
            chatroomId: this.roomId
          });
          this.sharedService.trackActivity({
            type: Activity.messaging,
            name: Activity.signMessage,
            des: {
              data: {
                displayName: this.sharedService.userData.displayName,
                messageId: message.id,
                chatroomId: this.roomId
              },
              desConstant: Activity.signMessageDes
            }
          });
        }
      },(error) => {
        const errorMessage =
          error && error.data && error.data.errors && error.data.errors.length && error.data.errors[0].message
            ? error.data.errors[0].message
            : this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
            this.commonService.showToast({ message: errorMessage, color: 'danger' });
      });
  }

  get patientData(): any {
    if (isPresent(this.chatRoomPatientData)) {
      return this.chatRoomPatientData;
    }
    return this.roomUsers.find((data) => Number(data.roleId) === 3);
  }

  patientDisplayNameForTag(patient): string {
    let dobFormatted = null;
    let patientName = '';
    if (patient.caregiver_dob) {
      dobFormatted = moment(patient.caregiver_dob).format('MM/DD/YYYY');
    } else if (patient.dob) {
      dobFormatted = moment(patient.dob).format('MM/DD/YYYY');
    }
    if (this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) !== Constants.workflowAlternateContact) {
      patientName = patient.caregiver_displayname
        ? `${patient.caregiver_displayname} (${patient.displayName})`
        : patient.displayName;
    } else {
      patientName = patient.caregiver_displayname
        ? dobFormatted
          ? `${patient.caregiver_displayname}`
          : `${patient.caregiver_displayname} (${patient.displayName})`
        : patient.displayName;
    }
    return patientName;
  }

  async presentTagModal(message: any = null, i: number = null): Promise<void> {
    const isPatientChat = isPresent(this.selectedMessageData?.patient_data) && isPresent(this.selectedMessageData.patient_data[0]);
    const { allowIntegrationTags, patientTagInfo } = this.getCheckedMessages(true);
    let chatRoomPatient;
    if (isPatientChat) {
      chatRoomPatient = deepCopyJSON(this.getPatientData(this.selectedMessageData));
    } else if (message) {
      chatRoomPatient = this.getPatientData(message);
    } else if (patientTagInfo) {
      chatRoomPatient = allowIntegrationTags
        ? {
            ...patientTagInfo,
            ...patientTagInfo?.patient_data,
            userId: patientTagInfo?.patientId
          }
        : undefined;
    }
    if (chatRoomPatient) {
      chatRoomPatient = {
        ...chatRoomPatient,
        userId: chatRoomPatient?.associatedUserId || chatRoomPatient?.patientId || chatRoomPatient?.userId,
        IdentityValue: chatRoomPatient?.caregiver_identityvalue || chatRoomPatient.IdentityValue,
        displayname: chatRoomPatient?.formattedDisplayName || this.patientDisplayNameForTag(chatRoomPatient),
        passwordStatus: chatRoomPatient?.caregiver_displayname ? chatRoomPatient.caregiver_passwordStatus : chatRoomPatient.passwordStatus,
        dob: chatRoomPatient?.caregiver_dob || chatRoomPatient.dob,
        tenantId: chatRoomPatient?.tenantid
      };
    }
    const allowMultiPatientTagging = !(isPatientChat || chatRoomPatient);
    const admissionDetails =
      this.sharedService.isMultiAdmissionsEnabled && allowIntegrationTags
        ? { admissionId: chatRoomPatient?.admissionId, admissionName: chatRoomPatient?.admissionName }
        : {};
    const componentProps = this.multiTagSelectionIsActive ? { ...this.tagHistory } : { tagedItems: [] };
    const modal = await this.modalController.create({
      component: TagComponent,
      componentProps: {
        ...componentProps,
        admissionDetails,
        message,
        chatRoomPatient,
        allowMultiPatientTagging,
        allowIntegrationTags
      }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        if (data.selectMultiple) {
          this.multiTagSelectionIsActive = true;
          this.getTotalCheckedCount(this.getSelectedTagCount ? undefined : 1);
          if (message) {
            message.checked = true;
          }
          this.tagHistory = data.history;
        } else {
          this.doTagMessages(message, i, data);
        }
      }
    });
    return modal.present();
  }

  cancelMultiTag(): void {
    this.multiTagSelectionIsActive = false;
    this.getSelectedTagCount = undefined;
    this.chatMessages
      .filter((m) => m.checked)
      .map((m) => {
        m.checked = false;
      });
  }

  async presentUserPopover(ev: any): Promise<void> {
    const modal = await this.modalController.create({
      component: ChatRoomUsersComponent,
      initialBreakpoint: 1,
      breakpoints: [0.5, 0.75, 1],
      canDismiss: true,
      backdropDismiss: false,
      id: 'chatroom-users',
      showBackdrop: true,
      handleBehavior: 'cycle',
      componentProps: this.userListComponentProps
    });
    return modal.present();
  }
  get userListComponentProps() {
    return {
      users: [...this.roomUsers, ...this.deletedUsers].sort((a, b) => a.displayName.localeCompare(b.displayName)),
      roomId: this.roomId,
      chatParticipants: this.chatParticipants,
      roleParticipants: this.roleParticipants
    };
  }

  async presentSubjectModal(): Promise<void> {
    if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
      this.sharedService.lockDevice(Constants.deviceOrientation.landscapePrimary);
    }
    const modal = await this.modalController.create({
      component: SubjectPopoverComponent,
      mode: 'ios',
      componentProps: { subject: this.groupSubject } // TODO: Integrate with API
    });
    modal.onWillDismiss().then(({ data }) => {
      if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
        this.sharedService.unlockDevice();
        if (this.sharedService.platform.is('ios')) {
          this.sharedService.lockDevice(Constants.deviceOrientation.portrait);
          this.sharedService.unlockDevice();
        }
      }
      if (isPresent(data)) {
        // TODO: Handle group Id
        this.httpService
          .doPost({
            endpoint: APIs.updateChatSubject,
            payload: { name: data.subject },
            extraParams: { chatroomid: this.roomId },
            contentType: 'form'
          })
          .subscribe((response) => {
            let message = '';
            const trackActivity = {
              des: {
                data: {
                  displayName: this.sharedService?.userData.displayName,
                  subject: data.subject
                },
                desConstant: Activity.updateMessageGroupSubjectDesc
              }
            };
            if (response.status === 1) {
              message = this.commonService.getTranslateData('SUCCESS_MESSAGES.MESSAGE_SUBJECT_UPDATED');
              this.groupSubject = data.subject;
              if (this.messageListIndex > -1) {
                this.sharedService.messageList[this.messageListIndex].chatSubject = data.subject;
              }
              const setMessage = this.commonService.getTranslateDataWithParam('MESSAGES.UPDATE_GROUP_MESSAGE', {
                displayName: this.sharedService?.userData.displayName,
                subject: data.subject
              });
              const messageContent = {
                chatRoomId: this.roomId.toString(),
                data: setMessage,
                insert: true,
                messageType: this.messageType,
                notification: true,
                tenantId: this.sharedService?.userData?.tenantId,
                tenantName: this.sharedService?.userData?.tenantName,
                topic: data.subject
              };
              this.socketService.emitEvent(Socket.userMessagetoServer, messageContent);
            } else {
              message = this.commonService.getTranslateData('ERROR_MESSAGES.FAILED_SUBJECT_UPDATE');
              trackActivity.des.desConstant = Activity.failedUpdateMessageGroupSubjectDesc;
            }
            this.commonService.showMessage(message);
            this.sharedService.trackActivity({
              type: Activity.updateMessageGroupSubjectType,
              name: Activity.updateMessageGroupSubject,
              des: trackActivity.des
            });
          });
      }
    });
    await modal.present();
  }

  async presentReadPopover(ev: any, message): Promise<void> {
    this.sharedService
      .getMessageDeliveredUsers({ messageId: message.id, deliveryStatus: MessageDeliveryStatus.READ, showLoader: false })
      .subscribe(async ({ readUsers }) => {
        const popover = await this.popoverController.create({
          component: ReadPopoverComponent,
          componentProps: { users: readUsers },
          event: ev,
          reference: 'trigger',
          cssClass: 'read-blue-popover',
          mode: 'ios',
          alignment: 'center',
          side: 'bottom'
        });
        return popover.present();
      });
  }
  /**
   * isMessageSending used to identify whether it is failed message or not.
   * If id does not exists or it is in guid format, then it is a failed message.
   * @param id string | number
   * @returns
   */
  isMessageSending(id: string | number): boolean {
    return !id || id.toString().split('-').length > 1;
  }
  async priorityActionSheet(type: string, isFilter = false) {
    const buttons = [];
    if (isFilter) {
      buttons.push({
        text: this.commonService.getTranslateData('OPTIONS.ALL_MESSAGES'),
        handler: () => {
          if (type === this.TYPE.PRIORITY) {
            this.priorityFilterValue = 0;
          } else {
            this.flagFilterValue = Constants.flagTypes.noFlag;
          }
          this.fetchChatMessages();
        }
      });
    }
    buttons.push(
      ...[
        {
          text: this.commonService.getTranslateData(type === this.TYPE.PRIORITY ? 'PRIORITIES.HIGH' : 'OPTIONS.HIGH'),
          handler: () => {
            if (isFilter) {
              if (type === this.TYPE.PRIORITY) {
                this.priorityFilterValue = MessagePriority.HIGH;
              } else {
                this.flagFilterValue = Constants.flagTypes.high;
              }
              this.fetchChatMessages();
            } else {
              this.messagePriority = MessagePriority.HIGH;
            }
          }
        },
        {
          text: this.commonService.getTranslateData(type === this.TYPE.PRIORITY ? 'PRIORITIES.NORMAL' : 'OPTIONS.MEDIUM'),
          handler: () => {
            if (isFilter) {
              if (type === this.TYPE.PRIORITY) {
                this.priorityFilterValue = MessagePriority.NORMAL;
              } else {
                this.flagFilterValue = Constants.flagTypes.medium;
              }
              this.fetchChatMessages();
            } else {
              this.messagePriority = MessagePriority.NORMAL;
            }
          }
        },
        {
          text: this.commonService.getTranslateData(type === this.TYPE.PRIORITY ? 'PRIORITIES.LOW' : 'OPTIONS.LOW'),
          handler: () => {
            if (isFilter) {
              if (type === this.TYPE.PRIORITY) {
                this.priorityFilterValue = MessagePriority.LOW;
              } else {
                this.flagFilterValue = Constants.flagTypes.low;
              }
              this.fetchChatMessages();
            } else {
              this.messagePriority = MessagePriority.LOW;
            }
          }
        },
        {
          text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
          role: 'cancel',
          data: {
            action: 'cancel'
          }
        }
      ]
    );
    let header = '';
    if (type === this.TYPE.PRIORITY) {
      header = this.commonService.getTranslateData(isFilter ? 'TITLES.FILTER_MESSAGES_BY_PRIORITY' : 'TITLES.CHOOSE_PRIORITY');
    } else {
      header = this.commonService.getTranslateData('TITLES.FILTER_MESSAGES_BY_FLAG');
    }
    const actionSheet = await this.actionSheetController.create({
      header,
      mode: 'ios',
      cssClass: 'action-sheets-more-in-chat',
      buttons
    });
    await actionSheet.present();
  }

  processFiles(data): void {
    if (data) {
      if (this.attachedFile.length > Constants.maxFileLength) {
        const fileLengthErrorMessage = this.commonService.getTranslateDataWithParam(
          'ERROR_MESSAGES.MAX_FILE_LENGTH',
          { maxFileLength: Constants.maxFileLength }
        );
        this.commonService.showMessage(fileLengthErrorMessage);
      } else {
        data?.file?.forEach((e: any) => {
          if (this.sharedService.checkAttachmentCount(this.attachedFile.length + 1, Constants.maxFileLength)) {
            return;
          }
          this.allowedFileTypes = Constants.allowedBulkMessageFileTypes;
          if (e.size > Constants.maxFileUploadSize) {
            const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
              maxFileSize: Constants.maxFileSize
            });
            this.commonService.showMessage(sizeErrorMessage);
          } else {
            const fileName = e.name;
            const fileSize = e.size;
            const getFileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
            const pos = this.allowedFileTypes.indexOf(getFileExt);
            if (pos < 0) {
              this.fileFormat = false;
              this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.FILE_FORMAT'));
            } else if (fileSize > Constants.maxFileUploadSize) {
              this.fileSize = false;
              const sizeErrorMessage = this.commonService.getTranslateDataWithParam(
                'ERROR_MESSAGES.FILE_UPLOADSIZE',
                { maxFileSize: Constants.maxFileSize }
              );
              this.commonService.showMessage(sizeErrorMessage);
            } else {
              const id = messageCenterGUID(this.roomId, this.sharedService.userData.userId);
              this.fileFormat = true;
              this.fileSize = true;
              e.id = id;
              e.temp_id = id;
              const reader = getFileReader();
              reader.readAsDataURL(e); // read file as data url
              reader.onload = (event: any) => {
                // called once readAsDataURL is completed
                if (event && event.target) {
                  const length = this.filesInMemory.push({
                    data: event.target.result,
                    ext: '',
                    fileName
                  });
                  this.filesInMemory[length - 1].ext = this.commonService.getFileType(e.type);
                }
                this.cd.detectChanges();
              };
              this.bulkMessageService.fileFormatTypeTofileTag(e, '', '', '', '', (result) => {
                e.imageHtml = result;
                this.messageDataLocal = `${this.messageDataLocal}<br>${result}`;
              });
              this.attachedFile[this.attachedFile.length] = e;
            }
          }
        });
      }
    }
  }

  selectedFile(event: any): void {
    const chooseFiles = [];
    const allowedFileTypes = this.sharedService.appConfig?.configurations?.allowedFileFormat;
    const errorFIles = [];
    if (event.target.files && event.target.files[0]) {
      if (event.target.files.length > Constants.maxFileLength) {
        const fileLengthErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH', {
          maxFileLength: Constants.maxFileLength
        });
        this.commonService.showMessage(fileLengthErrorMessage);
      } else {
        Array.from(event.target.files).forEach((e: any, index: number) => {
          if (e.size > Constants.maxFileUploadSize) {
            const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
              maxFileSize: Constants.maxFileSize
            });
            this.commonService.showMessage(sizeErrorMessage);
          } else {
            const fileName = e.name;
            const fileSize = e.size;
            const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
            const pos = allowedFileTypes?.indexOf(ext);
            if (pos < 0) {
              errorFIles.push(e.name);
            } else if (fileSize > Constants.maxFileUploadSize) {
              const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
                maxFileSize: Constants.maxFileSize
              });
              this.commonService.showMessage(sizeErrorMessage);
            } else {
              chooseFiles.push(e);
            }
            if (index === event.target.files.length - 1) {
              this.processFiles({ file: chooseFiles });
            }
          }
        });
        if (errorFIles.length) {
          this.commonService.showMessage(
            this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_TYPE_NOT_ALLOWED', {
              errorFileName: errorFIles?.join()
            })
          );
        }
      }
    }
  }

  fileUpload(uniqueId?: string): any {
    const filesAttached = this.attachedFile;
    let index;
    if (uniqueId) {
      index = this.chatMessages.findIndex((x) => x.uniqueId === uniqueId);
    }
    this.httpService
      .fileUpload({
        endpoint: `${APIs.messageFileUpload}`,
        payload: filesAttached,
        field: 'file[]',
        id: this.sharedService?.userData.userId, // TODO: Without passing "userId" api return error
        page: 'chat',
        multiple: true,
        loader: false
      })
      .subscribe(
        (res) => {
          if (res[0].success) {
            res.forEach((e) => {
              this.resData.push({
                details: e,
                id: e.id,
                name: e.msg,
                format: e.view
              });
            });
            this.selectedFileNames.push(this.resData);
            this.resData = [];
            this.fileUploadToCmis(filesAttached, uniqueId);
          } else {
            this.commonService.showMessage(res[0].msg || this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'));
          }
        },
        () => {
          this.commonService.showToast({
            message: this.commonService.getTranslateData('ERROR_MESSAGES.FILE_UPLOAD_FAILED_SEND_AGAIN'),
            color: 'danger'
          });
          if (index > -1) {
            this.chatMessages.splice(index, 1);
          }
          this.filesInMemory = [];
          this.filesInMemoryOffline = [];
          this.attachedFile = [];
        }
      );
  }
  fileUploadToCmis(filesAttached: any, uniqueId?: string): void {
    const { fileUniqueName } = Constants;
    let pollingData = '';
    let pollingDataWithDownload = '';
    this.metaDataSet(this.selectedFileNames[0].chatRoomId, (data) => {
      let metaData = '';
      metaData = JSON.stringify(data);
      this.cmisFileUploadData = [];
      this.graphqlService.uploadFileToCmis(fileUniqueName, metaData).subscribe((filedata) => {
        let cmisFileData: any = filedata;
        this.sharedService.trackActivity({
          type: Activity.communication,
          name: Activity.fileUpload,
          des: Activity.fileUploadDes
        });
        cmisFileData = JSON.parse(cmisFileData.data.uploadFileToCmis.cmisFileData);
        Object.keys(cmisFileData).map((e, i) => {
          const parsedData = JSON.parse(cmisFileData[e]);
          parsedData.status === Constants.apiStatusCode
            ? this.cmisFileUploadData.push({
              result: parsedData.results
            })
            : '';
        });
        const downloads = [];
        this.cmisFileUploadData.forEach((e, index) => {
          if (e.result.organizationId) {
            const { fileType } = e.result.attributes.data[0];
            const { fileUrl } = this.bulkMessageService.getFileUrl(fileType, e);
            const fileName = e.result.attributes.data[0].displayName;
            this.bulkMessageService.fileFormatTypeTofileTag(
              filesAttached[index],
              fileUrl,
              fileName,
              fileType,
              filesAttached[index].type,
              (result) => {
                let downloadIconHtml = '';
                if (!isBlank(result.downloadIcon)) {
                  downloadIconHtml = result.downloadIcon;
                }
                this.messageData += `<br>${result.message}`;
                downloads.push(`${result.message} ${downloadIconHtml}`);
              }
            );
            pollingData = this.messageLocal
              ? `${this.messageData} <br> ${Constants.attachmentStartTag} ${unescape(this.messageLocal)} ${Constants.attachmentEndTag}`
              : `${this.messageData} <br> ${Constants.attachmentStartTag}${Constants.attachmentEndTag}`;
            pollingDataWithDownload = this.messageLocal
              ? `${downloads.join('<br>')} <br> ${Constants.attachmentStartTag} ${unescape(this.messageLocal)} ${Constants.attachmentEndTag}`
              : `${downloads.join('<br>')} <br> ${Constants.attachmentStartTag}${Constants.attachmentEndTag}`;
          }
        });
        const unique = uniqueId ? uniqueId : messageCenterGUID(this.roomId, this.sharedService.userData.userId);
        let userMessagetoServerData: UserMessagetoServer = {
          data: pollingData,
          chatroomId: this.roomId.toString(),
          type: Constants.messageInputType.attachment,
          id: unique,
          uniqueId: unique,
          insert: true,
          language: navigator.language,
          userId: this.sharedService.userData.userId,
          priorityId: this.messagePriority
        };
        if (uniqueId) {
          const index = this.chatMessages.findIndex((x) => x.uniqueId === unique);
          if (index > -1) {
            this.chatMessages[index].message = pollingDataWithDownload;
            this.chatMessages[index].uploading = false;
          }
        }
        userMessagetoServerData = this.messageCenterService.setUserMessageServerData(
          userMessagetoServerData,
          this.selectedMessageData,
          this.chatMessages
        );
        this.messageCenterService.addToFailedMessages([userMessagetoServerData], this.roomId);
        this.socketService.emitEvent(Socket.userMessagetoServer, userMessagetoServerData);
        this.filesInMemory = [];
        this.filesInMemoryOffline = [];
        this.messageData = '';
        this.message = '';
        this.messageLocal = '';
        this.messagePriority = MessagePriority.NORMAL;
      });
    });
    const notificationData = {
      sourceId: Constants.sourceId.message.toString(),
      sourceCategoryId: Constants.sourceCategoryId.messageSendNotification
    };
    const pushMessage = this.commonService.getTranslateData('MESSAGES.NOTIFICATION_MESSAGE');
    this.sentPushNotification(pushMessage, '', '', this.messagePriority, notificationData);
  }
  sentPushNotification(pushMessage: string, chatRoomOrToId:any = '', chatRoomType: string = '', priorityId: number = MessagePriority.NORMAL, notificationData: object = {}): void {
    const userDetails = {
      fromName: this.sharedService.userData.displayName,
      userid: this.sharedService.userData.userId
    };
    // Passing last message user details
    const lastMessage = this.getLastMessage();
    if (chatRoomType === Constants.chatRoomActionType.forward || chatRoomType === Constants.chatRoomActionType.invite) {
      userDetails.fromName = lastMessage.displayName;
      userDetails.userid = String(lastMessage.userid);
    }
    this.selectedMessageData['selectedTenantId'] = String(lastMessage?.tenantid);

    // TODO: need to clarify about showDoubleVerificationStatus
    const deepLinking: any = {
      roomId: this.roomId,
      messageType: this.messageType,
      baseId: this.baseId,
      messageGroupId: this.messageGroupId,
      createdby: this.createdUser,
      chatroomid: this.roomId,
      selectedTenantId: this.selectedMessageData?.selectedTenantId,
      priorityId
    };
    const pushParams = {
      chatRoomOrToId: chatRoomOrToId ? chatRoomOrToId : this.roomId,
      userId: this.forwardData || chatRoomType === Constants.chatRoomActionType.invite
        ? Constants.configFalse
        : this.sharedService.userData.userId,
      pushMessage,
      privilegeKey: '',
      showDoubleVerificationStatus: this.showDoubleVerificationStatus
    };
    this.sharedService.inviteAndForwardPushNotification(deepLinking, userDetails, pushParams, notificationData);

    this.resData = [];
    this.selectedFileNames = [];
    this.filesInMemory = [];
    this.attachedFile = [];
  }
  metaDataSet(chatRoomId: number, callBack: any): any {
    const metaData = {};
    if (this.selectedFileNames && this.selectedFileNames.length) {
      this.selectedFileNames[0].forEach((e, i) => {
        metaData[i] = {
          attributes: {
            data: [
              {
                userId: this.sharedService.userData.userCmisId,
                actualUserId: this.sharedService.userData.userId,
                owner: this.sharedService.userData.userCmisId,
                userType: Constants.messageUserType,
                objectType: Constants.objectType,
                fileType: e.format,
                displayName: this.attachedFile[i].name,
                parentFolderId: Constants.parentFolderId,
                isDeleted: false,
                createdOn: new Date().getTime(),
                modifiedOn: new Date().getTime(),
                chatRoomId: this.roomId ? this.roomId : Constants.defaultChatRoomId,
                chatType: this.messageGroupId ? Constants.groupChat : Constants.normalChat,
                sourceType: Constants.fileSourceType,
                fileOriginalName: e.name
              }
            ]
          }
        };
      });
    }
    callBack(metaData);
  }

  removeFileFromArray(index: number): void {
    this.commonService
      .showAlert({
        message: 'MESSAGES.REMOVE_ATTACHMENT',
        header: 'MESSAGES.ARE_YOU_SURE'
      })
      .then((confirmation) => {
        if (confirmation) {
          this.filesInMemory.splice(index, 1);
          this.attachedFile.splice(index, 1);
        }
      });
  }

  longPressOptions(message: any, i: number): void {
    if (!this.multiTagSelectionIsActive) {
      this.presentActionSheet(message, i);
    }
  }

  async presentActionSheet(message: any, i: number): Promise<void> {
    const buttonList: any = [
      {
        text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
        role: 'cancel',
        id: 'cancel'
      }
    ];
    if (Number(message.userid) === this.currentUser && message.messageStatus) {
      buttonList.unshift({
        text: this.commonService.getTranslateData('BUTTONS.DELETE_MESSAGE'),
        id: 'delete',
        cssClass: isBlank(message.tagedItems) ? 'delete-message' : 'delete-message-disabled',
        handler: () => {
          if(isBlank(message.tagedItems)){
            this.deleteUndoMessageConfirm(message, i, 'delete');
          } else {
            this.commonService.showToast({
              message: this.commonService.getTranslateData('MESSAGES.DELETE_TAGGED_MESSAGE_INFO'),
              color: 'dark'
            });
          }
        }
      });
    } 
    if (this.sharedService.platformValue !== Constants.platform.web) {
      buttonList.unshift({
        text: this.commonService.getTranslateData('BUTTONS.COPY_MESSAGE'),
        handler: () => {
          this.copyToClipBoard(message);
        }
      });
    }
    if (message.sign === this.falseAsString && Number(message.userid) === this.currentUser && this.enableSign) {
      buttonList.unshift({
        text: this.commonService.getTranslateData('BUTTONS.SIGN_MESSAGE'),
        handler: () => {
          this.presentSignatureModal(message, i);
        }
      });
    }
    if (this.enableTagging) {
      buttonList.unshift({
        text: this.commonService.getTranslateData('BUTTONS.TAG_MESSAGE'),
        id: 'tag',
        handler: () => {
          this.presentTagModal(message, i);
        }
      });
    }
    // Hide reply button for broadcast messages
    if (!this.isBroadcast) {
      buttonList.unshift({
        text: this.commonService.getTranslateData('BUTTONS.REPLY'),
        id: 'reply',
        handler: () => {
          this.replyMessage(message, i);
        }
      });
    }    
    if (buttonList.length > 1) {
      const actionSheet = await this.actionSheetController.create({
        mode: 'ios',
        buttons: buttonList
      });
      if(message.messageStatus === 1 || !message.hasOwnProperty('messageStatus')) {
        await actionSheet.present();
      }
    }
  }

  copyToClipBoard(message: any): void {
    this.clipboard.copy(this.textFromHtml(message.message));
  }

  // Remove html tags from copied message
  textFromHtml(str): string {
    const removeHtmlTag = Constants.removeHtmlTags;
    for (let index = 0; index < removeHtmlTag.length; index++) {
      const element = removeHtmlTag[index];
      str = str.replace(element.value, element.replaceWith);
    }
    return str;
  }

  doForward(): void {
    this.messageCenterService.doForwardMessage({ forwardData: { ...this.forwardData, chatroomId: this.roomId } }, ({ success }) => {
      if (success) {
        this.fetchRoomUsers();
        if (this.messageListIndex > -1) {
          if (this.forwardData.rerouteBehaviour === Constants.messageForwardingBehaviourOptions.keepForwarder) {
            const forwardedLabel = this.commonService.getTranslateData('LABELS.FORWARDED_TO');
            this.sharedService.messageList[this.messageListIndex].messageForwarded = `${forwardedLabel} ${this.forwardData.forwardUser}`;
          } else {
            this.sharedService.messageList.splice(this.messageListIndex, 1);
          }
        }
        const pushMessage = this.commonService.getTranslateData('MESSAGES.CHAT_FORWARD_NOTIFICATION');
        const notificationDataRecipient = {
          sourceId: Constants.sourceId.other.toString(),
          sourceCategoryId: Constants.sourceCategoryId.userForwardNotificationForRecipient
        };
        this.sentPushNotification(pushMessage, this.forwardData.toId, Constants.chatRoomActionType.forward, undefined, notificationDataRecipient);
      }
    });
  }

  removeSign(message: any, i: number): void {
    const url = APIs.chatSignatureAction;
    this.httpService
      .doPost({
        endpoint: url,
        payload: message,
        extraParams: { action: 'removeSignature' },
        contentType: 'form'
      })
      .subscribe(() => {
        this.chatMessages[i].sign = this.falseAsString;
        this.socketService.emitEvent(Socket.removeSignature, {
          id: message.id,
          userId: this.sharedService.userData.userId,
          chatroomId: this.roomId.toString()
        });
        this.sharedService.trackActivity({
          type: Activity.messaging,
          name: Activity.removeSignMessage,
          des: {
            data: {
              displayName: this.sharedService.userData.displayName,
              messageId: message.id,
              chatroomId: this.roomId
            },
            desConstant: Activity.removeSignMessageDes
          }
        });
      },(error) => {
        const errorMessage =
          error && error.data && error.data.errors && error.data.errors.length && error.data.errors[0].message
            ? error.data.errors[0].message
            : this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
            this.commonService.showToast({ message: errorMessage, color: 'danger' });
      });
  }

  scrollToBottom(): void {
    setTimeout(() => {
      this.content.scrollToBottom();
      this.cd.detectChanges();
    }, 100);
  }
  maskedMessageReply(): void {
    const patientReplyData = {
      chatroomId: this.roomId ? this.roomId.toString() : this.sharedService.roomID.toString(),
      language: navigator.language ? navigator.language : ''
    };
    const apiURL = APIs.maskedMessageReply;
    this.httpService.doPost({ endpoint: apiURL, payload: patientReplyData }).subscribe((res) => {
      if (this.sharedService.messageList) {
        if (this.messageListIndex !== -1) {
          this.sharedService.messageList.splice(this.messageListIndex, 1);
          this.sharedService.messageListUpdated.next(null);
        }
        this.sharedService.addNewChatFromAPI(res.chatroomId.toString());
      }
      this.sharedService.updateChatRoomId(this.sharedService.roomID, res.chatroomId);
      this.router.navigate([`message-center/messages/active/chat/${res.chatroomId}`]);
    });
    this.canReplyForMaskedMessage = false;
  }
  sendMessage(): void {
    this.message = this.formatMessage(this.message.replace(/\n/g, Constants.break))
    if (this.filesInMemory.length) {
      this.messageLocal = this.message;
      this.message = '';
      this.filesInMemoryOffline = JSON.parse(JSON.stringify(this.filesInMemory));
      this.filesInMemory = [];
      this.messageDataLocal = `${this.messageDataLocal} ${Constants.break} ${Constants.attachmentStartTag} ${unescape(
        this.messageLocal
      )} ${Constants.attachmentEndTag}`;
      const uniqueId = messageCenterGUID(this.roomId, this.sharedService.userData.userId);
      this.chatMessages.push({
        message: this.messageDataLocal,
        avatar: this.sharedService.userData.profileImageUrl,
        sent: String(new Date().getTime() / 1000),
        sign: this.falseAsString,
        id: uniqueId,
        uniqueId,
        userid: this.sharedService.userData.userId,
        uploading: true,
        readUsers: [],
        messageDeletedTime: null,
        messageStatus: 1,
        displayName: this.sharedService.userData.displayName,
        priorityId: this.messagePriority
      });
      this.messageDataLocal = '';
      this.fileUpload(uniqueId);
    } else {
      const lastMessage = this.getLastMessage();
      this.chatMessages = this.messageCenterService.sendMessage(
        this.message,
        this.roomId,
        this.messagePriority,
        this.selectedMessageData,
        this.chatMessages,
        {
          patientSiteId: this.patientSiteId,
          selectedTenantId: String(lastMessage?.tenantid),
          title: this.title,
          messageGroupId: this.messageGroupId,
          baseId: this.baseId,
          messageType: this.messageType,
          doubleVerificationStatus: this.showDoubleVerificationStatus
        },
        this.getMentionedUsers(this.message),
        this.getRepliedTo(this.message)
      );
      this.message = '';
      this.messagePriority = MessagePriority.NORMAL;
    }
    this.scrollToBottom();
    this.addActiveMention();
  }

  fullImageUrl(partialUrl: string): string {
    return partialUrl.includes(this.imageUrlPrefix) ? partialUrl : `${this.imageUrlPrefix}${partialUrl}`;
  }

  presentActionSheetForFlag(message: any, i: number, event: any): void {
    message.chatroomId = this.roomId.toString();
    event.close();
    this.sharedService.presentActionSheet({ message, index: i, flagApiType: 'msg' }, (flagType: number) => {
      this.chatMessages[i].msg_flag = flagType.toString();
      this.isChatUpdated = true;
    });
  }

  getFlagClass(flagKey: string): string {
    return this.sharedService.getFlagType(Number(flagKey));
  }

  async presentEmojiPopover(ev: any): Promise<void> {
    this.isEmojiPopupOpen = true;
    const popover = await this.popoverController.create({
      component: EmojiPopoverComponent,
      componentProps: { users: this.roomUsers },
      event: ev,
      cssClass: 'common-emoji-popover',
      mode: 'ios'
    });

    popover.onWillDismiss().then(({ data }) => {
      this.hidePopup();
      if (data) {
        this.setMessage(data);
      }
    });
    return await popover.present();
  }

  setMessage(data: any) {
    this.message = `${this.message}${data.emoji}`;
  }

  hidePopup() {
    this.isEmojiPopupOpen = false;
  }

  loadMoreMessages(): void {
    this.pageNumber++;
    this.fetchChatMessages(true, false);
  }

  doTagMessages(message: any, i: number, tagData: any): void {
    if (tagData.selectedTags.length === 0) return;
    const allExistingTags = [];
    const selectedMessages = this.multiTagSelectionIsActive ? this.chatMessages.filter((m) => m.checked) : [message];
    selectedMessages.forEach((msg: any) => {
      if (!isBlank(msg?.tagedItems)) {
        const existingTags = tagData.selectedTags.filter((a: any) => {
          const indexFound = msg.tagedItems.findIndex((b: any) => Number(b.id) === Number(a.id));
          return indexFound > -1;
        });
        existingTags.forEach((item) => {
          if (allExistingTags.findIndex((tag: any) => tag.name === item.name) === -1) {
            allExistingTags.push(item);
          }
        });
      }
    });
    if (allExistingTags.length > 0) {
      this.commonService.showMessage(
        this.commonService.getTranslateDataWithParam('MESSAGES.TAGS_ALREADY_EXISTS', {
          tags: allExistingTags.map((tag) => tag.name).join(', ')
        })
      );
    } else {
      this.processMessagesTagging(message, i, tagData);
    }
  }

  processMessagesTagging(message: any, i: number, tagData: any) {
    // TODO: Need to handle multiple message tagging
    const tagIds = [];
    tagData.selectedTags.map((ele) => tagIds.push(Number(ele.id)));
    const chatroomId = this.roomId || this.sharedService.roomID;
    const payload = {
      chatroomIds: [chatroomId],
      single: tagData.selectedUser ? tagData.selectedUser.id : '0',
      multiple: tagIds.join(';'),
      id: this.multiTagSelectionIsActive ? this.chatMessages.filter((m) => m.checked).map((m) => m.id) : [message.id],
      timezone: this.sharedService.getConfigValue(Config.tenantTimezoneName),
      config_identity_value: this.sharedService.getConfigValue(Config.esiCodeForPatientIdentity),
      config_staff_identity: this.sharedService.getConfigValue(Config.esiCodeForStaffIdentity),
      config_staff_name: this.sharedService.getConfigValue(Config.esiCodeForStaffName),
      enable: this.sharedService.getConfigValue(Config.enableProgressNoteIntegration),
      admissionId: this.sharedService.isMultiAdmissionsEnabled ? tagData.selectedUser?.admissionId : undefined
    };
    this.httpService
      .doPost({
        endpoint: APIs.chatSignatureAction,
        payload,
        extraParams: { action: 'addchattags' }
      })
      .subscribe(
        (response) => {
          if (response.success) {
            if (tagData?.patientDetails) {
              this.chatRoomPatientData = { ...tagData.patientDetails, displayName: tagData.patientDetails?.displayname };
            }
            this.isChatUpdated = true;
            let patientTagInfo;
            this.chatMessages.forEach((msg, index) => {
              if (payload.id.includes(msg.id)) {
                this.chatMessages[index].tag = this.trueAsString;
                this.chatMessages[index].tagedItems = this.chatMessages[index]?.tagedItems
                  ? [...this.chatMessages[index].tagedItems, ...tagData.selectedTags]
                  : tagData.selectedTags;
                this.chatMessages[index].tagSign = response.approvalRequired ? this.falseAsString : this.trueAsString;
                if (+tagData.selectedUser?.id > 0) {
                  this.chatMessages[index].patient = tagData.selectedUser?.id;
                  this.chatMessages[index].pdisplayname = tagData.selectedUser.displayName;
                  this.chatMessages[index].patientTagInfo = {
                    patientId: this.chatMessages[index].patient,
                    admissionId: payload.admissionId || undefined,
                    patient_data: {
                      ...tagData.selectedUser,
                      ...tagData.patientDetails,
                      userId: tagData.selectedUser.id
                    }
                  };
                  patientTagInfo = this.chatMessages[index].patientTagInfo;
                }
              }
            });
            this.socketService.emitEvent(Socket.updateMessageTags, {
              autoApprove: response.approvalRequired,
              tagNames: tagData.selectedTags,
              messageIds: this.multiTagSelectionIsActive ? this.chatMessages.filter((m) => m.checked).map((m) => m.id) : [message.id],
              assosiatedPatient: tagData.patientDetails,
              ...(patientTagInfo ? { patientTagInfo } : {}),
              currentChatroomId: String(this.roomId),
              updatedBy: this.sharedService.userData.userId
            });
            let responseMessage;
            if (response.approvalRequired) {
              responseMessage = this.commonService.getTranslateData('MESSAGES.MESSAGE_TAGGED_SENT_FOR_APPROVAL');
              /**
               *Send push notification to approval required tag
               */
              this.sharedService.sentPushNotification(
                this.roomId.toString(),
                this.sharedService.userData.userId,
                this.commonService.getTranslateData('MESSAGES.APPROVAL_MESSAGE'),
                'formApprover',
                '',
                ''
              );
            } else {
              responseMessage = this.commonService.getTranslateData('MESSAGES.MESSAGE_TAGGED_AUTO_APPROVED');
            }
            this.commonService.showMessage(responseMessage);
            if (this.multiTagSelectionIsActive) {
              this.chatMessages
                .filter((m) => m.checked)
                .map((m) => {
                  m.checked = false;
                });
              this.multiTagSelectionIsActive = false;
            }
          } else {
            this.commonService.showMessage(response.error);
          }
        },
        (error) => {
          const errorMessage =
            error && error.data && error.data.errors && error.data.errors.length && error.data.errors[0].message
              ? error.data.errors[0].message
              : this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
          this.commonService.showToast({ message: errorMessage, color: 'danger' });
        }
      );
  }

  setTagInMessage(message: any, tagData: any, response: any): void {
    message.tag = this.trueAsString;
    message.patient = tagData.selectedUser ? tagData.selectedUser.id : null;
    message.pdisplayname = tagData.selectedUser ? tagData.selectedUser.displayName : '';
    message.tagedItems = message.tagedItems ? [...message.tagedItems, ...tagData.selectedTags] : tagData.selectedTags;
    message.tagSign = response.approvalRequired === this.trueAsString ? this.falseAsString : this.trueAsString;
  }

  async goToVideoChat(): Promise<void> {
    this.videoCallService.iosAudioPreLoad().then(() => {
      let videoButtons = [];
      if (this.joinChat) {
        this.connectVideo();
      } else if (this.sharedService.isEnableConfig(Config.enableApplessVideoChat)) {
        if (this.chatWithUserType === Constants.enrolledUser) {
          videoButtons.push({ text: this.commonService.getTranslateData('BUTTONS.IN_APP'), confirm: 'In-App', role: 'In-App' });
          videoButtons.push({ text: this.commonService.getTranslateData('BUTTONS.APP_LESS'), confirm: 'Appless', role: 'Appless' });
        } else {
          videoButtons.push({ text: this.commonService.getTranslateData('BUTTONS.APP_LESS'), confirm: 'Appless', role: 'Appless' });
        }
        this.commonService
          .showAlert({
            message: 'MESSAGES.VIDEO_CHAT_INITIATE',
            header: 'MESSAGES.VIDEO_CHAT',
            buttons: videoButtons,
            cssClass: 'send-form-via-modal show-button-center',
            backDrop: true
          })
          .then((confirmation) => {
            if (confirmation === 'Appless') {
              this.videoCallService.isAppLessWorkFlow = true;
            } else {
              this.videoCallService.isAppLessWorkFlow = false;
            }
            this.connectVideo();
          });
      } else {
        this.connectVideo();
      }
    });
  }

  connectVideo() {
    if (this.sharedService.platform.is('capacitor')) {
      this.videoCallPermissionService.checkMicroPhoneAuthorizationStatus((audioStatus: boolean) => {
        if (audioStatus) {
          this.videoCallPermissionService.checkCameraPermission((cameraStatus: boolean) => {
            if (cameraStatus) {
              this.initializeVideo();
            }
          });
        }
      });
    } else {
      this.initializeVideo();
    }
  }

  setVideoCallParams() {
    const chatroomId = this.roomId || this.sharedService.roomID;
    const lastMessage = this.getLastMessage();
    this.videoCallService.onGoingChatroom = String(this.roomId);
    this.sharedService.roomId = String(this.roomId);
    if (this.joinChat) {
      this.setVideoCallValues();
      this.videoCallService.videoConnectOption.host = this.videoCallRoomData ? this.videoCallRoomData.host : VideoCall.vidyoHost;
      this.videoCallService.videoConnectOption.roomEntity = this.videoCallRoomData.roomEntity;
      this.videoCallService.videoConnectOption.roomKey = this.videoCallRoomData.roomKey;
      this.videoCallService.videoConnectOption.roomName = this.videoCallRoomData.roomName;
      this.videoCallService.videoConnectOption.roomPin = this.videoCallRoomData.roomPin;
      this.videoCallService.connectOptions.name = this.sharedService.userData.displayName;
      this.videoCallService.connectOptions.portal = this.videoCallService.videoConnectOption.host;
      this.videoCallService.connectOptions.roomKey = this.videoCallService.videoConnectOption.roomKey;
      this.videoCallService.connectOptions.pin = this.videoCallService.videoConnectOption.roomPin;
      this.setVideoConnectData(lastMessage, () => {
        this.videoCallService.videoConnect.next(this.videoCallService.videoConnectOption);
        this.videoCallService.joinVideoCall(false);
        this.videoCallService.joinVideoCall(true);
      });
    } else {
      this.sharedService.isLoading = true;
      this.videoCallService.videoRoomCreate(chatroomId)?.subscribe(
        (response: any) => {
          this.sharedService.isLoading = false;
          const responseData = response.data.createVidyoRoom;
          if (response && responseData.roomKey !== null && responseData.roomName !== null) {
            this.videoCallService.participant = false;
            // pass data for app less
            this.videoCallService.videoConnectOption.host = responseData ? responseData.host : VideoCall.vidyoHost;
            this.videoCallService.videoConnectOption.roomEntity = responseData.roomEntity;
            this.videoCallService.videoConnectOption.roomKey = responseData.roomKey;
            this.videoCallService.videoConnectOption.roomName = responseData.roomName;
            this.videoCallService.videoConnectOption.roomPin = responseData.roomPin;
            this.videoCallService.connectOptions.name = this.sharedService.userData.displayName;
            this.videoCallService.connectOptions.portal = this.videoCallService.videoConnectOption.host;
            this.videoCallService.connectOptions.roomKey = responseData.roomKey;
            this.videoCallService.connectOptions.pin = responseData.roomPin;
            this.setVideoConnectData(lastMessage, () => {
              if (this.videoCallService.isAppLessWorkFlow) {
                const params = {
                  chatroomId: this.roomId || this.sharedService.roomID,
                  ...responseData,
                  ...this.videoCallService.videoConnectOption
                };

                this.sendUrlToAppLessVideo(params);
              } else {
                this.videoCallService.videoConnect.next(this.videoCallService.videoConnectOption);
              }
            });
          } else {
            this.commonService.showMessage(this.commonService.getTranslateData('MESSAGES.VIDEO_CREATION_FAILED'));
          }
        },
        () => {
          this.sharedService.isLoading = false;
        }
      );
    }
  }

  initializeVideo() {
    if (this.sharedService.platform.is('ios') && this.sharedService.platform.is('capacitor')) {
      this.setVideoCallParams();
    } else {
      this.videoCallService.createVcConnector((callBackVal: boolean) => {
        if (callBackVal) {
          this.setVideoCallParams();
        } else {
          this.commonService.showToast({
            message: this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
            color: 'danger'
          });
        }
      });
    }
  }

  setVideoConnectData(lastMessage, callBack: any) {
    this.videoCallService.videoConnectOption.activeMessage = lastMessage;
    this.videoCallService.videoConnectOption.token = getValueFromSession(Constants.storageKeys.vidyoToken);
    this.videoCallService.videoConnectOption.displayName = this.sharedService.userData.displayName;
    this.videoCallService.videoConnectOption.resourceId = this.videoCallService.onGoingChatroom.toString();
    this.videoCallService.videoConnectOption.sendEvent = true;
    this.videoCallService.videoConnectOption.users = this.roomUsers;
    this.videoCallService.videoConnectOption.avatarBasePath = this.sharedService.userData.avatar;
    this.videoCallService.videoConnectOption.userData = this.sharedService.userData;
    this.videoCallService.videoConnectOption.joinChat = this.joinChat;
    this.videoCallService.videoConnectOption.applessVideo = this.videoCallService.isAppLessWorkFlow;
    // For ios navigate to video call page, show camera in full screen
    if (this.sharedService.platform.is('ios') && this.sharedService.platform.is('capacitor')) {
      this.router.navigateByUrl('video-call');
    } else {
      this.sharedService.resetSessionTimeout(false);
    }

    setTimeout(() => {
      callBack(true);
    }, 1500);
  }

  setVideoCallValues() {
    // Use video call page instead of video-component
    this.videoCallService.participant = true;
    this.videoCallService.joinChat = true;
    if (this.sharedService.platform.is('capacitor') && this.sharedService.platform.is('ios')) {
      this.videoCallService.sharedService.videoCall = false;
    } else {
      this.videoCallService.sharedService.videoCall = true;
    }
  }

  sendUrlToAppLessVideo(params) {
    this.sharedService.isLoading = true;
    this.graphqlService.sendAppLessVideoData(params)?.subscribe(
      (response: any) => {
        this.sharedService.isLoading = false;
        if (response.data) {
          this.videoCallService.videoID = response.data.sendApplessData || 0;
          this.videoCallService.videoConnect.next(params);
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }

  changeChatFilter(value): void {
    this.filterOptions = value;
    if (!(isBlank(this.filteredTags) && this.priorityFilterValue === 0 && this.flagFilterValue === 0 && !this.showMentionMessages)) {
      if (this.filterOptions === '0') {
        this.priorityFilterValue = 0;
        this.filteredTags = [];
        this.flagFilterValue = 0
        this.showMentionMessages = false;
        this.commonService.showToast({
          message: this.commonService.getTranslateData('MESSAGES.FILTER_SET_TO_ALL'),
          color: 'warning'
        });
      }
    }

    this.fetchChatMessages();
  }
  enableTranslation(initialCheck = false): void {
    this.toggleTranslationCheck = initialCheck ? false : !this.toggleTranslationCheck;
    if (this.chatMessages.length && this.isTranslationEnabled) {
      this.messageTranslate();
    } else if (!this.toggleTranslationCheck || !this.isTranslationEnabled) {
      const beforeTranslation = deepParseJSON(this.beforeTranslation) ? JSON.parse(this.beforeTranslation) : [];
      if (this.chatMessages.length === beforeTranslation?.length) {
        this.chatMessages = beforeTranslation;
        this.scrollToBottom();
      } else {
        this.fetchChatMessages(false, false);
      }
    }
  }
  get isTranslationEnabled(): boolean {
    if (isBlank(this.toggleTranslationCheck)) {
      this.toggleTranslationCheck = false;
    }
    return (this.toggleTranslate && this.toggleTranslationCheck) || (!this.toggleTranslate && this.chatAutoTranslate);
  }
  translatedMessageList(resultResponseData: any, message?: any): void {
    let sourceLanguage;
    const messageLength = resultResponseData ? resultResponseData.length : this.chatMessages.length;
    if (isPresent(message?.id) && isPresent(resultResponseData) && isPresent(this.chatMessages)) {
      const index = this.chatMessages.findIndex((item: any) => item?.uniqueId === message.id);
      if (index) {
        this.chatMessages[index].message = resultResponseData[0]?.translatedText;
        sourceLanguage = resultResponseData[0]?.detectedSourceLanguage;
        this.saveTranslationLog(sourceLanguage);
      }
    } else {
      this.chatMessages.forEach((elem, index) => {
        if (index < messageLength) {
          if (resultResponseData) {
            if (elem?.userid.toString() !== this.sharedService.userData?.userId && this.chatMessages && this.chatMessages[index]?.message) {
              if (this.chatMessages[index].userid.toString() !== this.sharedService.userData?.userId) {
                this.chatMessages[index].message = resultResponseData[index]?.translatedText;
              }
              sourceLanguage = resultResponseData[index]?.detectedSourceLanguage;
              this.saveTranslationLog(sourceLanguage);
            }
          }
        }
      });
    }
  }
  saveTranslationLog(sourceLang: string): void {
    if (this.beforeTranslation) {
      const existMessages = JSON.parse(this.beforeTranslation);
      const ccLang = navigator.language.split('-')[0];
      existMessages.forEach((elem, i) => {
        if (elem.userid && elem.userid.toString() !== this.sharedService.userData.userId) {
          this.traslatedData.push({
            message: elem.message,
            translation: this.chatMessages[i].message,
            fromLang: sourceLang,
            toLang: ccLang,
            chatRoomId: this.roomId.toString(),
            userId: this.sharedService.userData.userId
          });
        }
      });
    }
  }
  async infoModal(message?: any): Promise<any> {
    if (+message.userid !== +this.currentUser) {
      this.presentInfoModal(message);
      return;
    }
    this.sharedService
      .getMessageDeliveredUsers({ messageId: message.id, deliveryStatus: MessageDeliveryStatus.ALL, showLoader: true })
      .subscribe(async ({ deliveredUsers, readUsers }) => {
        if (!isBlank(message?.deleteUndoHistory)) {
          this.getMessageHistory(message, deliveredUsers, readUsers);
        } else {
          this.presentInfoModal(message, deliveredUsers, readUsers);
        }
      });
  }

  getMessageHistory(message, deliveredUsers: MessageDeliveredUsers[] = [], readUsers: MessageDeliveredUsers[] = []): void {
    this.sharedService.isLoading = true;
    const params: any = {
      messageId: message.id
    };
    this.graphqlService.getMessageDeleteUndoHistory(params)?.subscribe(
      (response: any) => {
        this.sharedService.isLoading = false;
        if (response.data) {
          const updatedMessage = { ...message, deleteUndoHistory: response.data.getDeleteUndoHistoryByMessageId.deleteUndoHistory || [] };
          this.presentInfoModal(updatedMessage, deliveredUsers, readUsers);
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }

  async presentInfoModal(message, deliveredUsers: MessageDeliveredUsers[] = [], readUsers: MessageDeliveredUsers[] = []): Promise<void> {
    const modal = await this.modalController.create({
      component: MsgInfoModalComponent,
      componentProps: {
        message,
        isSentChat: +message.userid === +this.currentUser,
        chatroomId: this.roomId.toString(),
        deliveredUsers,
        readUsers
      }
    });
    return modal.present();
  }
  private getAdmissionIdFromPatientData(patientTagInfo) {
    return this.sharedService.isMultiAdmissionsEnabled && patientTagInfo && patientTagInfo.admissionId ? patientTagInfo.admissionId: '';
}
  private getPatientIdFromPatientTagInfo(patientTagInfo) {
    return patientTagInfo && patientTagInfo.patientId ? `${patientTagInfo.patientId}${this.getAdmissionIdFromPatientData(patientTagInfo)}` : '';
  }
  getTotalCheckedCount(length?: number) {
    if (length) {
      this.getSelectedTagCount = length;
    } else {
      const { checkedMessages } = this.getCheckedMessages();
      this.getSelectedTagCount = checkedMessages.length;
    }
  }
  getCheckedMessages(getPatients = false) {
    const patients = [];
    let patientTagInfo;
    const checkedMessages = this.chatMessages.filter((item: any) => {
      if (getPatients) {
        const patientId = this.getPatientIdFromPatientTagInfo(item.patientTagInfo);
        if (patientId && !patients.includes(patientId) && item.checked) {
          patientTagInfo = item.patientTagInfo;
          patients.push(patientId);
        }
      }
      return item.checked;
    });
    this.allowIntegrationTags = patients.length <= 1;
    return { checkedMessages, patients, allowIntegrationTags: this.allowIntegrationTags, patientTagInfo };
  }

  clearTagMessageConfirm() {
    const buttons = [
      { text: this.commonService.getTranslateData('BUTTONS.CANCEL'), confirm: false },
      { text: this.commonService.getTranslateData('BUTTONS.OK'), confirm: true }
    ];
    this.commonService
      .showAlert({
        message: 'MESSAGES.REMOVE_MESSAGE_TAG_CONFIRM',
        header: 'BUTTONS.TAG_MESSAGES',
        buttons
      })
      .then((confirmation) => {
        if (confirmation) {
          this.cancelMultiTag();
        }
      });
  }

  statNewChatMessage() {
    this.sharedService.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
  }
  switchApplessMode(): void {
    if (this.selectedMessageData.createdBy !== this.sharedService.userData.userId) {
      const message = this.commonService.getTranslateData('ERROR_MESSAGES.APPLESS_MESSAGE_MODE_NO_PRIVILEGE');
      this.commonService.showMessage(message);
      return;
    }
    const alertData = {
      message: this.enableApplessMode ? 'MESSAGES.CHANGE_MESSAGE_MODE_TO_INAPP' : 'MESSAGES.CHANGE_MESSAGE_MODE_TO_APPLESS',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.commonService.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        this.processApplessMode(this.enableApplessMode ? Number(Constants.configFalse) : Number(Constants.configTrue));
      }
    });
  }

  resetMessageReplyTimeoutOnNewmessage(message: any): void {
    if (message.userId !== Constants.administratorId) {
      this.updateConfigPermissions();
      this.setMessageReplyTimeout();
    }
  }

  setMessageReplyTimeout(timeout: number = Number(this.messageReplyTimeout) * 60): void {
    if (this.sharedService?.userData?.group === String(Constants.patientGroupId)) {
      if (this.messageReplyTimeoutTimer) {
        clearTimeout(this.messageReplyTimeoutTimer);
      }

      this.messageReplyTimeoutTimer = setTimeout(() => {
        this.doNotReplyStatus = true;
        this.isEnabledDoNotReplySection = this.isEnabledDoNotReplyFunction;
        if (!this.doubleVerificationConfirmed) {
          this.isShowDoubleVerification = false;
          this.showDoubleVerificationStatus = Number(Constants.configFalse);
        }
        const splitTitle: any = this.title.split('Chat With ');
        const getName = splitTitle.length > 1 ? splitTitle[1].split(',') : [];
        this.clinicianName = this.clinicianName || getName.length > 1 ? getName[0] : '';
      }, timeout * 60 * 1000);
    }
  }

  setBranchHourSwitchTimer(): void {
    if (
      this.allowPatientToChat24Hour &&
      this.isEnableDoubleVerification &&
      this.sharedService?.userData?.group === String(Constants.patientGroupId) &&
      (!isPresent(this.selectedMessageData) || this.isGeneralMessageOrGroup)
    ) {
      const currentTime = moment().format('HH:mm:ss');
      const branchStartTime = this.sharedService.getSiteConfigValue(Config.branchStartTime) || Constants.siteConfigs.defaultBranchStartTime;
      const branchEndTime = this.sharedService.getSiteConfigValue(Config.branchEndTime) || Constants.siteConfigs.defaultBranchEndTime;
      const currentTimeHour = Number(currentTime.split(':')[0]);
      const currentTimeMinutes = Number(currentTime.split(':')[1]);
      const currentTimeSeconds = Number(currentTime.split(':')[2]);
      const branchStartHour = Number(branchStartTime.split(':')[0]);
      const branchStartMinutes = Number(branchStartTime.split(':')[1]);
      let branchHourSwitchTimeInSec = 0;
      if (this.isWorkingHours) {
        branchHourSwitchTimeInSec =
          moment(`${moment().format('YYYY-MM-DD')} ${branchEndTime}`).diff(
            moment().hour(currentTimeHour).minute(currentTimeMinutes).seconds(currentTimeSeconds),
            'seconds'
          ) + 1;
      } else {
        if (
          moment(`${moment().format('YYYY-MM-DD')} ${currentTime}`).isAfter(
            moment(`${moment().format('YYYY-MM-DD')} ${branchStartTime}`)
          )
        ) {
          let actualStartTime = moment(`${moment().add(1, 'days').format('YYYY-MM-DD')} ${branchStartTime}`);
          branchHourSwitchTimeInSec =
            Math.abs(moment().diff(moment().hour(23).minute(59).seconds(59), 'seconds')) +
            Math.abs(
              moment(`${actualStartTime.format('YYYY-MM-DD')} 00:01`).diff(
                moment(`${actualStartTime.format('YYYY-MM-DD')} ${branchStartTime}`)
                  .hour(branchStartHour)
                  .minute(branchStartMinutes)
                  .seconds(0),
                'seconds'
              )
            ) +
            61;
        } else {
          branchHourSwitchTimeInSec = Math.abs(
            moment(`${moment().format('YYYY-MM-DD')} ${branchStartTime}`).diff(
              moment().hour(currentTimeHour).minute(currentTimeMinutes).seconds(currentTimeSeconds),
              'seconds'
            )
          );
        }
      }
      if (this.branchHourSwitchTimer) {
        clearTimeout(this.branchHourSwitchTimer);
      }

      this.branchHourSwitchTimer = setTimeout(() => {
        this.updateConfigPermissions({ resetDoubleVerification: true, updateConfigFromPolling: false });
        this.setBranchHourSwitchTimer();
        this.checkTimeOutMessageIfUserNotAvailable();
      }, branchHourSwitchTimeInSec * 1000);
    }
  }

  setDoubleVerificationStatus(status: string): void {
    this.showDoubleVerificationStatus = Number(status);
    this.isShowDoubleVerification = false;
    this.doubleVerificationConfirmed = true;
    const data = {
      displayName: this.sharedService.userData?.displayName,
      userId: this.sharedService.userData?.userId,
      doubleVerificationStatusChoosed: status
        ? this.commonService.getTranslateData('BUTTONS.DOUBLE_VERIFICATION_NOT_URGENT')
        : this.commonService.getTranslateData('BUTTONS.DOUBLE_VERIFICATION_URGENT'),
      roomId: this.roomId
    };
    const activityData = {
      type: Activity.doubleVerification,
      name: Activity.messaging,
      des: Activity.doubleVerificationDes
    };
    this.sharedService.trackActivity(activityData);
  }

  filterTags(): void {
    const url = APIs.getAlltags;
    const params = {
      tag_type: 1,
      patientTenantId: this.sharedService.userData.tenantId,
    };
    this.httpService.doGet({ endpoint: url, extraParams: params }).subscribe(
      async (response) => {
        this.allTags = response;
        const modal: any = await this.modalController.create({
          component: AdvancedSelectComponent,
          componentProps: {
            options: this.sharedService.mergeSelectedData(this.filteredTags, this.allTags),
            showSearch: true,
            buttons: { selectAll: true, clearAll: true, done: true },
            headerTitle: this.commonService.getTranslateData('TITLES.FILTER_THREAD_BY_TAGS')
          },
          cssClass: 'common-advanced-select',
          animated: true
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data) {
            const filteredData = this.allTags.filter((tag) => {
              return data.selectedData.find((row) => row.selected && tag.id === row.id);
            });
            this.filteredTags = filteredData;
            this.fetchChatMessages();
          }
        });
        await modal.present();
      },
      (error) => {
        this.commonService.showToast({ message: this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'), color: 'danger' });
        this.sharedService.errorHandler(error);
      }
    );
  }

  isFiltered() {
    return !(isBlank(this.filteredTags) && this.priorityFilterValue === 0 && this.flagFilterValue === 0 && this.filterOptions === '1' && !this.showMentionMessages);
  }

  async messageTypeActionSheet() {
    const buttons = [];
    this.messageFilterOptions.forEach((option) => {
      buttons.push({
        text: this.commonService.getTranslateData(option.name),
        role: option.value === this.filterOptions ? 'selected' : null,
        handler: () => {
          this.changeChatFilter(option.value);
        }
      });
    });
    buttons.push({
      text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
      role: 'cancel',
      data: {
        action: 'cancel'
      }
    });

    const actionSheet = await this.actionSheetController.create({
      header: this.commonService.getTranslateData('LABELS.FILTER_BY_MESSAGE_TYPE'),
      mode: 'ios',
      cssClass: 'action-sheets-more-in-chat',
      buttons
    });
    await actionSheet.present();
  }

  removeMessageTags(tagId: number, messageId: any) {
    const index = this.chatMessages.findIndex((message) => Number(message.id) === Number(messageId));
    if (index > -1) {
      this.chatMessages[index].tagedItems = this.chatMessages[index].tagedItems?.filter((tag) => tag.id !== tagId);
      this.socketService.emitEvent(Socket.updateMessageTags, {
        autoApprove: false,
        tagNames: [],
        messageIds: [messageId],
        currentChatroomId: String(this.roomId),
        updatedBy: this.sharedService.userData.userId,
        removedTagId: tagId
      });
      this.removePatientAssociationFromMessage(index);
      this.isChatUpdated = true;
    }
  }
  private removePatientAssociationFromMessage(index: number) {
    const patientTags = this.chatMessages[index].tagedItems?.filter((tag) => !isBlank(tag.patient));
    if (isBlank(patientTags)) {
      this.chatMessages[index].patient = '';
      this.chatMessages[index].patientTagInfo = undefined;
    }
}
  onMessageInput() {
    const selection = window.getSelection();
    if (selection.rangeCount && this.sharedService?.userData?.group !== UserGroup.PATIENT.toString()) {
      const range = selection.getRangeAt(0);
      const lastLine = range.startContainer.nodeValue;
      if (lastLine && (lastLine.startsWith('@') || lastLine.includes('@'))) {
        const first = lastLine.slice(0, range.startOffset);
        const lastIndex = first.lastIndexOf('@');
        const searchString = first.substring(lastIndex + 1, first.length);
        if (lastIndex === 0 || [CHAR_CODES.SPACE, CHAR_CODES.NBSP, CHAR_CODES.NO_BREAK].includes(first.charCodeAt(lastIndex - 1))) {
          this.showMentionList = true;
          if (searchString === '') {
            this.filteredMentionUsers = this.mentionUsers;
          } else {
            this.filteredMentionUsers = this.mentionUsers.filter(
              (user) =>
                user.displayName &&
                user.displayName.toLowerCase().includes(searchString.toLowerCase())
            );
          }
        } else {
          this.showMentionList = false;
        }
      } else {
        this.showMentionList = false;
      }
    }
  }

  userSelectHandle(user) {
    this.showMentionList = false;
    const selection = <Selection>window.getSelection();
    if (selection && selection.rangeCount === 0 && this.savedRange !== null) {
      selection.addRange(this.savedRange);
    }
    if (selection.rangeCount) {
      const range = selection.getRangeAt(0);
      const container = range.startContainer;
      const lastLine = container.nodeValue;
      if (lastLine && lastLine.includes('@')) {
        const first = lastLine.slice(0, range.startOffset);
        const last = lastLine.slice(range.endOffset, lastLine.length);
        const lastIndex = first.lastIndexOf('@')
        const updatedLine = `${first.substring(0, lastIndex)}<input type="button" value="${user.displayName}" attr.data-target="${user.userId
          }" class='mention'></input>&NoBreak;${last}`;
        const fragment = document.createRange().createContextualFragment(updatedLine);
        const tempDiv = document.createElement('div');
        tempDiv.appendChild(fragment);
        const parent = container.parentNode;
        parent.replaceChild(tempDiv, container);
        selection.removeAllRanges();
        while (tempDiv.firstChild) {
          parent.insertBefore(tempDiv.firstChild, tempDiv);
        }
        parent.removeChild(tempDiv);
        this.message = (<HTMLElement>document.getElementById('message-textarea')).innerHTML;
        setTimeout(() => {
          this.moveCursorToEnd();
        }, 100);
      }
    }
  }
  deleteUndoMessageConfirm(message?: any,i?: number, key?: string) {
    const buttons = [
      { text: this.commonService.getTranslateData('BUTTONS.CANCEL'), confirm: false },
      { text: this.commonService.getTranslateData('BUTTONS.OK'), confirm: true }
    ];
    this.commonService
      .showAlert({
        message: key === 'delete' ? 'MESSAGES.DELETE_MESSAGE_CONFIRM' : 'MESSAGES.UNDO_MESSAGE_CONFIRM',
        header: key === 'delete' ? 'BUTTONS.DELETE_MESSAGE' : 'BUTTONS.UNDO_MESSAGE',
        buttons
      })
      .then((confirmation) => {
        if (confirmation) {
          this.deleteOrUndoMessage(message,i, key);
        }
      });
  }

  deleteOrUndoMessage(message?: any,i?: number, key?: string) {
    const payload = {
      id: message.id,
      action: key
    };
    this.httpService
    .doPut({
      endpoint: APIs.messageStatusEndpoint,
      payload: payload
    })
    .subscribe((response) => {
      if (response?.status) {
        this.fetchUpdatedChatMessages(response?.messageId);
        this.commonService.showToast({ message: response.message });
      } else {
        const message = key === 'delete' ? this.commonService.getTranslateData('MESSAGES.MESSAGE_DELETE_FAILED') : this.commonService.getTranslateData('MESSAGES.MESSAGE_UNDO_FAILED');
        this.commonService.showToast({ message, color: 'danger' });
      }
      
    });
  }

  fetchUpdatedChatMessages(messageID) {
    const params = {
      room: this.roomId ? this.roomId.toString() : this.sharedService.roomID.toString(),
      id: messageID
    };
    this.messageCenterService.fetchChatMessages(params, true).subscribe(
      (response: ChatMessageResponse) => {
        if (isPresent(response.content)) {
          const updatedMessage = response.content.find((message) => message.id === messageID);
          const index = this.chatMessages.findIndex((message) => message.id === updatedMessage.id);
          if (index > -1) {
            this.chatMessages[index] = { ...this.chatMessages[index], ...updatedMessage };
          }
        }
      },
      () => {
        this.router.navigate([this.isArchivedMessage ? PageRoutes.archivedMessage : PageRoutes.activeMessages]);
      }
    );
  }

  getDeletedDateTime(dateTime){
    const deletedDate = convertUTCToTimeZoneDateTimeSplit(
      dateTime,
      '',
      moment.tz.guess(),
      Constants.dateFormat.mmddyyhma,
    );
    return deletedDate;
  }

  isUndoAvailable(deletedTime: string): boolean {
    const currentTime = moment();
    const deletedDate = convertUTCToTimeZoneDateTimeSplit(
        deletedTime,
        '',
        moment.tz.guess(),
        Constants.dateFormat.mmddyyhma,
    );
    const deletedMoment = moment(deletedDate, Constants.dateFormat.mmddyyhma);
    return currentTime.diff(deletedMoment, 'minutes') <= 30;
  }

  replyMessage(message: any = null, i: number = null, event?: any) {
    event?.close();
    const container = document.createElement('div');

    //callout-content
    const calloutContent = document.createElement('div');
    calloutContent.classList.add('callout-content');
    calloutContent.contentEditable = "false";
    calloutContent.setAttribute('attr.data-target', message.id);

    //callout
    const callout = document.createElement('div');
    callout.classList.add('callout', 'callout-default');

    //Name
    const name = document.createElement('small');
    name.innerHTML = message.displayName
    callout.appendChild(name);

    //time
    const time = document.createElement('small');
    time.classList.add('callout-time')
    time.innerHTML = moment(Number(message.sent) * 1000).format('MMM DD YYYY hh:mm A');
    callout.appendChild(time);

    //Message
    let msg = document.createElement('div');
    msg.classList.add('callout-message')
    msg.innerHTML = message.message.replace(/\&NoBreak;/g, '');
    msg = this.removeElementsByClass(msg, ['callout-content']);
    callout.appendChild(msg);

    //Close
    const closeButton = document.createElement('span');
    closeButton.innerHTML = '<ion-icon tappable name="close" onclick="event.target.parentNode.parentNode.parentNode.removeChild(event.target.parentNode.parentNode)"> </ion-icon>';
    closeButton.classList.add('close');
    calloutContent.appendChild(callout)
    container.appendChild(calloutContent);
    callout.appendChild(closeButton);

    container.innerHTML = container.innerHTML + '&NoBreak;';
    let tempDiv = document.createElement('div');
    tempDiv.innerHTML = this.message.replace(/\&NoBreak;/g, '');
    tempDiv.innerHTML = tempDiv.innerHTML + container.innerHTML;
    this.message = tempDiv.innerHTML;
    setTimeout(() => {
      this.moveCursorToEnd();
    }, 100);
  }
  moveCursorToEnd() {
    const editableDiv = document.getElementById("message-textarea");
    const range = document.createRange();
    const sel = window.getSelection();
    const lastChild = editableDiv.lastChild;
    if (lastChild && lastChild.nodeType === 3) {
      range.setStart(lastChild, lastChild.textContent.length);
    } else {
      range.setStart(editableDiv, 0);
    }
    range.collapse(true);
    sel.removeAllRanges();
    sel.addRange(range);
    editableDiv.focus();
  }

  removeElementsByClass(containerElement, classNames) {
    classNames.forEach((className) => {
      const elements = containerElement.getElementsByClassName(className);
      while (elements.length > 0) {
        elements[0].parentNode.removeChild(elements[0]);
      }
    });
    return containerElement;
  }

  formatMessage(message) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = message;
    this.removeElementsByClass(tempDiv, ['close']);
    const elements = tempDiv.getElementsByClassName('mention');
    Array.from(elements).forEach((el) => {
      const input = (<HTMLInputElement>el);
      if (input.value) {
        const mention = document.createElement('span');
        mention.classList.add('mention');
        mention.setAttribute('attr.data-target', input.getAttribute("attr.data-target"));
        mention.innerHTML = input.value;
        input.parentNode.replaceChild(mention, input);
      }
    });
    return tempDiv.innerHTML;
  }

  getMentionedUsers(message) {
    const mentionedUsers = [];
    let element = document.createElement('div');
    element.innerHTML = message;
    element = this.removeElementsByClass(element, ['callout-content']);
    const elements = element.getElementsByClassName('mention');
    for (let i = 0; i < elements.length; i += 1) {
      const attrib = elements[i].getAttribute('attr.data-target');
      if (!isBlank(attrib)) mentionedUsers.push(Number(attrib));
    }
    return mentionedUsers;
  }
  getRepliedTo(message) {
    const repliedTo = [];
    const element = document.createElement('div');
    element.innerHTML = message;
    const elements = element.getElementsByClassName('callout-content');
    for (let i = 0; i < elements.length; i += 1) {
      const attrib = elements[i].getAttribute('attr.data-target');
      if (!isBlank(attrib)) repliedTo.push(Number(attrib));
    }
    return repliedTo.filter((elem, index, self) => index === self.indexOf(elem));
  }

  addActiveMention() {
    setTimeout(() => {
      const elements = this.elRef.nativeElement.getElementsByClassName('mention');
      for (const element of elements) {
        const attrib = element.getAttribute('attr.data-target');
        if (!isBlank(attrib) && Number(attrib) === this.currentUser) element.classList.add('active');
      }
    });
  }

  showDownloadIcon(message: any) {
    const getImgTag = message.message.split('<br>');
    let addBRTag = '';
    getImgTag.forEach((imgTag: any, index: number) => {
      if(imgTag.includes('comment-attachment')) {
        addBRTag = '<br>';
      }
      if (imgTag.includes('<img') && !imgTag.includes('show-download-options')) {
        const div = document.createElement('div');
        div.innerHTML = imgTag;
        const imageElements = div.querySelectorAll('img');
        let updatedImgTags = '';
        imageElements.forEach((createdElement) => {
          let url = createdElement.getAttribute('data-src') || createdElement.getAttribute('data-viewsrc');
          const mediaType = createdElement.getAttribute('data-mediatype');
          let imageClassName = '';
          if (mediaType === Constants.documentTypes.image) {
            createdElement.classList.add('image-download-max-width');
            url = createdElement.getAttribute('src');
            imageClassName = 'image-download-icon-position';
          }
          updatedImgTags += `<br> ${createdElement.outerHTML} 
          <span class="show-download-options ${imageClassName}">
            <ion-icon name="cloud-download" 
            ng-click="showDocumentOptionForMobile($event)" 
            data-src='${url}' 
            data-mediatype='${mediaType}'></ion-icon>
          </span>`;
        });
        getImgTag[index] = updatedImgTags;
      } else if (imgTag.includes('<img') && imgTag.includes('show-download-options')) {
        getImgTag[index] = `<br> ${imgTag}`;
      }
      getImgTag[index] = `${addBRTag}${getImgTag[index]}`;
      if (index === getImgTag.length - 1) {
        message.message = getImgTag.join('');
        if (message.data) {
          message.data = getImgTag.join('');
        }
      }
    });
    return message;
  }

  /**
     * // status 1 = restoreParticipants 
     *    status 0 = deleteParticipants
     */
  updateChatRoomParticipant():void {
    this.socketService.subscribeEvent(Socket.updateChatroomParticipants).subscribe((response: UpdateChatroomParticipantsResponse) => {
      if(+this.roomId === +response.room && +response.initiatorUserId !== +this.sharedService.userData.userId) {          
        this.fetchRoomUsers();
      }
    });
  }
  get isVirtualPatient(): boolean {
    return (
      Number(this.sharedService?.userData?.group) === UserGroup.PATIENT &&
      this.sharedService?.userData?.isVirtual
    );
  }

  processApplessMode(type: number): void {
    this.httpService
      .doPost({
        endpoint: APIs.applessChatMode,
        payload: {
          appLessMode: type,
          chatRoomId: String(this.roomId)
        },
        version: Constants.apiVersions.apiV5
      })
      .subscribe(
        (response) => {
          if (response?.success) {
            this.enableApplessMode = Boolean(type);
            this.selectedMessageData.appLessMode = this.enableApplessMode;
          } else {
            this.selectedMessageData.appLessMode = type;
          }
        },
        () => {
          const message = this.commonService.getTranslateData('ERROR_MESSAGES.APPLESS_MESSAGE_MODE_UPDATE_FAILED');
          this.commonService.showMessage(message);
        }
      );
  }

  handleAppLessAndInAppMode(data): void {
    const STATUS_INACTIVE = '0';
    const ENROLLED = 1;

    this.isVirtualUserExist = data.allParticipants.some((item) => item.status !== STATUS_INACTIVE && +item.is_enrolled !== ENROLLED);
  }
}
