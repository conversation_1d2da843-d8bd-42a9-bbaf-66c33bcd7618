import { Component, OnInit } from '@angular/core';
import { InfiniteScrollCustomEvent, ModalController, NavParams } from '@ionic/angular';
import { APIs } from 'src/app/constants/apis';
import { ChatWithTypes, Constants, RecipientIdType, UserGroup } from 'src/app/constants/constants';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Activity } from 'src/app/constants/activity';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';
import { isBlank, formatDate, isPresent } from 'src/app/utils/utils';
import { Subject } from 'rxjs';
import { Config } from 'src/app/constants/config';
import { LoginResponse } from 'src/app/interfaces/login';
import { DoOperation, MessageGroupData, MessageGroupParams, SearchAction } from 'src/app/interfaces/messages';
import { PageRoutes } from 'src/app/constants/page-routes';
import { CreateChatRoomData, CreateChatRoomPayload, GeneralResponse } from 'src/app/interfaces/common-interface';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';
import { UserService } from 'src/app/services/user-service/user.service';
import { ChooseContactsComponent } from './choose-contacts/choose-contacts.component';
import { SelectTopicComponent } from './select-topic/select-topic.component';

@Component({
  selector: 'app-choose-recipients',
  templateUrl: './choose-recipients.page.html',
  styleUrls: ['./choose-recipients.page.scss']
})
// eslint-disable-next-line @angular-eslint/component-class-suffix
export class ChooseRecipientsPage implements OnInit {
  usersList: any[] = [];
  staffList: any[];
  pageCount = 0;
  chatWithTypes = ChatWithTypes;
  activeTabValue = ChatWithTypes.PATIENT;
  enrolledUser = `(${Constants.enrolledUser})`;
  virtualUser = `(${Constants.virtualUser})`;
  searchText = '';
  loadLimit = 20;
  showLoadMore = false;
  showPatientsTab = true;
  showStaffsTab = true;
  showGroupsTab = true;
  showPdgTab = true;
  errorMessage = this.commonService.getTranslateData('PLACEHOLDERS.SEARCH_HERE');
  associatedPatient: number;
  mrn: string;
  patientGroupId = Constants.patientGroupId;
  eventsSubject: Subject<void> = new Subject<void>();
  tabActive = true;
  selectedSiteIds: number[] = [];
  constants = Constants;
  isLoadMore = false;
  userData: LoginResponse;
  patientDefaultWorkFlow: string;
  staffChatRoomRecipients: number[] = [];
  staffChatRoomRecipientsFull: { userId: number; name: string }[] = [];
  loaderEvent: InfiniteScrollCustomEvent;
  isModalOpen = false;
  selectedData: CreateChatRoomData;
  admissionDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_ADMISSION');
  displayText = this.commonService.getTranslateData('TITLES.CHOOSE_PATIENT');
  modalHeaderTitle = 'TITLES.SELECT_PATIENT';
  groupTopicDisplayText = '';
  admissionDetails: any = {};
  constructor(
    private readonly httpService: HttpService,
    public sharedService: SharedService,
    private readonly commonService: CommonService,
    private readonly permissionService: PermissionService,
    private readonly modalController: ModalController,
    private readonly userService: UserService,
    private readonly navParams: NavParams
  ) {
    this.staffList = this.navParams.get('staffList');
    const roleList = this.navParams.get('roleList');
    this.admissionDetails = this.navParams.get('admissionDetails');
    this.associatedPatient = this.navParams.get('associatedPatient');
    this.mrn = this.commonService.getTranslateData('GENERAL.MRN');
    this.userData = sharedService.userData;

    if (isPresent(roleList)) {
      this.usersList = roleList;
      this.activeTabValue = this.chatWithTypes.ROLE;
      this.modalHeaderTitle = 'TITLES.SELECT_ROLE';
    }
  }

  ngOnInit(): void {
    if (this.activeTabValue !== this.chatWithTypes.ROLE) {
      this.updateConfigPermissions();
      this.sharedService.configValuesUpdated.subscribe(() => {
        this.updateConfigPermissions();
      });
    } else {
      this.displayText = this.commonService.getTranslateData('TITLES.CHOOSE_ROLE');
      this.modalHeaderTitle = '';
    }
  }
  updateConfigPermissions() {
    if (!this.permissionService.userHasPermission(Permissions.chatWithPatients)) {
      this.activeTabValue = this.chatWithTypes.STAFF;
    } else {
      this.activeTabValue = this.chatWithTypes.PATIENT;
    }
    this.showPatientsTab = this.permissionService.userHasPermission(Permissions.chatWithPatients);
    this.showStaffsTab = this.permissionService.userHasPermission(Permissions.chatWithClinician);
    this.showGroupsTab = this.permissionService.userHasPermission(Permissions.chatWithMessageGroups);
    this.patientDefaultWorkFlow = this.sharedService.getConfigValue(Config.defaultPatientsWorkflow);
    this.showPdgTab = this.showGroupsTab && this.sharedService.isEnableConfig(Config.allowVirtualPatient);
    const defaultCategory = this.sharedService.getConfigValue(Config.defaultCategoryOfChatWindowPopup);
    if (defaultCategory === Constants.patientValue && this.showPatientsTab) {
      this.activeTabValue = this.chatWithTypes.PATIENT;
    } else if (defaultCategory === Constants.userTypes.staff && this.showStaffsTab) {
      this.activeTabValue = this.chatWithTypes.STAFF;
    } else if (defaultCategory === Constants.userTypes.groups && this.showGroupsTab) {
      this.activeTabValue = this.chatWithTypes.MESSAGE_GROUP;
    } else if (defaultCategory === Constants.userTypes.pdg && this.showPdgTab) {
      this.activeTabValue = this.chatWithTypes.PATIENT_GROUP;
    }
    this.segmentChanged({ detail: { value: this.activeTabValue } });
  }
  goBack(): void {
    this.isModalOpen = false;
    this.modalController.dismiss();
    const staffPartnerTab = [this.chatWithTypes.STAFF, this.chatWithTypes.PARTNER];
    if (staffPartnerTab.includes(this.activeTabValue)) {
      this.clearRecipients();
    }
  }
  reset(): void {
    this.usersList = [];
    this.pageCount = Constants.defaultPageCount;
    this.showLoadMore = false;
    this.isLoadMore = false;
    this.clearRecipients();
  }
  segmentChanged(event): void {
    this.activeTabValue = event.detail.value;
    const isActiveTabPatient = this.activeTabValue === this.chatWithTypes.PATIENT;
    const isActiveTabPatientGroup = this.activeTabValue === this.chatWithTypes.PATIENT_GROUP;
    if (isActiveTabPatient || isActiveTabPatientGroup) {
      this.admissionDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_ADMISSION');
      this.displayText = this.commonService.getTranslateData('TITLES.CHOOSE_PATIENT');
      this.modalHeaderTitle = 'TITLES.SELECT_PATIENT';
    }

    if (this.activeTabValue === this.chatWithTypes.MESSAGE_GROUP) {
      this.displayText = this.commonService.getTranslateData('TITLES.CHOOSE_GROUP');
      this.modalHeaderTitle = 'TITLES.SELECT_GROUP';
    }
    this.setTitleAndDisplayText();
    this.selectedData = {};
    this.searchText = '';
    this.reset();
    this.errorMessage = this.commonService.getTranslateData('PLACEHOLDERS.SEARCH_HERE');
  }
  /**
   * isContactDisabled returns false if current tab is not groups and user is either enrolled or having contact method or having alternate conacts
   * @param user
   * @returns boolean
   */
  isContactDisabled(user): boolean {
    return (
      this.activeTabValue !== this.chatWithTypes.ROLE &&
      this.activeTabValue !== this.chatWithTypes.MESSAGE_GROUP &&
      this.activeTabValue !== this.chatWithTypes.PATIENT_GROUP &&
      !(this.userService.isUserContactable(user) || isPresent(user?.alternateContacts))
    );
  }
  get getIdentifier(): string {
    switch (this.activeTabValue) {
      case this.chatWithTypes.ROLE:
        return RecipientIdType.ROLE;
      case this.chatWithTypes.MESSAGE_GROUP:
        return RecipientIdType.MESSAGE_GROUP;
      default:
        return RecipientIdType.DEFAULT;
    }
  }
  fetchUsers(): void {
    this.sharedService.isLoading = false;
    if (this.activeTabValue === this.chatWithTypes.MESSAGE_GROUP) {
      const endpoint = APIs.getMessageGroups;
      const params: MessageGroupParams = {
        data: {
          pagination: {
            page: this.pageCount + 1,
            limit: this.loadLimit
          }
        }
      };
      if (this.sharedService.isEnableConfig(Config.enableMultiSite) && isPresent(this.selectedSiteIds)) {
        params.siteIds = this.selectedSiteIds;
      }
      if (this.searchText) {
        params.data.filter = {
          search: this.searchText
        };
      }
      this.httpService
        .doGet({
          endpoint,
          extraParams: { payload: JSON.stringify(params) },
          version: Constants.apiVersions.apiV5,
          loader: false
        })
        .subscribe({
          next: ({ data, success }: GeneralResponse<MessageGroupData>) => {
            const usersList = this.pageCount === 0 ? [] : this.usersList;
            if (success) {
              const response = data.messageGroups;
              this.completeLoaderEvent();
              this.usersList = [...usersList, ...response];
              this.showLoadMore = response.length === this.loadLimit;
            }
            if (isBlank(this.usersList)) {
              this.setErrorMessage();
            }
          },
          error: () => {
            this.usersList = this.pageCount === 0 ? [] : this.usersList;
            if (isBlank(this.usersList)) {
              this.setErrorMessage();
            }
            this.completeLoaderEvent();
          }
        });
    } else if (isPresent(this.staffList)) {
      this.usersList = [
        ...this.usersList,
        ...this.staffList.map((r) => ({
          ...r,
          isScheduled: this.sharedService.scheduleSelectionFilter(r)
        }))
      ];
    } else if (this.activeTabValue !== this.chatWithTypes.ROLE) {
      let params: any = {
        excludeRoleId: this.activeTabValue === this.chatWithTypes.STAFF,
        excludeLogginedUser: this.sharedService.userData.userId,
        pageCount: this.pageCount,
        searchKeyword: this.searchText,
        roleId: this.activeTabValue === this.chatWithTypes.PARTNER ? UserGroup.PARTNER : UserGroup.PATIENT,
        optionShow: this.activeTabValue === this.chatWithTypes.PATIENT_GROUP ? this.chatWithTypes.PATIENT : this.activeTabValue,
        crossSiteOptions: this.activeTabValue === this.chatWithTypes.STAFF,
        siteIds:
          this.sharedService.isEnableConfig(Config.enableMultiSite) && isPresent(this.selectedSiteIds)
            ? this.selectedSiteIds.toString()
            : Constants.defaultSiteId
      };
      if (!this.sharedService.isMultiAdmissionsEnabled && this.activeTabValue === this.chatWithTypes.PATIENT_GROUP) {
        params = {
          ...params,
          includePDGInfo: true
        };
      }
      this.sharedService.fetchUsers(params, false, this.activeTabValue === this.chatWithTypes.PATIENT_GROUP).subscribe({
        next: (response) => {
          this.completeLoaderEvent();
          if (isPresent(response) && response.length > 0) {
            if (!this.isLoadMore || this.pageCount === 0) {
              this.usersList = [];
            }
            this.usersList = [
              ...this.usersList,
              ...response.map((r) => ({
                ...r,
                displayText: `${this.commonService.getPatientDisplayName(r, Constants.patientValue)}
                          ${r.tenantId !== this.sharedService.userData.tenantId ? `[${r.tenantName}]` : ''}`,
                isScheduled: this.sharedService.scheduleSelectionFilter(r)
              }))
            ];
          } else {
            this.setErrorMessage();
          }
          this.showLoadMore = response?.length === this.loadLimit;
        },
        error: (error) => {
          this.completeLoaderEvent();
          this.sharedService.errorHandler(error);
        }
      });
    }
  }
  /**
   * setErrorMessage sets error message based on active tab value.
   */
  setErrorMessage(): void {
    this.errorMessage = '';
    switch (this.activeTabValue) {
      case this.chatWithTypes.MESSAGE_GROUP:
        this.errorMessage = this.commonService.getTranslateData('MESSAGES.NO_MESSAGE_GROUP_AVAILABLE');
        break;
      case this.chatWithTypes.STAFF:
        this.errorMessage = this.commonService.getTranslateData('MESSAGES.NO_STAFFS_AVAILABLE');
        break;
      case this.chatWithTypes.PARTNER:
        this.errorMessage = this.commonService.getTranslateData('MESSAGES.NO_PARTNERS_AVAILABLE');
        break;
      case this.chatWithTypes.PATIENT:
      case this.chatWithTypes.PATIENT_GROUP:
        this.errorMessage = this.commonService.getTranslateData('MESSAGES.NO_PATIENTS_AVAILABLE');
        break;
      default:
    }
  }
  showChevronIcon(data): boolean {
    return (
      this.patientDefaultWorkFlow === Constants?.workflowAlternateContact &&
      isPresent(data?.alternateContacts) &&
      this.activeTabValue === ChatWithTypes.PATIENT
    );
  }
  /**
   * addUserToMessageGroup used to add user to group after checking room members
   * @param roomId number
   * @param roomMembers string
   */
  addUserToMessageGroup(group): void {
    if (group.isParticipant === Constants.configFalse) {
      const apiURL = APIs.addUserToMessageGroup;
      const params = {
        roomId: Number(group.chatRoomId),
        type: this.activeTabValue
      };
      this.httpService.doPost({ endpoint: apiURL, payload: params }).subscribe({
        next: ({ data, success }: GeneralResponse<{ roomId: number }>) => {
          if (isPresent(data?.roomId) && success) {
            this.redirectToChatRoom(+data.roomId);
          }
        },
        error: () => {
          this.commonService.dismissModal();
        }
      });
    } else {
      this.redirectToChatRoom(+group.chatRoomId);
    }
  }
  /**
   * Common function to redirect to chat room
   * @param chatRoomId number
   */
  redirectToChatRoom(chatRoomId: number): void {
    this.sharedService.updateChatRoomId(this.sharedService.roomID, chatRoomId);
    const path = `${PageRoutes.chatRoom}/${chatRoomId}`;
    this.commonService.redirectToPage(path);
  }
  async presentTopicModal(user): Promise<void> {
    const modal = await this.modalController.create({
      component: SelectTopicComponent,
      componentProps: {
        selectedGroup: { ...user, groupName: this.activeTabValue === this.chatWithTypes.MESSAGE_GROUP ? user.groupName : user.displayText },
        identifier: this.getIdentifier,
        admissionId: this.sharedService.isMultiAdmissionsEnabled ? this.selectedData.admissionId : undefined
      }
    });
    modal.onWillDismiss().then(({ data }) => {
      this.modalController.dismiss();
      if (data) {
        this.selectedData = { ...this.selectedData, ...data, isSelectTopic: true };
        if (data?.selectedGroup && data?.topic) {
          this.groupTopicDisplayText = data?.topic?.subject;
        } else if (data.newTopicName) {
          this.groupTopicDisplayText = data.newTopicName;
          this.selectedData.topic = data.newTopicName;
        }
      }
    });
    return modal.present();
  }
  getLabelText(user) {
    let label = '';
    if (this.activeTabValue === this.chatWithTypes.MESSAGE_GROUP) {
      return user.groupName;
    }
    if (this.activeTabValue === this.chatWithTypes.ROLE) {
      label = user?.roleName ?? '';
      if (user?.tenant_id.toString() !== this.userData?.tenantId.toString()) {
        label += ` [ ${user?.tenant_name} ]`;
      }
      return label;
    }
    label = user.caregiver_userid ? `${user.caregiver_displayname} (${user.displayname})` : user.displayname;
    if (!user.caregiver_userid && user.dob && user?.roleId?.toString() === this.constants?.roleId?.patient) {
      label += ` - ${formatDate(user.dob, this.constants?.dateFormat?.mdy)}`;
    }
    if (
      !user.caregiver_userid &&
      user.IdentityValue &&
      (this.activeTabValue === this.chatWithTypes.PATIENT || this.activeTabValue === this.chatWithTypes.PATIENT_GROUP)
    ) {
      label += ` [${this.mrn}: ${user.IdentityValue}]`;
    }
    if (isPresent(user.passwordStatus) && this.activeTabValue !== this.chatWithTypes.PATIENT_GROUP) {
      label += user.passwordStatus === true ? ` ${this.enrolledUser}` : ` ${this.virtualUser}`;
    }
    if (user.caregiver_userid && user.caregiver_dob && user?.roleId?.toString() === this.constants?.roleId?.patient) {
      label += ` - ${formatDate(user.caregiver_dob, this.constants?.dateFormat?.mdy)}`;
    }
    if (user.caregiver_userid && user.caregiverIdentityValue && user?.roleId?.toString() === this.constants?.roleId?.patient) {
      label += ` [${this.mrn}: ${user.caregiverIdentityValue}]`;
    }
    if (isPresent(user.naTags)) {
      label += ` (${user.naTagNames})`;
    }
    if (user.tenantId !== this.userData?.tenantId) {
      label += ` [${user.tenantName}]`;
    }
    if (
      user.siteName !== '' &&
      this.userData?.config?.enable_multisite === this.constants?.enableMultiSite &&
      user?.roleId?.toString() === this.constants?.roleId?.patient
    ) {
      label += ` - ${user.siteName}`;
    }
    return label;
  }
  /**
   * multiSelectionActive multi selection for partner/staff to create chatroom
   */
  get multiSelectionActive(): boolean {
    return this.activeTabValue === this.chatWithTypes.STAFF || this.activeTabValue === this.chatWithTypes.PARTNER;
  }
  /**
   * isStaffRecipientsSelected is to show selected users and buttons
   */
  get isStaffRecipientsSelected(): boolean {
    return this.multiSelectionActive && isPresent(this.staffChatRoomRecipients);
  }
  /**
   * showInitialLoader loading staff
   */
  get showInitialLoader() {
    return (
      this.pageCount === Constants.defaultPageCount &&
      this.activeTabValue !== this.chatWithTypes.ROLE &&
      isBlank(this.usersList) &&
      !this.errorMessage
    );
  }
  /**
   * initialLoadingText Loading text based on active tab
   */
  get initialLoadingText(): string {
    let loadingText = this.commonService.getTranslateData('BUTTONS.LOADING_CLINICIANS');
    switch (this.activeTabValue) {
      case this.chatWithTypes.PATIENT:
        loadingText = this.commonService.getTranslateData('BUTTONS.LOADING_PATIENTS');
        break;
      case this.chatWithTypes.MESSAGE_GROUP:
      case this.chatWithTypes.PATIENT_GROUP:
        loadingText = this.commonService.getTranslateData('BUTTONS.LOADING_GROUPS');
        break;
      default:
    }
    return loadingText;
  }
  /**
   * addUsersToChatRoom function is common function on user list click action decide on multi selection push/pop user or single selection create chat room.
   * @param user
   * @returns
   */
  addUsersToChatRoom(user: Record<string, any>): void {
    if (!this.isContactDisabled(user) && this.multiSelectionActive && user.userId) {
      if (!this.removeUserFromChatRoom(user)) {
        this.staffChatRoomRecipients.push(user.userId);
        this.staffChatRoomRecipientsFull.push({ userId: user.userId, name: user.displayname });
      }
    }
  }
  removeUserFromChatRoom(user) {
    const index = this.staffChatRoomRecipients.indexOf(user.userId);
    if (index !== -1) {
      this.staffChatRoomRecipients.splice(index, 1);
      this.staffChatRoomRecipientsFull.splice(index, 1);
      return true;
    }
    return false;
  }
  /**
   * startChat is button click function to start chat with selected users
   */
  startChat(): void {
    const key = this.getIdentifier;
    const isActiveTabPatient = this.activeTabValue === this.chatWithTypes.PATIENT;
    const isActiveTabPatientGroup = this.activeTabValue === this.chatWithTypes.PATIENT_GROUP;
    const isActiveTabMessageGroup = this.activeTabValue === this.chatWithTypes.MESSAGE_GROUP;
    if (isActiveTabPatient || isActiveTabPatientGroup) {
      if (isBlank(this.selectedData) || isBlank(this.selectedData.userId)) {
        const text = this.commonService.getTranslateData('GENERAL.PATIENT');
        this.showValidationToastMessage(text);
        return;
      }
      if (
        this.sharedService.isMultiAdmissionsEnabled &&
        ((isActiveTabPatient && isBlank(this.selectedData.admissionId)) ||
          (isActiveTabPatientGroup && !this.selectedData?.chatRoomId && isBlank(this.selectedData.admissionId)))
      ) {
        const text = this.commonService.getTranslateData('LABELS.ADMISSION');
        this.showValidationToastMessage(text);
        return;
      }
      if (isActiveTabPatient) {
        this.createChatRoom(this.selectedData);
      } else {
        if (this.selectedData?.allowMultiThreadChat === this.constants.configTrue && !this.selectedData.isSelectTopic) {
          const text = this.commonService.getTranslateData('LABELS.TOPIC');
          this.showValidationToastMessage(text);
          return;
        }
        this.startChatForPDGGroup(key);
      }
    } else if (isActiveTabMessageGroup) {
      if (isBlank(this.selectedData)) {
        const text = this.commonService.getTranslateData('LABELS.GROUP');
        this.showValidationToastMessage(text);
        return;
      }
      if (this.selectedData?.allowMultiThreadChat === this.constants.configTrue && !this.selectedData.isSelectTopic) {
        const text = this.commonService.getTranslateData('LABELS.TOPIC');
        this.showValidationToastMessage(text);
        return;
      }
      this.startChatForPDGGroup(key);
    } else {
      const userIds = this.staffChatRoomRecipients.map((x) => Number(x));
      if (!userIds.length) {
        const text = this.commonService.getTranslateData(this.activeTabValue === this.chatWithTypes.STAFF ? 'GENERAL.STAFF' : 'GENERAL.PARTNER');
        this.showValidationToastMessage(text);
        return;
      }
      this.createChatRoom({ userIds });
    }
  }
  /**
   * clearRecipients resets recipient list
   */
  clearRecipients(): void {
    this.staffChatRoomRecipients = [];
    this.staffChatRoomRecipientsFull = [];
    this.setTitleAndDisplayText();
  }
  /**
   * goToChat for new chat modal click action
   * @param user user data
   */
  goToChat(user): void {
    if (this.isContactDisabled(user)) {
      this.commonService.showToast({
        message: 'ERROR_MESSAGES.USER_CANT_BE_SELECTED',
        color: 'dark'
      });
      return;
    }

    switch (this.activeTabValue) {
      case this.chatWithTypes.MESSAGE_GROUP:
        this.isModalOpen = false;
        this.selectedData = user;
        this.selectedData.isSelectTopic = false;
        this.groupTopicDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_TOPIC');
        this.displayText = user?.groupName;
        if (user.allowMultiThreadChat !== Constants.configTrue) {
          this.selectedData.topic = user?.groupName;
        }
        break;
      case this.chatWithTypes.STAFF:
      case this.chatWithTypes.PARTNER:
        this.addUsersToChatRoom(user);
        break;
      case this.chatWithTypes.PATIENT:
      case this.chatWithTypes.PATIENT_GROUP:
        if (isPresent(user?.alternateContacts) && this.activeTabValue === this.chatWithTypes.PATIENT) {
          this.alternateContactModal(user);
        } else {
          this.groupTopicDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_TOPIC');
          this.selectedData = { ...user, associatedPatient: user?.caregiver_userid };
          this.displayText = this.getLabelText(user);
          this.resetAdmission();
        }
        break;
      case this.chatWithTypes.ROLE:
        this.createChatRoom({
          roleId: user.roleId,
          associatedPatient: this.associatedPatient ?? +this.sharedService.userData.userId,
          admissionId: this.sharedService.isMultiAdmissionsEnabled ? this.admissionDetails?.admissionId : undefined
        });
        break;
      default:
    }
  }
  /**
   * getChatRoomPayload is common function to get chat room payload based on active tab value
   * @param user CreateChatRoomData
   * @returns CreateChatRoomPayload
   */
  getChatRoomPayload(user: CreateChatRoomData): CreateChatRoomPayload {
    let data: CreateChatRoomData;
    let topic;
    switch (this.activeTabValue) {
      case this.chatWithTypes.PATIENT_GROUP:
        topic = user.topic;
        if (this.selectedData.allowMultiThreadChat === Constants.configFalse || !this.selectedData.allowMultiThreadChat) {
          topic = this.commonService.getTranslateDataWithParam(
            this.sharedService.isMultiAdmissionsEnabled ? 'LABELS.PDG_TOPIC_ADMISSION' : 'LABELS.PDG_TOPIC',
            {
              firstName: this.selectedData.firstname,
              lastName: this.selectedData.lastname,
              dob: formatDate(this.selectedData.dob, this.constants?.dateFormat?.mdy),
              mrn: this.selectedData.IdentityValue,
              admissionName: this.admissionDisplayText
            }
          );
        }
        data = {
          topic,
          patientId: Number(user?.userId)
        };
        break;
      case this.chatWithTypes.MESSAGE_GROUP:
        data = {
          topic: user.topic,
          groupId: Number(user?.groupId)
        };
        break;
      case this.chatWithTypes.STAFF:
      case this.chatWithTypes.PARTNER:
        data = {
          userIds: user.userIds
        };
        break;
      case this.chatWithTypes.PATIENT:
        data = {
          userId: Number(user.userId),
          associatedPatient: user?.associatedPatient && Number(user?.associatedPatient)
        };
        break;
      case this.chatWithTypes.ROLE:
        data = {
          roleId: Number(user?.roleId),
          associatedPatient: user?.associatedPatient && Number(user?.associatedPatient)
        };
        break;
      default:
        break;
    }
    return { chatWith: this.activeTabValue, data, admissionId: this.selectedData?.admissionId || undefined };
  }
  /**
   * createChatRoom is common function to
   * @param data
   * @param patientClick
   * @returns
   */
  createChatRoom(chatRoomData: CreateChatRoomData): void {
    const payload = this.getChatRoomPayload(chatRoomData);
    this.httpService
      .doPost({
        endpoint: APIs.createChatRoom,
        payload,
        contentType: 'json',
        loader: true
      })
      .subscribe({
        next: ({ data, success }: GeneralResponse<{ roomId: string }>) => {
          if (isPresent(data?.roomId) && success) {
            const chatRoomId = +data.roomId;
            this.commonService.dismissModal();
            this.redirectToChatRoom(chatRoomId);
            this.sharedService.trackActivity({
              type: Activity.messaging,
              name: Activity.startChatSession,
              des: {
                data: {
                  chatroomId: chatRoomId,
                  activityContent: this.activeTabValue,
                  details: JSON.stringify(payload.data)
                },
                desConstant: Activity.startChatSessionDes
              },
              linkageId: data
            });
          } else {
            this.createChatRoomHandleError({ activityContent: this.activeTabValue, details: JSON.stringify(data) });
          }
        },
        error: ({ data }: GeneralResponse<{ errors: any[] }>) => {
          this.createChatRoomHandleError({ activityContent: this.activeTabValue, details: JSON.stringify(data.errors) });
          this.commonService.dismissModal();
        }
      });
  }
  /**
   * createChatRoomHandleError function to handle error on create chat room
   * @param errorDes
   */
  createChatRoomHandleError(errorDes: { activityContent: string; details: string }): void {
    this.sharedService.trackActivity({
      type: Activity.messaging,
      name: Activity.startChatSession,
      des: {
        data: errorDes,
        desConstant: Activity.startChatSessionFailDes
      }
    });
    this.commonService.dismissModal();
  }
  searchOperations(searchAction: SearchAction): void {
    if (this.activeTabValue === this.chatWithTypes.ROLE) {
      return;
    }
    this.searchText = searchAction.value;
    this.pageCount = Constants.defaultPageCount;
    this.showLoadMore = false;
    this.usersList = [];
    this.completeLoaderEvent();
    if (searchAction.do === DoOperation.search) {
      this.errorMessage = '';
      this.fetchUsers();
    } else {
      this.errorMessage = this.commonService.getTranslateData('PLACEHOLDERS.SEARCH_HERE');
    }
    this.sharedService.trackActivity({
      type: Activity.messaging,
      name: Activity.searchUser,
      des: {
        data: {
          displayName: this.sharedService.userData.displayName,
          keyword: this.searchText
        },
        desConstant: Activity.searchUserChatDes
      }
    });
  }
  emitEventToSearchBarRecipients(): void {
    this.eventsSubject.next(null);
  }
  completeLoaderEvent(): void {
    this.sharedService.isLoading = false;
    this.loaderEvent?.target?.complete();
  }
  loadData(event): void {
    this.loaderEvent = event;
    this.pageCount += 1;
    this.isLoadMore = true;
    this.fetchUsers();
  }

  filterSitesData(data: []): void {
    this.selectedSiteIds = data;
    this.reset();
    if (this.searchText) {
      this.fetchUsers();
    }
  }
  async alternateContactModal(user) {
    const modal = await this.modalController.create({
      component: ChooseContactsComponent,
      componentProps: {
        userEntity: user
      }
    });
    modal.onWillDismiss().then(({ data }) => {
      if (data) {
        this.isModalOpen = false;
        const { selectedUser, patientSelected } = data;
        const associatedData = patientSelected ? {} : { associatedPatient: selectedUser?.patientId };
        this.selectedData = { ...selectedUser, ...associatedData };
        let displayName = '';
        let displayUserType = '';
        if (patientSelected) {
          if (isPresent(user.passwordStatus)) {
            displayUserType = selectedUser.passwordStatus ? this.enrolledUser : this.virtualUser;
          }
          displayName = selectedUser.displayname;
        } else {
          displayName = selectedUser.displayName;
          displayUserType = `(${selectedUser.relation})`;
        }
        this.displayText = `${displayName} ${displayUserType}`;
        if (this.activeTabValue === this.chatWithTypes.PATIENT_GROUP) {
          if (!user?.chatRoomId) {
            this.resetAdmission();
          }
        } else {
          this.resetAdmission();
        }
      }
    });
    return modal.present();
  }
  initialSiteData(data: number[]): void {
    this.selectedSiteIds = data;
  }

  selectAdmission(): void {
    const params = {
      userId: this.selectedData?.associatedPatient ? this.selectedData.associatedPatient : this.selectedData.userId,
      from: this.activeTabValue === this.chatWithTypes.PATIENT_GROUP ? this.chatWithTypes.PATIENT_GROUP : ''
    };
    this.sharedService.selectAdmission(params, undefined, AdmissionComponent, (admission: any) => {
      if (admission) {
        this.selectedData.admissionId = admission.admissionId;
        this.admissionDisplayText = admission.admissionName;
        if (this.activeTabValue === this.chatWithTypes.PATIENT_GROUP) {
          this.selectedData.allowMultiThreadChat = admission.allowMultiChatThread;
          if (!isBlank(admission.chatRoomId)) {
            this.selectedData.chatRoomId = admission.chatRoomId;
          }
          if (!isBlank(admission.chatRoomCreatedBy)) {
            this.selectedData.chatRoomCreatedBy = admission.chatRoomCreatedBy;
          }
          if (!isBlank(admission.topic) && !this.selectedData.allowMultiThreadChat) {
            this.selectedData.topic = admission.topic;
            this.groupTopicDisplayText = admission.topic;
          }
          if (!isBlank(admission?.isParticipant)) {
            this.selectedData.isParticipant = admission.isParticipant.toString();
          }
        }
      }
    });
  }

  startChatForPDGGroup(key: string): void {
    this.modalController.dismiss();
    if (+this.selectedData?.chatRoomId > 0) {
      this.addUserToMessageGroup(this.selectedData);
    } else if (this.selectedData?.newTopicName) {
      this.createChatRoom({ [key]: this.selectedData[key], topic: this.selectedData?.newTopicName });
    } else if (this.activeTabValue === this.chatWithTypes.PATIENT_GROUP) {
      this.createChatRoom({
        [key]: this.selectedData[key],
        topic: this.selectedData?.topic,
        admissionId: this.selectedData?.admissionId
      });
    } else if (this.activeTabValue === this.chatWithTypes.MESSAGE_GROUP) {
      this.createChatRoom({ [key]: this.selectedData[key], topic: this.selectedData?.topic });
    }
  }

  setSelectionText(): void {
    this.displayText = this.staffChatRoomRecipientsFull
      .map((item: any) => {
        return item.name;
      })
      .toString();
  }

  setTitleAndDisplayText(): void {
    if (this.activeTabValue === this.chatWithTypes.STAFF) {
      this.displayText = this.commonService.getTranslateData('TITLES.CHOOSE_STAFF');
      this.modalHeaderTitle = 'LABELS.SELECT_STAFF';
    }
    if (this.activeTabValue === this.chatWithTypes.PARTNER) {
      this.displayText = this.commonService.getTranslateData('TITLES.CHOOSE_PARTNERS');
      this.modalHeaderTitle = 'TITLES.SELECT_PARTNERS';
    }
  }

  showValidationToastMessage(text: string): void {
    const message = this.commonService.getTranslateDataWithParam('VALIDATION_MESSAGES.MANDATORY_SELECTION_WITH_PARAMS', {
      text
    });
    this.commonService.showToast({
      message,
      color: 'dark'
    });
  }

  resetAdmission(): void {
    this.isModalOpen = false;
    if (this.sharedService.isMultiAdmissionsEnabled) {
      this.selectedData.admissionId = undefined;
      this.admissionDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_ADMISSION');
      if (this.activeTabValue === this.chatWithTypes.PATIENT_GROUP) {
        this.groupTopicDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_TOPIC');
      }
    }
  }
}
