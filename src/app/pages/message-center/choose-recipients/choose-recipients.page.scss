.scheduled-icon {
    margin-right: 4px;
    font-size: 16px;
}

.space {
    display: inline-block;
    margin-left: 6px;
}

.error-message {
    padding: 20px;
}

.inbox-right-box {
    width: 150px;
    height: auto;
    position: absolute;
    right: 0px;
}

.invite-go {
    background: #f2f2f2 url(/assets/icon/documents/listview-arrow-right.png) no-repeat 95% center;
    background-size: 15px auto;
    padding-right: 30px;
    font-size: 45px;
}

.common-checkbox {
    min-width: 26px;
}

.groupchat-buttons {
    ion-col {
        ion-button {
            font-size: 14px;
            height: 50px;
            --background: var(--ion-color-de-york);

            ion-img {
                width: 28px;
                margin-right: 12px;
            }
        }
    }
}

.chip-label {
    max-width: 100px;
}

.accordion-group {
    ion-accordion {
        .user-chip {
            max-height: 120px;
            overflow: auto;
            width: 100%;

            ion-icon[name='person-circle-outline'] {
                color: var(--ion-color-de-york);
            }
        }
    }
}

.chat-with-segment {
    overflow-x: auto;
    ion-segment-button {
        width: -webkit-fill-available;
        min-width: fit-content;
    }
}