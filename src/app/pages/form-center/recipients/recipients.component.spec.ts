import { UntypedFormArray, UntypedFormControl, UntypedFormGroup, ReactiveFormsModule } from '@angular/forms';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';
import { RecipientsComponent } from 'src/app/pages/form-center/recipients/recipients.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';
import { OrderByPipe } from 'src/app/pipes/order-by/order-by.pipe';
import { Constants } from 'src/app/constants/constants';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { Config } from 'src/app/constants/config';

describe('RecipientsComponent', () => {
  let component: RecipientsComponent;
  let fixture: ComponentFixture<RecipientsComponent>;
  const mockData: any = [558, 669];
  let navParams: NavParams;
  let sharedService: SharedService;
  let common: CommonService;
  let httpService: HttpService;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  const testData = [
    {
      alternateContacts: [{ userId: '1553167' }],
      role: 'Patient',
      userId: '121371'
    }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [RecipientsComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule
      ],
      providers: [
        OrderByPipe,
        ModalController,
        HttpService,
        CommonService,
        NavParams,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        NativeStorage,
        SQLite,
        PersistentService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    navParams = TestBed.inject(NavParams);
    sharedService = TestBed.inject(SharedService);
    sharedService.localConfig = TestConstants.localConfig;
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    spyOn(sharedService, 'trackActivity').and.stub();
    spyOn(navParams, 'get').and.returnValue({ form: { staffFill: true } });
    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    spyOn(common, 'redirectToPage').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture = TestBed.createComponent(RecipientsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('filterSitesData function should be defined', () => {
    component.filterSitesData(mockData);
    expect(component.filterSitesData).toBeTruthy();
  });
  it('execute sendFormRecipients', () => {
    component.userData = { displayName: '' };
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray([{ userId: '1' }].map((c) => new UntypedFormControl(c)))
    });
    component.recipients = [{ userId: '1' }];
    component.selectedItem = { passwordStatus: true };
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendFormRecipients('');
    expect(component.sendFormRecipients).toBeTruthy();
  });
  it('execute sendFormRecipients', () => {
    component.userData = { displayName: '' };
    const data = [{ userId: '1' }, { userId: 'tag-' }];
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
    });
    component.recipients = data;
    component.selectedItem = { passwordStatus: true };
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendFormRecipients('');
    expect(component.sendFormRecipients).toBeTruthy();
  });
  it('execute sendForm', () => {
    component.sendForm();
    expect(component.sendForm).toBeTruthy();
  });
  it('execute sendForm: practitioner', () => {
    component.formFacing = 'practitioner';
    spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of({ status: 1, send_to: [1, 2] }));
    component.interactionChannel = 'appless';
    component.sendForm();
    expect(component.sendForm).toBeTruthy();
  });
  it('execute viewForm', () => {
    component.selectedAssociatedPatient = [];
    component.viewForm('', '', '');
    expect(component.viewForm).toBeTruthy();
  });
  it('execute searchOperations: staff', () => {
    component.formFacing = 'staff';
    component.searchOperations({ do: 'search' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute searchOperations: patient', () => {
    component.formFacing = 'patient';
    component.searchOperations({ do: 'search' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute searchOperations: add', () => {
    component.searchOperations({ do: 'add' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute searchOperations: reset', () => {
    component.searchOperations({ do: 'reset' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute loadData', fakeAsync(() => {
    component.loadData();
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.loadData).toBeTruthy();
  }));
  it('execute showAddPatient', fakeAsync(() => {
    component.showAddPatient();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.showAddPatient).toBeTruthy();
  }));
  it('execute getStatusWarning: submitjson', () => {
    component.getStatusWarning({
      data: { defaultFromFilingCenterSubmitjson: true, progressNoteIntegration: true },
      status: { statusMessage: 'Success', integrationStatus: 'Enabled' }
    });
    expect(component.getStatusWarning).toBeTruthy();
  });
  it('execute getStatusWarning: submit', () => {
    component.getStatusWarning({
      data: { defaultFromFilingCenterSubmit: true, progressNoteIntegration: true },
      status: { statusMessage: 'Success', integrationStatus: 'Enabled' }
    });
    expect(component.getStatusWarning).toBeTruthy();
  });
  it('execute getStatusWarning: default', () => {
    component.getStatusWarning({
      data: {},
      status: {}
    });
    expect(component.getStatusWarning).toBeTruthy();
  });
  it('execute viewPatientForm:  staffFill true', () => {
    component.form = { staffFill: 'true' };
    component.recipients = [{ userId: 11 }];
    component.viewPatientForm([11]);
    expect(component.viewPatientForm).toBeTruthy();
  });
  it('execute viewPatientForm: staffFill false', () => {
    component.form = { staffFill: 'false' };
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.viewPatientForm([11]);
    expect(component.viewPatientForm).toBeTruthy();
  });
  it('execute checkIntegrationStatus: alternate contact', fakeAsync(() => {
    component.storeSelectedValue = ['43--44'];
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  }));

  it('execute checkIntegrationStatus', fakeAsync(() => {
    component.storeSelectedValue = ['43'];
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  }));
  it('execute checkIntegrationStatus: api throw error', async () => {
    component.storeSelectedValue = ['43'];
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    spyOn(common, 'showAlert').and.resolveTo(true);
    await component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  });

  it('execute checkIntegrationStatus: not recipient', fakeAsync(() => {
    component.storeSelectedValue = [];
    component.recipients = [];
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  }));
  it('execute checkIntegrationStatus: with patients', () => {
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray([{ userId: 1 }].map((c) => new UntypedFormControl(c)))
    });
    component.recipients = [{ userId: 'tag-abc' }];
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  });

  it('should handle alternate contact id', () => {
    component.form = { staffFill: 'false' };
    component.storeSelectedValue = ['1--2'];
    sharedService.isEnableConfig = () => false;
    spyOn(httpService, 'doPost').and.returnValue(of({}));
    spyOn(component, 'viewPatientForm');
    component.checkIntegrationStatus();
    expect(httpService.doPost).toHaveBeenCalledWith(jasmine.objectContaining({ payload: jasmine.objectContaining({ patient_id: '2' }) }));
    expect(component.viewPatientForm).toHaveBeenCalled();
  });

  it('execute appendUserTags: patient', () => {
    component.formFacing = 'patient';
    component.appendTags = true;
    component.tagDetails = [{}];
    component.appendUserTags();
    expect(component.appendUserTags).toBeTruthy();
  });
  it('execute appendUserTag: not patient', () => {
    component.appendUserTags();
    expect(component.appendUserTags).toBeTruthy();
  });
  it('execute goBack', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });
  it('execute sendFormToInAppLess', () => {
    const data = [{ userId: '1' }, { userId: '2' }, { userId: 'tag-12' }];
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
    });
    component.recipients = data;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendFormToInAppLess('');
    expect(component.sendFormToInAppLess).toBeTruthy();
  });
  it('execute sendPractitionerForm: practitioner facing', () => {
    component.filteredListData = [3];
    component.storeSelectedData = [{ userid: 3 }];
    component.form = { staffFill: 'true' };
    component.formFacing = Constants.practitioner;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendPractitionerForm('');
    expect(component.sendPractitionerForm).toBeTruthy();
  });
  it('execute sendPractitionerForm: multiple recipients', fakeAsync(() => {
    component.filteredListData = [3, 4];
    component.storeSelectedData = [{ userid: 3 }];
    component.form = { staffFill: 'true' };
    component.formFacing = Constants.practitioner;
    component.recipientsCompletedDocumentIDs = '';
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendPractitionerForm('');
    expect(component.sendPractitionerForm).toBeTruthy();
  }));
  it('execute sendPractitionerForm', () => {
    const data = [{ userId: '1' }];
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
    });
    component.recipients = data;
    component.form = { staffFill: 'true' };
    component.sendPractitionerForm('');
    expect(component.sendPractitionerForm).toBeTruthy();
  });
  it('execute sendFormToInAppLess: practitioner', () => {
    const data = [{ userId: '1' }, { userId: '2' }, { userId: 'tag-12' }];
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
    });
    component.recipients = data;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendFormToInAppLess('practitioner');
    expect(component.sendFormToInAppLess).toBeTruthy();
  });
  describe('getUsersByTenantRoleWithTagId', () => {
    let expectedBody;
    beforeEach(() => {
      expectedBody = {
        isTenantRoles: true,
        roleId: component.userData?.roleId,
        pageCount: component.pageCount,
        tagsId: component.tagId,
        formRecipients: Constants.formRecipients,
        searchKeyword: component.searchKeyword,
        siteIds: '0',
        nursingAgencies: '',
        reoleSearchNeeded: false,
        status: Constants.notRejected,
        admissionId: undefined
      };
    });
    it('execute getUsersByTenantRoleWithTagId', () => {
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([{ id: 1 }]));
      component.getUsersByTenantRoleWithTagId();
      expect(component.getUsersByTenantRoleWithTagId).toBeTruthy();
    });
    it('execute getUsersByTenantRoleWithTagId: no items', () => {
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      expect(component.getUsersByTenantRoleWithTagId).toBeTruthy();
    });
    it('execute getUsersByTenantRoleWithTagId: throw error', () => {
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(throwError(''));
      component.getUsersByTenantRoleWithTagId();
      expect(component.getUsersByTenantRoleWithTagId).toBeTruthy();
    });
    it('should include needVirtualPatients when Config.enableApplessModel is enabled', () => {
      sharedService.userData.config[Config.enableApplessModel] = '1';
      expectedBody.needVirtualPatients = true;
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    });

    it('should not include needVirtualPatients when Config.enableApplessModel is disabled', () => {
      sharedService.userData.config[Config.enableApplessModel] = '0';
      component.getUsersByTenantRoleWithTagId();
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    });

    it('should prevent patient click when user is not contactable', () => {
      component.formFacing = Constants.patientValue;
      sharedService.userData.config[Config.enableApplessModel] = '0';
      const eachObj: any = {
        status: 7,
        userid: '528',
        passwordStatus: false,
        countryCode: '+1',
        mobile: '111111111',
        email: '<EMAIL>',
        enable_email_notifications: '0',
        enable_sms_notifications: '0',
        isNotContactable: true,
        alternateContacts: [{}]
      };
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([eachObj]));
      component.getUsersByTenantRoleWithTagId();
      expect(eachObj.isNotContactable).toBeDefined();
      expect(component.recipients[0].isNotContactable).toBeTruthy();
    });

    it('should prevent alternate user click when user is not contactable', () => {
      component.formFacing = Constants.patientValue;
      sharedService.userData.config[Config.enableApplessModel] = '0';
      const eachObj: any = {
        status: 7,
        userid: '528',
        passwordStatus: false,
        countryCode: '+1',
        mobile: '111111111',
        email: '<EMAIL>',
        enable_email_notifications: '1',
        enable_sms_notifications: '1',
        isNotContactable: true,
        alternateContacts: [
          {
            status: 7,
            userid: '528',
            passwordStatus: false,
            countryCode: '+1',
            mobile: '111111111',
            email: '<EMAIL>',
            enable_email_notifications: '0',
            enable_sms_notifications: '0',
            isNotContactable: true
          }
        ]
      };
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([eachObj]));
      component.getUsersByTenantRoleWithTagId();
      expect(eachObj.alternateContacts[0].isNotContactable).toBeDefined();
      expect(component.recipients[0].alternateContacts[0].isNotContactable).toBeTruthy();
    });

    it('should include nursingAgencies when userData has nursing_agencies', () => {
      const nursing_agencies = 12345;
      component.userData.nursing_agencies = nursing_agencies;
      expectedBody.nursingAgencies = nursing_agencies;
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    });

    it('should include nursingAgencies with empty value when userData does not have nursing_agencies', () => {
      component.userData.nursing_agencies = '';
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    });
  });

  it('execute getAssociatedPatients', () => {
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of([{ id: 1 }]));
    component.getAssociatedPatients();
    expect(component.getAssociatedPatients).toBeTruthy();
  });
  it('execute getAssociatedPatients: no items', () => {
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of([]));
    component.getAssociatedPatients();
    expect(component.getAssociatedPatients).toBeTruthy();
  });
  it('execute getAssociatedPatients: throw error', () => {
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(throwError(''));
    component.getAssociatedPatients();
    expect(component.getAssociatedPatients).toBeTruthy();
  });
  it('execute getAssociatedPatients: throw error', () => {
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of({ status: 401 }));
    component.getAssociatedPatients();
    expect(component.getAssociatedPatients).toBeTruthy();
  });
  it('execute clearAll: reset/append checked boolean to false', () => {
    component.recipients = [{ id: 43 }];
    component.clearAll('');
    expect(component.recipients).toEqual([{ id: 43, checked: false }]);
  });
  it('execute clearAll: reset store selected value', () => {
    component.storeSelectedValue = [{ id: 43 }];
    component.clearAll('ButtonClick');
    expect(component.storeSelectedValue).toEqual([]);
  });
  it('execute getTagDetails', () => {
    spyOn(httpService, 'doPost').and.returnValue(of([{ id: 1 }]));
    component.getTagDetails();
    expect(component.getTagDetails).toBeTruthy();
  });
  it('execute ngOnInit: patient', () => {
    component.drivenBy = 'patient';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit: staff', () => {
    component.formFacing = 'staff';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute isDisabled', () => {
    const data = component.isDisabled;
    expect(data).toBeTrue();
  });
  it('execute alternateSelect', () => {
    component.recipients = testData;
    component.alternateSelect(0, 0);
    expect(component.alternateSelect).toBeTruthy();
  });
  it('execute alternateChecked', () => {
    component.recipients = testData;
    component.alternateChecked(0, 0);
    expect(component.alternateChecked).toBeTruthy();
  });
  it('viewForm function shoud defined and check patient facing form and caregiver role ', () => {
    const mockForm = {
      confirmActionPrefill: true,
      externalFileExchange: false,
      id: 9380033,
      name: 'deep_form',
      populatePreviousSubmission: true,
      staffFill: 'true'
    };
    component.recipients = [{ role: Constants.roleName.caregiver }];
    component.viewForm('patient', {}, mockForm, 0);
    expect(component.viewForm).toBeTruthy();
  });
  it('execute downloadFormRecipients', () => {
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.downloadFormRecipients('');
    expect(component.downloadFormRecipients).toBeTruthy();
  });
  it('should return early if recipient.isNotContactable is true', () => {
    const recipient = { isNotContactable: true };
    const form = {};
    const facing = 'patient';
    const index = 1;
    component.storeSelectedData = [];
    spyOn(component, 'viewForm').and.callThrough();
    spyOn(component, 'showUserNotContactableMessage').and.callThrough();
    component.viewForm(facing, recipient, form, index);
    expect(component.viewForm).toHaveBeenCalledWith(facing, recipient, form, index);
    expect(component.storeSelectedData).toEqual([]);
    expect(component.showUserNotContactableMessage).toHaveBeenCalled();
  });
  it('should return early if recipient.alternateContact.isNotContactable is true when multi admission on', () => {
    const recipient = { isNotContactable: false, alternateContacts: [{ isNotContactable: true }] };
    const form = {};
    const facing = 'patient';
    const index = 1;
    const altIndex = 0;
    component.storeSelectedData = [];
    spyOn(component, 'viewForm').and.callThrough();
    spyOn(component, 'showUserNotContactableMessage').and.callThrough();
    component.viewForm(facing, recipient, form, index, altIndex);
    expect(component.viewForm).toHaveBeenCalledWith(facing, recipient, form, index, altIndex);
    expect(component.storeSelectedData).toEqual([]);
    expect(component.showUserNotContactableMessage).toHaveBeenCalled();
  });
  it('should return early if recipient.alternateContact.isNotContactable is true when multi admission off', () => {
    component.recipients = [{ isNotContactable: false, alternateContacts: [{ isNotContactable: true }] }];
    const index = 0;
    const altIndex = 0;
    spyOn(component, 'alternateSelect').and.callThrough();
    spyOn(component, 'showUserNotContactableMessage').and.callThrough();
    component.alternateSelect(index, altIndex);
    expect(component.alternateSelect).toHaveBeenCalledWith(index, altIndex);
    expect(component.showUserNotContactableMessage).toHaveBeenCalled();
  });
});
