import { Component, OnInit } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { isBlank, formatDate, isPresent } from 'src/app/utils/utils';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NavParams, ModalController, NavController } from '@ionic/angular';
import { Constants, IntegrationType } from 'src/app/constants/constants';
import { UntypedFormGroup, UntypedFormBuilder, UntypedFormArray, UntypedFormControl } from '@angular/forms';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Config } from 'src/app/constants/config';
import { Activity } from 'src/app/constants/activity';
import { AddVirtualPatientComponent } from 'src/app/pages/user/add-virtual-patient/add-virtual-patient.component';
import { PageRoutes } from 'src/app/constants/page-routes';
import { CompletedDocumentRecipientsComponent } from 'src/app/components/completed-document-recipients/completed-document-recipients.component';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { zip } from 'rxjs';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';
import { UserService } from 'src/app/services/user-service/user.service';

@Component({
  selector: 'app-recipients',
  templateUrl: './recipients.component.html',
  styleUrls: ['./recipients.component.scss']
})
export class RecipientsComponent implements OnInit {
  userData: any;
  tagDetails: any;
  recipients: any = [];
  formFacing: string;
  formType: string;
  tagId: number;
  headerTitle: string;
  pageCount = Constants.defaultPageCount;
  showLoadMore: boolean;
  selectRecipientForm: UntypedFormGroup;
  form: any = {};
  formId: number;
  formName: string;
  defaultMsgTitle: string;
  mrn: string;
  searchKeyword: string;
  addPatient = false;
  drivenBy: string;
  selectedAssociatedPatient: any;
  isBlank: any;
  defaultMsg: string;
  multipleRecipientMsg: any;
  constants: any;
  tagRecipient: any = [];
  appendTags = true;
  filteredListData: any = [];
  patientDrivenReqBody = {
    siteIds: [0]
  };
  errorMessage = '';
  createPatientLinkMessage = '';
  isLoadMore = false;
  isStaffFilling = false;
  isEnableMultiSite = false;
  isEnableMultiAdmissions = false;
  interactionChannel: string;
  selectedItem: any = {};
  storeSelectedValue = [];
  storeSelectedData: any = [];
  alternateSelectedId: string;
  associatedPatient: boolean;
  recipientsCompletedDocumentIDs = '';
  admissionId;
  siteLabel = '';
  constructor(
    private readonly httpService: HttpService,
    private readonly sharedService: SharedService,
    private readonly navParams: NavParams,
    private readonly modalController: ModalController,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly common: CommonService,
    private readonly persistentService: PersistentService,
    private readonly userService: UserService,
    private navController: NavController
  ) {
    this.userData = this.sharedService.userData;
    this.formFacing = this.navParams.get('formFacing');
    this.formType = this.navParams.get('formType');
    this.tagId = this.navParams.get('tagId');
    this.form = this.navParams.get('form');
    this.drivenBy = this.navParams.get('drivenBy');
    this.selectedAssociatedPatient = this.navParams.get('selectedAssociatedPatient');
    this.formId = this.form ? this.form.id : '';
    this.formName = this.form ? this.form.name : '';
    this.selectRecipientForm = this.formBuilder.group({
      recipientList: new UntypedFormArray([]),
      alternateContactsList: new UntypedFormArray([])
    });
    this.defaultMsgTitle = 'MESSAGES.ARE_YOU_SURE';
    this.defaultMsg = 'MESSAGES.SINGLE_RECIPIENT';
    this.multipleRecipientMsg = 'MESSAGES.MULTIPLE_RECIPIENTS';
    this.mrn = this.common.getTranslateData('GENERAL.MRN');
    this.isBlank = isBlank;
    this.constants = Constants;
    this.isStaffFilling = Boolean(this.form?.staffFill);
    this.isEnableMultiSite = this.sharedService.isEnableConfig(Config.enableMultiSite);
    this.isEnableMultiAdmissions = this.sharedService.isEnableConfig(Config.enableMultiAdmissions);
    this.recipientsCompletedDocumentIDs = this.navParams.get('copyOfRecipient');
  }

  ngOnInit(): void {
    if (this.drivenBy === Constants.patientValue) {
      this.headerTitle = 'TITLES.SELECT_ASSOCIATED_PATIENT';
      this.getAssociatedPatients();
      this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITE');
    } else if (
      this.formFacing === Constants.staffValue ||
      (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
    ) {
      this.addPatient = true;
      this.headerTitle = 'TITLES.CHOOSE_ASSOCIATED_PATIENT';
      this.getAssociatedPatients();
      this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITE');
    } else {
      this.headerTitle = 'TITLES.CHOOSE_RECIPIENTS';
      if (!this.tagDetails) {
        this.getTagDetails();
      }
      this.getUsersByTenantRoleWithTagId();
      if (this.formFacing === Constants.patientValue) {
        this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITE');
      } else this.siteLabel = 'LABELS.SITE';
    }
  }
  getTagDetails(): void {
    const reqData = {
      tagId: this.tagId
    };
    this.httpService
      .doPost({
        endpoint: APIs.getTagDetails,
        payload: {},
        extraParams: reqData,
        contentType: 'form',
        loader: true
      })
      .subscribe((tagDetails) => {
        if (!isBlank(tagDetails)) {
          this.tagDetails = tagDetails;
        }
      });
  }
  getUsersByTenantRoleWithTagId(): void {
    this.showLoadMore = false;
    const selectedSiteIds =
      this.patientDrivenReqBody && this.patientDrivenReqBody.siteIds ? this.patientDrivenReqBody.siteIds.toString() : Constants.defaultSiteId;

    const body = {
      isTenantRoles: this.formFacing === Constants.staffValue ? undefined : true,
      roleId: this.userData?.roleId,
      pageCount: this.pageCount,
      tagsId: this.tagId,
      formRecipients: Constants.formRecipients,
      searchKeyword: this.searchKeyword,
      siteIds: this.associatedPatient && this.formFacing === Constants.practitioner ? this.selectedAssociatedPatient.siteId : selectedSiteIds,
      nursingAgencies: this.userData?.nursing_agencies || '',
      reoleSearchNeeded: false,
      status: Constants.notRejected,
      admissionId: this.sharedService.isMultiAdmissionsEnabled ? this.navParams.get('admissionId') : undefined,
      ...(this.sharedService.isEnableConfig(Config.enableApplessModel) && { needVirtualPatients: true })
    };
    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    if (body.siteIds === Constants.defaultSiteId && isPresent(localSelectedSites)) {
      body.siteIds = JSON.parse(localSelectedSites).toString();
    } else if (isBlank(body.siteIds)) {
      body.siteIds = Constants.defaultSiteId;
    }

    this.sharedService.getUsersByTenantRoleWithTagId(body).subscribe(
      (recipients) => {
        if (!isBlank(recipients)) {
          this.errorMessage = '';
          this.showLoadMore = recipients.length >= Constants.defaultLimit;
          const recipientsData = recipients.map((r) => ({
            ...r,
            sitename: r.sitename ? r.sitename : r.siteName,
            displayDob: !isBlank(r.dob) ? formatDate(r.dob, Constants.dateFormat.mmddyy) : '',
            displayName: `${this.common.getPatientDisplayName(r, Constants.patientValue)}`,
            checked: false,
            isNotContactable: !this.userService.isUserContactable(r),
            isContactNotOptedIn: !this.userService.isContactOptedIn(r),
            alternateContacts: !isBlank(r.alternateContacts)
              ? r.alternateContacts.map((altUsr) => ({
                  ...altUsr,
                  isNotContactable: !this.userService.isUserContactable(altUsr),
                  isContactNotOptedIn: !this.userService.isContactOptedIn(altUsr)
                }))
              : []
          }));
          this.recipients = this.recipients.concat(recipientsData);
          const updatedData = this.recipients.map((r: any) => ({
            ...r,
            checked: this.storeSelectedValue?.length > 0 && this.storeSelectedValue.includes(r.userId || r.userid)
          }));
          this.recipients = updatedData;
          if (isBlank(this.recipients)) {
            this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
          }
          this.appendUserTags();
        } else {
          if (isBlank(this.recipients)) {
            this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
          }
          this.appendUserTags();
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
        this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
      }
    );
  }
  appendUserTags(): void {
    if (this.appendTags && this.formFacing === Constants.patientValue && !isBlank(this.tagDetails)) {
      const patientTags = this.tagDetails.map((r) => ({
        ...r,
        displayName: !isBlank(r.tag_name) ? `${r.tag_name} [${this.common.getTranslateData('GENERAL.USER_TAG')}]` : ''
      }));
      this.createForm(this.recipients.concat(patientTags));
    } else {
      this.createForm(this.recipients);
    }
  }

  goBack(): void {
    this.modalController.dismiss();
  }

  getAssociatedPatients(): void {
    this.showLoadMore = false;
    const payload = {
      pageCount: this.pageCount,
      tagId: this.tagId,
      searchKeyword: this.searchKeyword,
      siteIds: this.patientDrivenReqBody && this.patientDrivenReqBody.siteIds ? this.patientDrivenReqBody.siteIds.toString() : Constants.defaultSiteId
    };
    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    if (payload.siteIds === Constants.defaultSiteId && isPresent(localSelectedSites)) {
      payload.siteIds = JSON.parse(localSelectedSites).toString();
    } else if (isBlank(payload.siteIds)) {
      payload.siteIds = Constants.defaultSiteId;
    }

    this.sharedService.getAssociatedPatientsByTagId(payload).subscribe(
      (recipients) => {
        if (!isBlank(recipients)) {
          if (recipients.status === 401) {
            this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
            return false;
          }
          this.errorMessage = '';
          this.createPatientLinkMessage = '';
          this.showLoadMore = recipients.length >= Constants.defaultLimit;
          const recipientsData = recipients.map((r) => ({
            ...r,
            displayDob: !isBlank(r.dob) ? formatDate(r.dob, Constants.dateFormat.mmddyy) : '',
            displayName: this.common.getPatientDisplayName(r, Constants.patientValue, Constants.displayNamePatientCaregiverFormat)
          }));
          this.recipients = this.recipients.concat(recipientsData);
          this.createForm(this.recipients);
        } else if (isBlank(this.recipients)) {
          this.errorMessage = this.userService.showCommonMessage(this.addPatient, '', false);
          this.createPatientLinkMessage = this.addPatient ? this.common.getTranslateData('MESSAGES.TO_CREATE_NEW_PATIENT') : '';
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
        this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
      }
    );
  }
  loadMore(): void {
    this.appendTags = false;
    this.pageCount++;
    this.formFacing === Constants.staffValue ||
      this.drivenBy === Constants.patientValue ||
      (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
      ? this.getAssociatedPatients()
      : this.getUsersByTenantRoleWithTagId();
  }

  createForm(list: []): void {
    const controls = list.map((c: any) => new UntypedFormControl(c.checked || false));
    this.selectRecipientForm = this.formBuilder.group({
      recipientList: new UntypedFormArray(controls),
      alternateContactsList: new UntypedFormArray(controls)
    });
  }
  clearAll(value?: string): void {
    this.selectRecipientForm.reset();
    this.alternateSelectedId = undefined;
    if (this.formType === Constants.formTypes.offline && this.isEnableMultiAdmissions) {
      this.recipients.map(
        (item: { checked: boolean; admissionId: string; admissionName: string; admissionSiteName: string; admissionSiteId: string }) => {
        item.checked = false;
        item.admissionId = '';
        item.admissionName = '';
        item.admissionSiteName = '';
        item.admissionSiteId = '';
        return item;
      });
    } else {
      this.recipients.map((item) => (item.checked = false));
    }
    if (value === 'ButtonClick') {
      this.storeSelectedValue = [];
    }
  }
  get isDisabled(): boolean {
    return this.storeSelectedValue.length === 0;
  }
  sendPractitionerForm(formFacing: string): void {
    const selectedRecipient = this.storeSelectedData.filter((user: any) => {
      return user.userid === this.filteredListData[0];
    });
    if (this.form.staffFill === 'true' && this.filteredListData.length <= 1) {
      if (this.formFacing === Constants.practitioner) {
        this.modalController.dismiss({
          form: {
            name: this.formName,
            id: this.formId,
            populatePreviousSubmission: this.form.populatePreviousSubmission,
            confirmActionPrefill: this.form.confirmActionPrefill,
            staffFilling: this.form.staffFill,
            admissionId: this.admissionId
          },
          recipient: selectedRecipient[0],
          associatedPatient: this.selectedAssociatedPatient,
          facing: formFacing,
          interactionChannel: this.interactionChannel,
          copyOfRecipient: this.recipientsCompletedDocumentIDs
        });
      }
    } else {
      const message = this.filteredListData.length <= 1 ? this.defaultMsg : this.multipleRecipientMsg;
      const alertData = {
        message,
        header: this.defaultMsgTitle
      };
      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          // Send form here.
          this.sendForm(this.recipientsCompletedDocumentIDs);
          const ids = [];
          if (this.filteredListData.length) {
            this.filteredListData.forEach((value) => {
              ids.push(`(${value})`);
            });
          }
          this.sharedService.trackActivity({
            type: Activity.forms,
            name: Activity.sendForm,
            des: {
              data: {
                displayName: this.userData.displayName,
                formName: this.formName,
                formId: this.formId,
                recipientId: ids.join(',')
              },
              desConstant: this.filteredListData.length <= 1 ? Activity.sendFormDes : Activity.sendFormMultipleUsersDes
            }
          });
        }
      });
    }
  }

  downloadFormRecipients(formFacing: string): void {
    let filteredListArray = [];
    let filteredRecipientsWithAdmission = [];
    if (this.storeSelectedValue.length > 0) {
      filteredListArray = [...new Set([...this.filteredList, ...this.storeSelectedValue])];
    } else {
      filteredListArray = this.filteredList;
    }
    if (this.isEnableMultiAdmissions) {
      filteredRecipientsWithAdmission = this.recipients.filter((item: any) => item?.checked);
    }
    if (filteredListArray.length > 0) {
      const alertData = {
        message: filteredListArray.length === 1 ? 'MESSAGES.SINGLE_OFFLINE_DOWNLOAD_RECIPIENT' : 'MESSAGES.MULTIPLE_OFFLINE_DOWNLOAD_RECIPIENTS',
        header: this.defaultMsgTitle
      };

      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          const forms = [];
          filteredListArray.forEach((recipientId: any, index: number) => {
            const params = {
              form_id: this.formId,
              tenant_id: this.userData.tenantId,
              recipientId,
              admissionId: this.isEnableMultiAdmissions ? filteredRecipientsWithAdmission[index]?.admissionId : undefined
            };
            forms.push(this.httpService.doGet({ endpoint: APIs.getHtmlForm, extraParams: params, loader: true, useBaseAs: 'forms' }));
          });
          this.sharedService.loaderMessage = filteredListArray.length === 1 ? 'LOADER_MESSAGES.FILE_IS_DOWNLOADING' : 'LOADER_MESSAGES.FILES_ARE_DOWNLOADING';
          zip(...forms).subscribe((res) => {
            res.forEach((form, index) => {
              this.common.renderIframe(form);
              const userData = this.recipients.find((recipient) => {
                let userId = '';
                if (recipient.role === Constants.roleName.caregiver) {
                  userId = recipient.caregiver_userid;
                } else if (recipient.userId) {
                  userId = recipient.userId;
                } else {
                  userId = `tag-${recipient.id}`;
                }
                return userId === filteredListArray[index];
              });
              let formData = {
                firstName: '',
                lastName: '',
                site: this.isEnableMultiAdmissions ? userData?.admissionSiteName : userData.sitename,
                dob: '',
                mrn: '',
                id: filteredListArray[index],
                formName: this.formName,
                formContent: form,
                admissionId: this.isEnableMultiAdmissions ? userData?.admissionId : '',
                admissionName: this.isEnableMultiAdmissions ? userData?.admissionName : ''
              };
              if (!filteredListArray[index].startsWith('tag-')) {
                formData = {
                  ...formData,
                  firstName: userData.role === Constants.roleName.caregiver ? userData.c_fname : userData.firstname,
                  lastName: userData.role === Constants.roleName.caregiver ? userData.c_lname : userData.lastname,
                  dob: userData.role === Constants.roleName.caregiver ? userData.caregiver_dob : userData.dob,
                  mrn: userData.role === Constants.roleName.caregiver ? userData.caregiverIdentityValue : userData.IdentityValue
                };
              }
              const siteId = this.isEnableMultiAdmissions ? userData?.admissionSiteId : userData.siteId;
              this.persistentService.addOfflineForm(`${this.formId}`, siteId.toString(), JSON.stringify(formData)).then(() => {
                if (index === res.length - 1) {
                  this.sharedService.loaderMessage = '';
                  this.common.showMessage(this.common.getTranslateData('MESSAGES.OFFLINE_DOWNLOAD_SUCCESSFULLY'));
                  this.goBack();
                  this.navController.navigateForward(PageRoutes.offlineForms);
                }
              });
            });
          });
        }
      });
    }
  }

  sendFormRecipients(formFacing: string): void {
    const appLessMode = Boolean(this.sharedService.isEnableConfig(Config.enableApplessModel));
    let filteredListArray = [];

    if (this.storeSelectedValue.length > 0) {
      filteredListArray = [...new Set([...this.filteredList, ...this.storeSelectedValue])];
    } else {
      filteredListArray = this.filteredList;
    }
    if (filteredListArray.length <= 1 && appLessMode && this.selectedItem.passwordStatus && !this.selectedItem.isContactNotOptedIn) {
      let buttons = [];
      const applessButtonText = [this.common.getTranslateData('BUTTONS.APP_LESS'), this.common.getTranslateData('BUTTONS.MAGIC_LINK')].join(' ');
      if (this.selectedItem && !this.selectedItem.passwordStatus) {
        buttons = [{ text: applessButtonText, confirm: true, id: 'app-less' }];
      } else {
        buttons = [
          { text: applessButtonText, confirm: true, role: 'done', id: 'app-less' },
          { text: this.common.getTranslateData('BUTTONS.IN_APP'), confirm: false, role: 'done', id: 'in-app' }
        ];
        this.common
          .showAlert({
            message: 'MESSAGES.SEND_FORM_VIA',
            buttons,
            cssClass: 'send-form-via-modal',
            backDrop: true
          })
          .then((confirmation) => {
            this.interactionChannel = confirmation ? Constants.appless : Constants.mobileapp;
            this.sendFormToInAppLess(formFacing);
          });
      }
    } else {
      this.sendFormToInAppLess(formFacing);
    }
  }

  sendFormToInAppLess(formFacing: string) {
    const filteredListArray = [...new Set([...this.filteredList, ...this.storeSelectedValue])];
    this.filteredListData = filteredListArray;
    if (formFacing === Constants.practitioner) {
      const ids = [];
      if (this.filteredListData.length) {
        this.filteredListData.forEach((value) => {
          ids.push(`(${value})`);
        });
      }
      const activityData = {
        displayName: this.userData.displayName,
        recipientId: ids.join(','),
        recipientName: '',
        formName: this.formName,
        formId: this.formId
      };
      if (this.associatedPatient) {
        activityData.recipientName = this.selectedAssociatedPatient.displayname;
        this.sharedService.trackActivity({
          type: Activity.forms,
          name: Activity.selectRecipient,
          des: { data: activityData, desConstant: Activity.selectRecipientDes }
        });
      } else if (this.filteredListData.length <= 1 && this.selectedItem) {
        activityData.recipientName = this.selectedItem.displayname;
        this.sharedService.trackActivity({
          type: Activity.forms,
          name: Activity.selectRecipient,
          des: { data: activityData, desConstant: Activity.selectRecipientDes }
        });
      }
      this.sendPractitionerForm(formFacing);
    } else {
      let tagRecipient;
      this.filteredList.map((value: any, index: number) => {
        if (value.includes('tag-')) {
          tagRecipient = this.filteredListData.splice(index, 1);
        }
      });
      if (tagRecipient) {
        this.tagRecipient = tagRecipient.map((value: any) => {
          return value.replace('tag-', '');
        });
      } else {
        this.tagRecipient = [];
      }

      if (filteredListArray.length <= 1 && this.tagRecipient.length < 1) {
        // Check integration status if only one recipient selected.
        // TODO : Check activity tracking based on in app and appless.

        this.checkIntegrationStatus();
        this.sharedService.trackActivity({
          type: Activity.forms,
          name: Activity.sendForm,
          des: {
            data: {
              displayName: this.userData.displayName,
              formName: this.formName,
              formId: this.formId,
              recipientId: filteredListArray
            },
            desConstant: Activity.sendFormDes
          }
        });
      } else if (filteredListArray.length > 1 || this.tagRecipient.length >= 1) {
        // No need to call integration status api.
        const alertData = {
          message: this.multipleRecipientMsg,
          header: this.defaultMsgTitle
        };
        this.common.showAlert(alertData).then((confirmation) => {
          if (confirmation) {
            // Send form here.
            this.sendForm();
            const ids = [];
            filteredListArray.forEach((value) => {
              ids.push(`(${value})`);
            });
            const data = {
              displayName: this.userData.displayName,
              formName: this.formName,
              formId: this.formId,
              recipientId: ids.join(',')
            };
            this.sharedService.trackActivity({
              type: Activity.forms,
              name: Activity.sendApplessForm,
              des: { data, desConstant: Activity.sendApplessFormDes }
            });
          }
        });
      }
    }
  }
  get filteredList(): [] {
    return this.selectRecipientForm.value.recipientList
      .map((v: any, i: number) =>
        v
          ? isPresent(this.recipients[i]?.caregiver_userid)
            ? this.recipients[i].caregiver_userid
            : this.recipients[i]?.userId
              ? this.recipients[i].userId
              : `tag-${this.recipients[i]?.id}`
          : null
      )
      .filter((v: any) => v !== null);
  }

  // Form Facing = 'patientValue', 'staffFacing'
  checkIntegrationStatus(): void {
    let alterNateContactId;
    if (this.storeSelectedValue.length === 1) {
      if (this.storeSelectedValue[0].includes('--')) {
        const getVal: any = this.storeSelectedValue[0].split('--');
        alterNateContactId = getVal.length ? getVal[1] : '';
      } else {
        alterNateContactId = this.storeSelectedValue[0];
      }
    }
    let patient = [];
    patient = this.filteredList.length ? this.filteredList : alterNateContactId ? [alterNateContactId] : this.filteredList;
    if (!isBlank(patient) && !this.sharedService.isEnableConfig(Config.enableSftp)) {
      if (this.form.staffFill === 'true') {
        this.viewPatientForm(patient);
        return;
      }
      // sftp Integration not enabled check.
      const body = {
        patient_id: patient[0],
        form_id: this.formId,
        admissionId: this.isEnableMultiAdmissions ? this.admissionId : undefined,
        action: IntegrationType.FORM_SUBMIT
      };
      this.sharedService.isLoading = true;
      this.httpService.doPost({ endpoint: APIs.getIntegrationStatus, payload: body, loader: false, skipErrorHandling: true }).subscribe(
        () => {
          this.sharedService.isLoading = false;
          // Send form here.
          this.viewPatientForm(patient);
        },
        (error: any) => {
          this.sharedService.isLoading = false;
          this.common
            .showAlert({
              message: `<center>
              <strong>${error.status.message}</strong> <br/><br/> ${this.common.getTranslateData('MESSAGES.YOU_CAN_CONTINUE_ANYWAY')}.
              </center>`,
              header: '',
              cssClass: 'common-alert visit-alert ',
              buttons: [
                {
                  text: this.common.getTranslateData('BUTTONS.GO_BACK')
                },
                {
                  text: this.common.getTranslateData('BUTTONS.CONTINUE_ANYWAY')
                }
              ]
            })
            .then((confirmation) => {
              if (confirmation) {
                this.viewPatientForm(patient);
              }
          });
        }
      );
    } else {
      const alertData = {
        message: this.defaultMsg,
        header: this.defaultMsgTitle
      };
      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          // Send form here.
          this.viewPatientForm(patient);
        }
      });
    }
  }
  viewPatientForm(patient: any): void {
    if (this.form.staffFill === 'true') {
      const recipient = this.recipients.filter((user: any) => {
        return user.userid === patient[0];
      });

      if (this.formFacing === Constants.patientValue || this.formFacing === Constants.staffValue) {
        this.openCompletedDocumentRecipientsModal(recipient, (returnCallBackData: any) => {
          if (
            (!returnCallBackData && !this.form.sendCompletedForm) ||
            returnCallBackData === 'SKIP' ||
            (returnCallBackData && this.form.sendCompletedForm)
          ) {
            this.modalController.dismiss({
              form: {
                name: this.formName,
                id: this.formId,
                populatePreviousSubmission: this.form.populatePreviousSubmission,
                confirmActionPrefill: this.form.confirmActionPrefill,
                staffFilling: this.form.staffFill,
                externalFileExchange: this.form.externalFileExchange,
                alternateSelectedId: this.alternateSelectedId,
                admissionId: this.isEnableMultiAdmissions ? this.admissionId : undefined
              },
              recipient: recipient[0],
              facing: this.formFacing,
              interactionChannel: this.interactionChannel,
              copyOfRecipient: returnCallBackData === 'SKIP' ? '' : returnCallBackData || ''
            });
          } else {
            this.recipientsCompletedDocumentIDs = '';
            this.clearAll('ButtonClick');
          }
        });
      }
    } else {
      const message = this.defaultMsg;
      const alertData = {
        message,
        header: this.defaultMsgTitle
      };
      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          this.sendForm();
        }
      });
    }
  }

  openCompletedDocumentRecipientsModal(recipient: any, callBack: any) {
    if (this.form.sendCompletedForm) {
      this.modalController
        .create({
          component: CompletedDocumentRecipientsComponent,
          componentProps: {
            recipient: recipient[0],
            formFacing: this.formFacing,
            pageName: Constants.copyDocumentPageName.formCenter,
            isSkipButtonShow: true,
            admissionId: this.admissionId
          }
        })
        .then((modal: any) => {
          modal.present();
          modal.onDidDismiss().then(({ data }) => {
            callBack(data);
          });
        });
    } else {
      callBack();
    }
  }

  getStatusWarning(statusData: any): any {
    if (
      !isBlank(statusData.data) &&
      statusData.status.integrationStatus === Constants.enabled &&
      statusData.status.statusMessage === Constants.success
    ) {
      if (statusData.data.defaultFromFilingCenterSubmitjson && statusData.data.progressNoteIntegration) {
        return this.sharedService.showWarningMessages(statusData.data.patientIdentity, statusData.data.staffId, Constants.pageType.recipients);
      }
      if (statusData.data.defaultFromFilingCenterSubmit && this.sharedService.isEnableConfig(Config.showFaxQueueWarning)) {
        return this.sharedService.showWarningMessages(statusData.data.patientIdentity, statusData.data.staffId, Constants.pageType.recipients);
      }
    }
    return this.sharedService.setMsgTitle('', Constants.pageType.recipients);
  }
  sendForm(copyOfRecipient?: any): void {
    let applessMode = '';
    let formSendMode;
    const body: any = {
      form_id: this.formId,
      recipients: this.filteredListData,
      tagRecipients: this.tagRecipient.length >= 1 ? this.tagRecipient.join(',') : ''
    };
    if (this.sharedService.isEnableConfig(Config.enableApplessModel)) {
      applessMode = Constants.applessDevices;
    }
    if (this.sharedService.isEnableConfig(Config.enableApplessModel) && this.interactionChannel !== Constants.mobileapp) {
      formSendMode = Constants.appless;
    } else {
      formSendMode = Constants.mobileapp;
    }

    if (formSendMode && formSendMode === Constants.appless) {
      body.formSendMode = formSendMode;
      body.applessMode = applessMode;
    }
    if (this.formFacing === Constants.practitioner && this.associatedPatient) {
      const assocPatient = [];
      assocPatient[0] = this.selectedAssociatedPatient.userid;
      body.formSubmissionId = 0;
      body.practitioner = 1;
      body.formSendMode = formSendMode;
      body.applessMode = this.sharedService.isEnableConfig(Config.enableApplessModel) ? 1 : 0; // TODO: check interactionchannel based on apless/inapp
      body.practitionerAssociatePatient = assocPatient;
    }
    if (this.formFacing === Constants.practitioner && !this.associatedPatient) {
      body.formSubmissionId = 0;
      body.practitioner = 1;
      body.formSendMode = Constants.mobileapp;
      body.applessMode = 0;
      body.userId = this.userData.userId;
      body.practitionerAssociatePatient = [this.userData.userId];
    }
    let message = '';
    if (copyOfRecipient) {
      body.completedFormsRecipients = copyOfRecipient.join('---');
    }
    this.sharedService.sendFormToRecipients(body).subscribe((response) => {
      message =
        response.status === Constants.statusSuccess
          ? this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_HAS_BEEN_SENT_SUCCESSFULLY', { form: response.form_name })
          : this.common.getTranslateDataWithParam('ERROR_MESSAGES.FORM_SENDING_FAILED', { form: this.formName });
      this.common.showMessage(message);
      if (response.status === Constants.statusSuccess) {
        this.sharedService.formPolling(response);
        this.modalController.dismiss();
        this.common.redirectToPage(PageRoutes.pendingForms);
      }
      this.clearAll();
    });
  }

  alternateSelect(altIndex: number, index: number): void {
    const recipient = this.recipients[index];
    const selectedItem = recipient.alternateContacts[altIndex];
    if (selectedItem.isNotContactable) {
      this.showUserNotContactableMessage();
      return;
    }
    const id = `${selectedItem.userId}--${recipient.userid}`;
    if (this.storeSelectedValue.includes(id)) {
      this.storeSelectedValue.splice(this.storeSelectedValue.indexOf(id), 1);
    } else {
      this.selectedItem = { ...selectedItem, passwordStatus: selectedItem.password, userid: selectedItem.userId };
      this.alternateSelectedId = id;
      this.storeSelectedValue.push(id);
    }
  }
  alternateChecked(altIndex: number, index: number): boolean {
    const recipient = this.recipients[index];
    const id = `${recipient.alternateContacts[altIndex].userId}--${recipient.userid}`;
    return this.storeSelectedValue.includes(id);
  }
  showUserNotContactableMessage() {
    this.common.showToast({
      message: 'ERROR_MESSAGES.USER_CANT_BE_SELECTED',
      color: 'dark'
    });
  }
  viewForm(facing: string, recipient: any, form: any, index?: number, altIndex?: number) {
    let user = recipient;
    if (!isBlank(altIndex)) {
      user = recipient.alternateContacts[altIndex];
    }
    if (user.isNotContactable) {
      this.showUserNotContactableMessage();
      return;
    }
    this.selectedItem = { ...user, passwordStatus: user.password };
    if (this.recipients[index]) {
      this.recipients[index].checked = !this.recipients[index].checked;
    }
    let updateSelection = false;
    if (!isBlank(altIndex)) {
      this.alternateSelectedId = `${recipient.alternateContacts[altIndex].userId}--${recipient.userid}`;
    }
    const selectedSiteIds = this.patientDrivenReqBody && this.patientDrivenReqBody.siteIds ? this.patientDrivenReqBody.siteIds : [];
    if (this.formType !== Constants.formTypes.offline && facing !== Constants.patientValue) {
      if (isBlank(this.selectedAssociatedPatient) && this.associatedPatient) {
        if (this.isEnableMultiAdmissions) {
          this.admissionId = '';
          this.selectAdmission(recipient.userId, selectedSiteIds, (response: any) => {
            if (!isBlank(response.admissionId)) {
              this.openCompletedDocumentRecipientsModal([recipient], (returnCallBackData: any) => {
                if (
                  (!returnCallBackData && !this.form.sendCompletedForm) ||
                  returnCallBackData === 'SKIP' ||
                  (returnCallBackData && this.form.sendCompletedForm)
                ) {
                  
                  this.modalController.dismiss({
                    form: { ...form, admissionId: this.admissionId, alternateSelectedId: this.alternateSelectedId },
                    recipient,
                    facing,
                    tagId: this.tagId,
                    interactionChannel: this.interactionChannel,
                    copyOfRecipient: returnCallBackData === 'SKIP' ? '' : returnCallBackData || '',
                  });
                } else {
                  this.recipientsCompletedDocumentIDs = '';
                  this.clearAll('ButtonClick');
                }
              });
            } else {
              this.clearAll('ButtonClick');
            }
          });
        } else if (facing === Constants.staffValue || facing === Constants.practitioner) {
          this.openCompletedDocumentRecipientsModal([recipient], (returnCallBackData: any) => {
            if (
              (!returnCallBackData && !this.form.sendCompletedForm) ||
              returnCallBackData === 'SKIP' ||
              (returnCallBackData && this.form.sendCompletedForm)
            ) {
              this.modalController.dismiss({
                form: { ...form, alternateSelectedId: this.alternateSelectedId },
                recipient,
                facing,
                tagId: this.tagId,
                interactionChannel: this.interactionChannel,
                copyOfRecipient: returnCallBackData === 'SKIP' ? '' : returnCallBackData || ''
              });
            } else {
              this.recipientsCompletedDocumentIDs = '';
              this.clearAll('ButtonClick');
            }
          });
        } else if (this.drivenBy === Constants.patientValue) {
          this.modalController.dismiss({
            form: { ...form, alternateSelectedId: this.alternateSelectedId },
            recipient,
            facing,
            tagId: this.tagId,
            interactionChannel: this.interactionChannel
          });
        }
      } else if (facing === Constants.practitioner) {
        updateSelection = true;
      }
    } else {
      updateSelection = true;
      const userID = this.getSelectedUserID(this.recipients[index]);
      // set compained id and user id if role is caregiver
      if (isPresent(this.recipients[index].caregiver_userid)) {
        this.recipients[index].userid = this.recipients[index].caregiver_userid;
        this.recipients[index].compainedId = `${this.recipients[index].userId}--${this.recipients[index].caregiver_userid}`;
      }

      if (
        this.isEnableMultiAdmissions &&
        (this.formType === Constants.formTypes.offline || facing === Constants.patientValue) &&
        this.recipients[index].checked
      ) {
        this.admissionId = '';
        this.selectAdmission(recipient.userId, selectedSiteIds, (admission: any) => {
          if (!isBlank(admission.admissionId)) {
            this.recipients[index].admissionId = admission.admissionId;
            this.recipients[index].admissionName = admission.admissionName;
            if (this.formType === Constants.formTypes.offline) {
              this.recipients[index].admissionSiteId = admission.siteId;
              this.recipients[index].admissionSiteName = admission.siteName;
            }
            if (facing === Constants.patientValue) {
              this.sendFormRecipients(facing);
            }
            if (isBlank(admission.admissionId)) {
              this.selectRecipientForm.value.recipientList[index] = false;
              this.selectRecipientForm.patchValue({
                recipientList: this.selectRecipientForm.value.recipientList
              });
              this.storeSelectedValue.splice(this.storeSelectedValue.indexOf(userID), 1);
              this.recipients[index].checked = false;
            }
          } else {
            this.clearAll('ButtonClick');
          }
        });
      }
    }
    if (updateSelection) {
      const userID = this.getSelectedUserID(this.recipients[index]);
      if (this.recipients[index].checked) {
        if (isBlank(altIndex)) this.alternateSelectedId = undefined;
        this.storeSelectedValue.push(userID);
        if (facing === Constants.practitioner) {
          this.storeSelectedData.push(this.recipients[index]);
        }
      } else {
        this.storeSelectedValue.splice(this.storeSelectedValue.indexOf(userID), 1);
        if (facing === Constants.practitioner) {
          this.storeSelectedData = this.storeSelectedData.filter((selectedData) => selectedData.id !== userID);
        }
        if (this.formType === Constants.formTypes.offline && this.isEnableMultiAdmissions) {
          this.recipients[index].admissionId = '';
          this.recipients[index].admissionName = '';
          this.recipients[index].admissionSiteName = '';
          this.recipients[index].admissionSiteId = '';
        }
      }
    }
  }
  searchOperations(action: any): void {
    if (action.do === 'search' || action.do === 'reset') {
      if (action.do === 'reset' && this.formFacing !== Constants.patientValue) {
        this.storeSelectedValue = []; // TODO: Clear selected recipients on reset only
      }
      this.appendTags = true;
      this.clearAll();
      this.recipients = [];
      this.searchKeyword = action.value;
      this.pageCount = Constants.defaultPageCount;
      this.formFacing === Constants.staffValue ||
        this.drivenBy === Constants.patientValue ||
        (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
        ? this.getAssociatedPatients()
        : this.getUsersByTenantRoleWithTagId();
      if (action.do === 'search') {
        switch (this.formFacing) {
          case 'staff': {
            const data = {
              displayName: this.userData.displayName,
              searchKey: this.searchKeyword,
              formName: this.formName,
              id: this.formId
            };
            this.sharedService.trackActivity({
              type: Activity.forms,
              name: Activity.patientSearch,
              des: { data, desConstant: Activity.patientSearchDes }
            });
            break;
          }
          case 'patient':
            this.sharedService.trackActivity({
              type: Activity.forms,
              name: Activity.searchRecipients,
              des: {
                data: {
                  displayName: this.userData.displayName,
                  keyWord: this.searchKeyword
                },
                desConstant: Activity.searchRecipientsDes
              }
            });
            break;
          default:
            break;
        }
      }
    } else if (action.do === 'add') {
      this.showAddPatient();
    }
  }
  async showAddPatient(): Promise<void> {
    const page = 'formCenter';
    const modal = await this.modalController.create({
      component: AddVirtualPatientComponent,
      componentProps: {
        form: this.form,
        tagId: this.tagId,
        formFacing: this.formFacing,
        page
      },
      id: 'add-patient'
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.modalController.dismiss(data, null, 'choose-recipient');
      }
    });
    return await modal.present();
  }

  loadData() {
    //TODO! CHP-3598
    this.loadMore();
  }
  filterSitesData(data: []): void {
    this.pageCount = Constants.defaultPageCount;
    this.recipients = [];
    this.patientDrivenReqBody.siteIds = data;
    this.formFacing === Constants.staffValue ||
      this.drivenBy === Constants.patientValue ||
      (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
      ? this.getAssociatedPatients()
      : this.getUsersByTenantRoleWithTagId();
  }

  loadSitesData(data: []): void {
    this.patientDrivenReqBody.siteIds = data;
  }

  selectAdmission(userId: any, selectedSiteIds, callBack) {
    const params = {
      userId,
      siteIds: selectedSiteIds
    };
    this.sharedService.selectAdmission(params, undefined, AdmissionComponent, (admission: any) => {
      let admissionValue = {
        admissionId: '',
        admissionName: '',
        siteId: '',
        siteName: '',
      };
      if (admission) {
        this.admissionId = admission.admissionId;
        admissionValue = JSON.parse(JSON.stringify(admission));
      } else {
        this.admissionId = '';
      }
      callBack(admissionValue);
    });
  }

  getSelectedUserID(item: any) {
    if (item.role === Constants.roleName.caregiver) {
      return item.caregiver_userid;
    }
    return item.userId ? item.userId : item.userid;
  }
}
