import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { DomSanitizer } from '@angular/platform-browser';
import { environment } from 'src/environments/environment';
import { Config } from 'src/app/constants/config';
import { Activity } from 'src/app/constants/activity';
import { deepParseJSON, isBlank, isPresent, isTrue } from 'src/app/utils/utils';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Socket } from 'src/app/constants/socket';
import { PageRoutes } from 'src/app/constants/page-routes';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { InAppBrowserData } from 'src/app/constants/inappbrowser';
import { ConfigValues } from 'src/assets/config/config';
import { theme } from 'src/theme/theme';
import { Subscription, timer } from 'rxjs';
import { ApplessResponse } from 'src/app/interfaces/login';

@Component({
  selector: 'app-view-forms',
  templateUrl: './view-forms.page.html',
  styleUrls: ['./view-forms.page.scss']
})
export class ViewFormsPage implements OnInit {
  viewData: any = {};
  form: any = {};
  userData: any = {};
  formUrl: any;
  formDetails = false;
  structuredFormData: any;
  formInfo: any;
  constants = Constants;
  activityData = {};
  clientId: string;
  formReq: any;
  formStatus: string;
  editForm: boolean;
  isBlank: any;
  authUrl: any;
  isFormViewFrame = false;
  interactionChannel: string;
  mobileView = false;
  formName: string;
  formId: number;
  formSendMode: string;
  applessworkFlowComplete = false;
  applessFormWorkFlowCompletedMessage: string;
  appLessSession = false;
  consoloFlag = false;
  submittedOn;
  iframeTitle = '';
  serverBaseUrl: string;
  machFormUrl: string;
  selectedForm: any;
  viewFormInterval;
  showHeader = true;
  showFooter = true;
  appLessResponse: ApplessResponse;
  socketSubscriptons: {
    submissionId?: Subscription;
    sendrecpResponse?: Subscription;
    activityTrack?: Subscription;
    saveAsDraft?: Subscription;
    submittedData?: Subscription;
  } = {};
  appLessHomeRedirected = false;
  formEmbedUrl: string;
  dismissConfirmationPopup = false;
  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly navCtrl: NavController,
    public readonly sharedService: SharedService,
    private readonly httpService: HttpService,
    private readonly sanitizer: DomSanitizer,
    private readonly common: CommonService,
    private readonly socketService: SocketService,
    private inAppBrowser: InAppBrowser
  ) {
    this.resetPage();
    this.route.queryParams.subscribe((params) => {
      if (this.router.getCurrentNavigation()?.extras.state) {
        this.viewData = this.router.getCurrentNavigation().extras.state.viewData;
        if (this.router.getCurrentNavigation().extras.state.selectedForm) {
          this.selectedForm = this.router.getCurrentNavigation().extras.state.selectedForm;
        }
        this.formStatus = this.router.getCurrentNavigation().extras.state.formStatus;
        this.appLessResponse = this.router.getCurrentNavigation().extras.state?.appLessResponse;
        this.form = this.viewData.form;
        if (isTrue(this.form?.isCampaignForm)) {
          this.showFooter = false;
          this.showHeader = false;
        }
        this.userData = this.sharedService.userData;
      } else {
        this.navCtrl.back();
      }
    });
    this.constants = Constants;
    this.isBlank = isBlank;
    this.clientId = this.sharedService.socketClientId;
    this.mobileView = this.sharedService.platform.is('capacitor');
    this.consoloFlag =
      this.sharedService.automaticLinkedItems?.formId &&
        this.sharedService.automaticLinkedItems?.formType === Constants.staffFacing
        ? true
        : false;
    this.appLessSession = this.sharedService.userData.appLessSession;
  }

  ngOnInit(): void {
    window.addEventListener('message', this.postMessageCallback.bind(this), false);
    this.serverBaseUrl = environment.apiServer;
    if (this.sharedService?.brandConfig) {
      if (this.sharedService?.brandConfig?.serverUrlCustom) {
        this.serverBaseUrl = this.sharedService.brandConfig.serverUrlCustom;
      }
      if (this.sharedService?.brandConfig?.machFormUrl) {
        this.machFormUrl = this.sharedService?.brandConfig?.machFormUrl;
      }
    }
    if (!this.viewData) {
      this.common.navCtrl.back();
      return;
    }

    this.formId = this.viewData.facing
      ? this.form.id
      : this.formStatus === Constants.formDraftStatus
        ? this.form.formid
        : this.form.formId;
    this.interactionChannel = this.viewData.form.interactionChannel
      ? this.viewData.form.interactionChannel
      : this.viewData.interactionChannel;
    let isAlternateUserVirtual = false;
    if (
      this.viewData.form.alternateSelectedId &&
      this.viewData.form.alternateSelectedId.split('--').length == 2 &&
      isPresent(this.viewData.recipient?.alternateContacts)
    ) {
      const alternateId = this.viewData.form.alternateSelectedId.split('--')[0];
      const alternateUser = this.viewData.recipient?.alternateContacts.filter((x) => x.userId == alternateId);
      isAlternateUserVirtual =
        alternateUser && alternateUser[0] && !alternateUser[0].password && alternateUser[0].isVirtual;
    }
    this.formSendMode =
      (this.sharedService.isEnableConfig(Config.enableApplessModel) && this.interactionChannel === Constants.appless) ||
        (isPresent(this.viewData.recipient) &&
          !this.viewData.recipient.passwordStatus &&
          !this.viewData.recipient.password &&
          this.viewData.recipient.mobVerificationStatus &&
          this.viewData.recipient.emailVerificationStatus &&
          !this.viewData.form.alternateSelectedId) ||
        (this.viewData.form.alternateSelectedId &&
          isPresent(this.viewData.recipient?.alternateContacts) &&
          isAlternateUserVirtual) ||
        (this.viewData.form && this.viewData.form?.appless_mode === Number(Constants.configTrue))
        ? Constants.appless
        : Constants.mobileapp;
    this.formName =
      this.formStatus === Constants.formDraftStatus
        ? this.viewData.form
          ? this.viewData.form.form_name
          : ''
        : this.viewData.facing
          ? this.form.name
          : this.form.formName;
    this.activityData = {
      displayName: this.userData.displayName,
      recipientName: !isBlank(this.viewData.recipient)
        ? this.viewData?.recipient?.displayname
        : this.viewData.form
          ? this.viewData.form?.patientName
          : '',
      recipientId: !isBlank(this.viewData.recipient)
        ? this.viewData.recipient.userid
        : this.viewData.form
          ? this.viewData.form.patient_id
          : '',
      formName: this.formName,
      formId: this.formId
    };
    if (!this.consoloFlag) {
      if (this.viewData.facing !== Constants.practitioner) {
        !isBlank(this.viewData.recipient) ? this.checkAllowEditForm() : this.checkFormSubmitted();
      } else {
        this.viewFormByUrl();
      }
    } else {
      this.checkDraftForm();
    }
    let tagMetaData = {};
    tagMetaData = this.viewData.form ? this.viewData.form.tag_meta : undefined;
    if (
      this.viewData.facing === Constants.practitioner ||
      !isBlank(tagMetaData) ||
      this.viewData.facing === Constants.patientValue
    ) {
      this.socketSubscriptons.submissionId = this.socketService
        .subscribeEvent(Socket.submissionId)
        .subscribe((data: any) => {
          if (this.sharedService.getConfigValue(Config.formSendMode) === Constants.formSendModes.sqs) {
            if (!isBlank(this.viewData)) {
              this.sharedService.trackActivity({
                type: Activity.forms,
                name: this.formSendMode === Constants.appless ? Activity.sendApplessForm : Activity.sendForm,
                des: {
                  data: {
                    displayName: this.userData.displayName,
                    formName: this.viewData.form.name ? this.viewData.form.name : this.viewData.form.form_name,
                    formId: this.formId,
                    recipientId: this.viewData.facing
                      ? this.viewData.recipient.userid
                      : this.viewData.form.practitioner_id
                  },
                  desConstant:
                    this.formSendMode === Constants.appless ? Activity.sendApplessFormDes : Activity.sendFormDes
                }
              });
            }
            if (this.mobileView && this.sharedService.browser) {
              this.sharedService.browser.close();
            }
            if (this.formName) {
              const successMessage = this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_SENT_SUCCESS_SQS', {
                formName: this.formName
              });
              this.common.showMessage(successMessage);
            }
            this.resetPage();
            this.common.redirectToPage(PageRoutes.pendingForms);
          } else {
            const result = localStorage.getItem('sendrecpResult');
            if (isPresent(result)) {
              // Not going to enter to this code block ideally( localstorage of iframe is not accessible). But this data can be obtained form postMessageCallback data in case of form_send_mode !==sqs.
              const recipient = [];
              recipient[0] = this.viewData.facing
                ? this.viewData.recipient.userid
                : this.viewData.form.practitioner_id === 0
                  ? this.viewData.form.recepient_id
                  : this.viewData.form.practitioner_id;
              const body: any = {
                form_id: this.formId,
                recipients: recipient,
                tagRecipients: '',
                formSubmissionId: data[0].data
              };
              if (
                (!isBlank(this.viewData.facing) && this.viewData.facing !== Constants.patientValue) ||
                (!isBlank(this.viewData.form.practitioner_id) && this.viewData.form.practitioner_id !== 0)
              ) {
                const assocPatient = [];
                assocPatient[0] = this.viewData.facing
                  ? this.viewData.associatedPatient.userid
                  : this.viewData.form.patient_associated_id;
                body.practitioner = 1;
                body.practitionerAssociatePatient = assocPatient;
              }
              if (
                (!isBlank(this.viewData.form.practitioner_id) && this.viewData.form.practitioner_id !== 0) ||
                (!isBlank(this.viewData.facing) && this.viewData.facing !== Constants.patientValue) ||
                this.sharedService.isEnableConfig(Config.enableApplessModel)
              ) {
                body.formSendMode = this.formSendMode; // TODO: check based on appless/inapp
                body.applessMode = this.formSendMode === Constants.appless ? 1 : 0; // TODO: check based on appless/inapp
              }
              if (this.formStatus === Constants.formDraftStatus && this.viewData.form.practitioner_id !== 0) {
                body.savedDraftID = this.viewData.form.form_submission_id;
              }
              const formSendMode = body.formSendMode;
              this.sharedService.sendFormToRecipients(body).subscribe((response) => {
                // this api is not called inside ionic 1 submissionId socket subscribe. Need to verify flow.
                const message =
                  response.status === Constants.statusSuccess
                    ? this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_SENT_SUCCESS', {
                      formName: this.viewData.form.name ? this.viewData.form.name : this.viewData.form.form_name
                    })
                    : this.common.getTranslateDataWithParam('ERROR_MESSAGES.FORM_SENT_FAILED', {
                      formName: this.viewData.form.name ? this.viewData.form.name : this.viewData.form.form_name
                    });
                this.common.showMessage(message);
                if (response.status === Constants.statusSuccess) {
                  this.sharedService.formPolling({
                    ...response,
                    formSendMode,
                    applessMode: formSendMode === Constants.appless ? Constants.applessDevices : ''
                  });
                  this.sharedService.trackActivity({
                    type: Activity.forms,
                    name: Activity.sendForm,
                    des: {
                      data: {
                        displayName: this.userData.displayName,
                        formName: this.viewData.form.name ? this.viewData.form.name : this.viewData.form.form_name,
                        formId: this.formId,
                        recipientId: this.viewData.facing
                          ? this.viewData.recipient.userid
                          : this.viewData.form.practitioner_id
                      },
                      desConstant: Activity.sendFormDes
                    }
                  });
                  if (this.mobileView && this.sharedService.browser) {
                    this.sharedService.browser.close();
                  }
                  this.resetPage();
                  this.common.redirectToPage(PageRoutes.pendingForms);
                }
              });
            }
          }
        });
      this.socketSubscriptons.sendrecpResponse = this.socketService
        .subscribeEvent(Socket.sendrecpResponse)
        .subscribe((data: any) => {
          const response = data[0].formData;
          if (
            isPresent(response) &&
            this.sharedService.getConfigValue(Config.formSendMode) === Constants.formSendModes.api
          ) {
            const recipient = [];
            recipient[0] = this.viewData.facing
              ? this.viewData.recipient.userid
              : this.viewData.form.practitioner_id === 0
                ? this.viewData.form.recepient_id
                : this.viewData.form.practitioner_id;
            const body: any = {
              form_id: this.formId,
              recipients: recipient,
              tagRecipients: '',
              formSubmissionId: data[0].data
            };
            if (
              (!isBlank(this.viewData.facing) && this.viewData.facing !== Constants.patientValue) ||
              (!isBlank(this.viewData.form.practitioner_id) && this.viewData.form.practitioner_id !== 0)
            ) {
              const assocPatient = [];
              assocPatient[0] = this.viewData.facing
                ? this.viewData.associatedPatient.userid
                : this.viewData.form.patient_associated_id;
              body.practitioner = 1;
              body.practitionerAssociatePatient = assocPatient;
            }
            if (
              (!isBlank(this.viewData.form.practitioner_id) && this.viewData.form.practitioner_id !== 0) ||
              (!isBlank(this.viewData.facing) && this.viewData.facing !== Constants.patientValue) ||
              this.sharedService.isEnableConfig(Config.enableApplessModel)
            ) {
              body.formSendMode = this.formSendMode;
              body.applessMode = this.formSendMode === Constants.appless ? 1 : 0;
            }
            if (this.formStatus === Constants.formDraftStatus && this.viewData.form.practitioner_id !== 0) {
              body.savedDraftID = this.viewData.form.form_submission_id;
            }
            const selectedRecipientsPolling = [];
            const deepLinkingStates = {
              state: Constants.deepLinkingStates.formsCenter,
              stateParams: {},
              tenantId: this.userData.tenantId,
              tenantName: this.userData.tenantName,
              formSendMode: '',
              sentId: ''
            };
            const formSendMode = body.formSendMode;
            deepLinkingStates.formSendMode = formSendMode;
            if (response.send_to) {
              response.send_to.forEach((value, key) => {
                const res = {
                  userid: value,
                  senderId: this.sharedService.userData.userId,
                  organizationMasterId: this.sharedService.userData.organizationMasterId,
                  formSendMode,
                  applessMode: formSendMode === Constants.appless ? Constants.applessDevices : '',
                  sentId: response.sentId
                };
                selectedRecipientsPolling.push(res);
                let selectedUserNames;
                selectedUserNames += `(${res.userid})`;
              });
              deepLinkingStates.sentId = response.sentId;
            }
            const formRemainderData = {
              patientReminderTime: parseInt(this.sharedService.getConfigValue(Config.patientReminderTime), 10) * 1,
              patientReminderTypes: this.sharedService.getConfigValue(Config.patientReminderTypes),
              messageReplyTimeout: this.sharedService.getConfigValue(Config.messageReplyTimeout),
              sentId: response.sentId,
              senderName: this.userData.displayName,
              tenantId: this.userData.tenantId,
              tenantName: this.userData.tenantName,
              serverBaseUrl: this.serverBaseUrl,
              apiVersion: ConfigValues.config.apiVersion,
              message: this.common.getTranslateData('MESSAGES.UNREAD_FORM_REQUEST'),
              formReminderType: this.sharedService.getConfigValue(Config.patientReminderCheckingType),
              environment: environment.alias,
              formSendMode: formSendMode === Constants.appless ? Constants.appless : '',
              applessMode: formSendMode === Constants.appless ? Constants.applessDevices : ''
            };
            const message =
              response.status === Constants.statusSuccess
                ? this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_SENT_SUCCESS', {
                  formName: this.viewData.form.name ? this.viewData.form.name : this.viewData.form.form_name
                })
                : this.common.getTranslateDataWithParam('ERROR_MESSAGES.FORM_SENT_FAILED', {
                  formName: this.viewData.form.name ? this.viewData.form.name : this.viewData.form.form_name
                });
            this.common.showMessage(message);
            if (response.status === Constants.statusSuccess) {
              const pushMessage = this.common.getTranslateData('MESSAGES.NEW_FORM_NOTIFICATION');
              const notificationData = {
                sourceId: Constants.sourceId.form.toString(),
                sourceCategoryId: Constants.sourceCategoryId.formSentNotification
              };
              this.sharedService.sentPushNotification(
                selectedRecipientsPolling,
                '0',
                pushMessage,
                '',
                deepLinkingStates,
                '',
                notificationData
              );
              this.sharedService
                .reminderForForm({ recipients: selectedRecipientsPolling, otherData: formRemainderData })
                .subscribe();
              this.sharedService.formPolling({
                ...response,
                formSendMode,
                applessMode: formSendMode === Constants.appless ? Constants.applessDevices : ''
              });
              this.sharedService.trackActivity({
                type: Activity.forms,
                name: Activity.sendForm,
                des: {
                  data: {
                    displayName: this.userData.displayName,
                    formName: this.viewData.form.name ? this.viewData.form.name : this.viewData.form.form_name,
                    formId: this.formId,
                    recipientId: this.viewData.facing
                      ? this.viewData.recipient.userid
                      : this.viewData.form.practitioner_id
                  },
                  desConstant: Activity.sendFormDes
                }
              });
              if (this.mobileView && this.sharedService.browser) {
                this.sharedService.browser.close();
              }
              this.resetPage();
              this.common.redirectToPage(PageRoutes.pendingForms);
            }
          }
        });
    }
    this.socketSubscriptons.activityTrack = this.socketService
      .subscribeEvent(Socket.activityTrack)
      .subscribe((data: any) => {
        const formData = data[0].formData;
        if (formData.submissionId && Number(formData.form_id) === Number(this.formId)) {
          const desData = {
            displayName: this.userData.displayName,
            formName: this.viewData.recipient
              ? this.form.name
              : this.form.formStatus === Constants.formStatusDraft
                ? this.form.form_name
                : this.form.formName,
            formId: this.formId,
            recipientName: !isBlank(this.viewData.recipient)
              ? this.viewData.recipient?.displayname
              : this.viewData.form.patientName,
            recipientId: !isBlank(this.viewData.recipient)
              ? this.viewData.recipient.userid
              : this.form.formStatus === Constants.formStatusDraft
                ? this.viewData.form.patient_id
                : this.viewData.form.recipient_id,
            submissionId: formData.submissionId
          };
          this.sharedService.trackActivity({
            type: Activity.forms,
            name: Activity.submitForm,
            des: {
              data: desData,
              desConstant: Activity.submitFormDes
            }
          });
          const successMessage = formData.form_Success_message;
        if (this.sharedService.userData.appLessSession || this.consoloFlag) {
          this.applessworkFlowComplete = true;
          this.applessFormWorkFlowCompletedMessage = successMessage;
        }
        if (!this.sharedService.isAppLessHomeLoggedIn() && !this.consoloFlag) {
          this.common.showMessage(successMessage);
        }
          if (successMessage) {
            if (this.mobileView && this.sharedService.browser) {
              this.sharedService.browser.close();
            }
            this.formRedirect(successMessage);
          }
        }
      });
    this.socketSubscriptons.saveAsDraft = this.socketService
      .subscribeEvent(Socket.saveAsDraft)
      .subscribe((data: any) => {
        if (data) {
          if (this.formStatus !== Constants.formPendingStatus || this.formStatus === Constants.formDraftStatus) {
            this.sharedService.trackActivity({
              type: Activity.forms,
              name: Activity.formSavedAsDraft,
              des: {
                data: this.activityData,
                desConstant: Activity.formSavedAsDraftDes
              }
            });
          }
        if (this.mobileView && this.sharedService.browser) {
          this.sharedService.browser.executeScript({
            code: this.sharedService.inAppBrowserData.setSavAsDraftButtonValue
          });
        }
        if (this.formName && !this.consoloFlag) {
            const successMessage = this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_SAVED_AS_DRAFT', {
              formName: this.formName
            });
            this.common.showMessage(successMessage);
          }
        }
      });
    this.socketSubscriptons.submittedData = this.socketService
      .subscribeEvent(Socket.submittedData)
      .subscribe((data: any) => {
        if (data && this.consoloFlag) {
          const formId = data[0].formData.from_id;
          if (formId) {
            this.common.showMessage(data[0].formData.form_Success_message);
          }
          const action = data[0]?.formData?.action === Constants.edit ? Constants.editForm : Constants.submit;
          const activityData = {
            displayName: this.userData.displayName,
            action: action,
            formName: data[0]?.formData?.formName,
            formId: formId,
            submissionId: data[0].formData?.submissionId
          };
          this.sharedService.trackActivity({
            type: Activity.forms,
            name: action,
            des: {
              data: activityData,
              desConstant: Activity.formSubmissionDes
            }
          });
          if (this.sharedService.automaticLinkedItems && this.sharedService.automaticLinkedItems.formId) {
            this.sharedService.consoloAppExit();
          }
        } else {
          if (
            !isBlank(data) &&
            data[0].formData.submissionId &&
            Number(data[0].formData.form_id) === Number(this.formId)
          ) {
            if (data[0].formData.form_Success_message) {
              if (this.mobileView && this.sharedService.browser) {
                this.sharedService.browser.close();
              }
              if (!this.sharedService.isAppLessHomeLoggedIn()) {
                this.common.showMessage(data[0].formData.form_Success_message);
              }
              this.formRedirect(data[0].formData.form_Success_message);
            }
          }
        }
      });
  }

  formRedirect(message?: string): void {
    if (this.dismissConfirmationPopup) {
      return;
    }
    this.dismissConfirmationPopup = true;
    if (this.sharedService.isAppLessHomeLoggedIn()) {
      this.redirectToAppLessHome(this.form.id, message);
    } else if ((this.formSendMode === Constants.appless && this.sharedService.userData.appLessSession) || this.consoloFlag) {
      this.resetPage();
    } else {
      if (!isBlank(this.form)) {
        if (this.form.formStatus === Constants.formStatusDraft) {
          this.common.redirectToPage(PageRoutes.draftForms);
        } else if (
          this.formStatus === Constants.formPendingStatus &&
          this.form.staff_facing !== Constants.practitionerFacingValue
        ) {
          this.common.redirectToPage(PageRoutes.pendingForms);
        } else {
          this.common.redirectToPage(PageRoutes.completedForms);
        }
        this.resetPage();
      }
    }
  }

  redirectToAppLessHome(formId?: number, message?: string): void {
    if (message) {
      this.appLessHomeRedirected = true;
      this.sharedService.appLessHomeNext.next({ type: 'form', id: formId, message });
      this.common.redirectToPage(`.${PageRoutes.appLess}/home`);
    }
  }

  checkAllowEditForm(): void {
    this.sharedService.isLoading = true;
    const payload = {
      formId: this.formId,
      patientId: this.viewData.recipient.patientId ? this.viewData.recipient.patientId : this.viewData.recipient.userid,
      formName: this.form.name,
      patientName: this.viewData.recipient?.displayname,
      combinedId: this.viewData.recipient?.compainedId,
      patientAssociation: this.viewData.recipient?.patientAssociation,
      admissionId: this.viewData.form.admissionId
    };

    this.sharedService.checkAllowEditForm(payload, this.activityData).then((response: any) => {
      this.sharedService.isLoading = false;
      if (response.status) {
        this.viewFormByUrl();
      }
    });

    this.sharedService.trackActivity({
      type: Activity.forms,
      name: Activity.selectAssociatedPatient,
      des: {
        data: this.activityData,
        desConstant: Activity.selectAssociatedPatientDes
      }
    });
  }
  checkFormSubmitted(): void {
    this.sharedService.isLoading = true;
    const payload = {
      caregiver_userid: this.viewData.form?.caregiver_userid,
      recipient_id:
        this.viewData.form?.formStatus === Constants.formStatusDraft ? this.viewData.form?.recepient_id : this.viewData.form?.recipient_id,
      formId: this.formId,
      sentId: this.viewData.form?.formStatus === Constants.formStatusDraft ? this.viewData.form?.sent_id : this.viewData.form?.sentId,
      form_submission_id: this.viewData.form?.form_submission_id,
      created_on: this.viewData.form?.created_on || '',
      fromId: this.viewData.form?.formStatus === Constants.formStatusDraft ? this.viewData.form?.from_id : this.viewData.form?.fromId
    };
    this.httpService.doPost({ endpoint: APIs.checkFormSubmited, payload, loader: false }).subscribe(
      (checkSubmited: any) => {
        this.sharedService.isLoading = false;
        // TODO: Handle view forms based on submission
        if (isBlank(checkSubmited?.form_submission_id)) {
          // Pending or Draft
          this.viewFormByUrl();
        } else {
          // Completed or Archived
          this.viewFormDetails();
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }

  viewFormByUrl(): void {
    this.formDetails = false;
    this.sharedService.isLoading = true;
    if (!isBlank(this.form.allowEdit)) {
      const authenticationUrl = `${environment.apiServer}${APIs.machformAuthentication}`;
      const authReq: any = {
        fromCallBell: Constants.fromCallBell,
        tenantId: this.userData.tenantId,
        userId: this.userData.userId
      };
      if (this.sharedService.isEnableConfig(Config.enableMultiAdmissions) && this.viewData.form.admissionId) {
        authReq.admissionId = this.viewData.form.admissionId;
      }
      const authReqString = new URLSearchParams(authReq).toString();
      this.authUrl = this.sanitizer.bypassSecurityTrustResourceUrl(unescape(`${authenticationUrl}?${authReqString}`));
    }
    let formEmbedUrl = `${environment.machFormUrl}${APIs.embed}`;
    const brandedUrl = this.sharedService.getConfigValue(Config.brandedApplessFormsUrl);
    if (
      this.sharedService.isEnableConfig(Config.enableSupportWidgetBranding) &&
      isPresent(brandedUrl) &&
      this.sharedService.userData.appLessSession
    ) {
      formEmbedUrl = `${brandedUrl}${APIs.embed}`;
    } else if (this.machFormUrl) {
      formEmbedUrl = `${this.machFormUrl}${APIs.embed}`;
    }

    if (!this.consoloFlag) {
      this.formReq = {
        id: this.formId,
        admissionId: this.viewData.form.admissionId,
        patientId:
          this.viewData.facing === Constants.practitioner && this.viewData.recipient.patientAssociation
            ? this.viewData.associatedPatient.userid
            : Number(
              this.viewData.recipient
                ? this.viewData.recipient.patientId
                  ? this.viewData.recipient.patientId
                  : this.viewData.recipient.userid
                : this.form.formStatus === Constants.formStatusDraft
                  ? this.viewData.form.recepient_id
                  : this.form.caregiver_userid
                    ? this.viewData.form.caregiver_userid
                    : this.viewData.form.recipient_id
            ),
        loginUserId:
          this.viewData.facing === Constants.practitioner && this.viewData.recipient.patientAssociation
            ? this.viewData.associatedPatient.userid
            : Number(
              this.viewData.recipient
                ? this.viewData.recipient.userid
                : this.form.formStatus === Constants.formStatusDraft
                  ? this.viewData.form.recepient_id
                  : this.viewData.form.fromId === Number(this.userData.userId)
                    ? this.viewData.form.fromId
                    : Number(this.userData.userId)
            ),
        tenantId: Number(this.userData.tenantId),
        toId:
          this.form.formStatus === Constants.formStatusDraft || this.viewData.facing || Number(this.form.staff_facing) === Constants.staffFacingValue
            ? Number(this.userData.userId)
            : this.viewData.form.fromId,
        environment: environment.alias,
        formName: this.viewData.recipient
          ? this.form.name
          : this.form.formStatus === Constants.formStatusDraft
            ? encodeURIComponent(this.form.form_name)
            : encodeURIComponent(this.form.formName),
        formId: this.formId,
        apiVersion: encodeURIComponent(this.sharedService.localConfig.apiVersion),
        toName:
          Number(this.viewData.form.staff_facing) === Constants.practitionerFacingValue
            ? this.viewData.form.allowEdit === Constants.allowEditValue
              ? this.userData.displayName
              : this.viewData.form.fromName
            : this.userData.displayName,
        fromName:
          this.formStatus === Constants.formPendingStatus && isBlank(this.form.allowEdit)
            ? this.viewData.form.patientName
            : this.userData.displayName,
        fromMob: true,
        clientId: this.clientId,
        serverBaseUrl: encodeURIComponent(this.serverBaseUrl),
        tagRecipients: '',
        authenticationToken: this.sharedService.userData.authenticationToken,
        enable_sftp_integration: this.sharedService.isEnableConfig(Config.enableSftpIntegrationMachformValue),
        fax_queue_show_warning: this.sharedService.isEnableConfig(Config.showFaxQueueWarning),
        screenOrientationMobile: this.sharedService.getConfigValue(Config.formMobileOrientaion),
        uniqueFormIdentity:
          this.formStatus === Constants.formDraftStatus ? this.viewData?.form?.form_guid : this.sharedService.guidCreate(this.formId), // unique identity
        formSendMode: this.formSendMode, // TODO: check based on appless/inapp
        completedFormsRecipients: Array.isArray(this.viewData.copyOfRecipient)
          ? this.viewData.copyOfRecipient.join('---')
          : ''
      };
      this.formReq.bundleIdentifier = theme.bundleIdentifier;
      let tagMetaData = this.viewData?.form?.tag_meta || undefined;
      let staffFacing;
      if (isPresent(tagMetaData)) {
        tagMetaData = deepParseJSON(tagMetaData);
        staffFacing = tagMetaData?.staffFacing;
      }
      if (
        this.viewData.facing === Constants.practitioner ||
        (this.formStatus === Constants.formDraftStatus && staffFacing === Constants.practitioner)
      ) {
        this.formReq.flowcheck = Constants.practitioner;
        if (this.viewData?.recipient?.userid) {
          this.formReq.recipientset = this.viewData.recipient.userid;
        }
      }
      if (this.viewData.facing === Constants.patientValue) {
        this.formReq.externalFileExchange = this.viewData.form.externalFileExchange;
      }
      if (
        this.viewData.facing === Constants.practitioner ||
        (this.form.formStatus === Constants.formStatusDraft &&
          (JSON.parse(this.viewData.form.tag_meta)?.staffFacing === Constants.practitioner ||
            JSON.parse(this.viewData.form.tag_meta)?.staffFacing === 'false')) ||
        this.viewData.facing === Constants.patientValue
      ) {
        this.formReq.staffFilling = this.form.staffFilling
          ? this.form.staffFilling
          : !isBlank(this.viewData.form.tag_meta) && JSON.parse(this.viewData.form.tag_meta)?.stafffill;
      }
      if (
        this.viewData.facing === Constants.staffValue ||
        this.viewData.facing === Constants.practitioner ||
        this.viewData.facing === Constants.patientValue
      ) {
        // only send staff form and practitioner
        this.formReq.receipientCount = Constants.receipientCount; // no of receipients in mobile - 1
        this.formReq.populatePreviousSubmission = this.viewData.form.populatePreviousSubmission;
      }
      if (
        this.viewData.form.formStatus === Constants.formStatusDraft ||
        this.viewData.facing === Constants.practitioner ||
        this.viewData.facing === Constants.patientValue
      ) {
        this.formReq.noStafffillvalidation = false;
      }
      if (this.formStatus === Constants.formPendingStatus && isBlank(this.form.allowEdit)) {
        // only pending without edit
        this.formReq.staffSubmissionId = this.viewData.form.staff_submission_id; // value undefined in test
        this.formReq.associatedId = this.viewData.form.associated_user_id; // value undefined in test
        this.formReq.recipientEmail = this.viewData.form.fromUsername;
      }
      if (this.form.allowEdit != null && !isBlank(this.form.allowEdit)) {
        // edit pending form
        this.formReq.AllowEdit = true;
        this.formReq.FormEntryID = this.viewData.form.form_submission_id;
      }
      if (isBlank(this.form.allowEdit)) {
        this.formReq.sentId = this.formStatus === Constants.formPendingStatus ? this.viewData.form.sentId : null;
        this.formReq.isWorkingHour = this.sharedService.checkBranchHours(true).isWorkingHours;
        this.formReq.autoSave = Number(this.sharedService.getConfigValue(Config.enableFormAutoSave));
        const checkDraftValue = [0, 1];
        if (this.selectedForm) {
          this.formReq.savaAsDraftStaff = this.selectedForm.enableSaveDraftStaff;
          this.formReq.savaAsDraftPatient = this.selectedForm.enableSaveDraftPatient;
        } else if (checkDraftValue.includes(this.form.enableSaveDraftPatient)) {
          this.formReq.savaAsDraftStaff = this.form.enableSaveDraftStaff;
          this.formReq.savaAsDraftPatient = this.form.enableSaveDraftPatient;
        } else {
          this.formReq.savaAsDraftStaff = Number(this.sharedService.getConfigValue(Config.savaAsDraftStaff));
          this.formReq.savaAsDraftPatient = Number(this.sharedService.getConfigValue(Config.savaAsDraftPatient));
        }
        this.formReq.saveAsDraftMessageInterval = Number(
          this.sharedService.getConfigValue(Config.saveDraftMessageInterval)
        );

        this.formReq.enableCollaborateEdit = Number(this.sharedService.getConfigValue(Config.enableCollaborateEdit));
        this.formReq.version = this.sharedService.localConfig.version;
      }
      if (isBlank(this.form.allowEdit) && this.viewData.facing !== Constants.patientValue) {
        this.formReq.facing_new =
          this.form.formStatus === Constants.formStatusDraft
            ? this.form.facing_new
            : Number(this.form.staff_facing) === Constants.practitionerFacingValue
              ? this.form.staff_facing
              : undefined;
      }
      if (!isBlank(this.form.allowEdit) || this.formStatus !== Constants.formPendingStatus) {
        this.formReq.staffFacing =
          this.form.formStatus === Constants.formStatusDraft
            ? JSON.parse(this.viewData.form.tag_meta).staffFacing === Constants.practitioner
              ? false
              : JSON.parse(this.viewData.form.tag_meta).staffFacing
            : (Number(this.viewData.form.staff_facing) === Constants.practitionerFacingValue &&
              this.viewData.form.allowEdit === Constants.allowEditValue) ||
              this.viewData.form.staff_facing === 0
              ? false
              : this.viewData.facing
                ? this.viewData.facing === Constants.staffValue
                : this.viewData.form.sent_status === 1;
        this.formReq.clearForm = Constants.clearFormFalseValue;
      }

      if (this.viewData.form.formStatus !== Constants.formStatusDraft && isBlank(this.form.allowEdit)) {
        this.formReq.tenantName = encodeURIComponent(this.userData.tenantName); // not in draft and allow edit
      } else {
        this.formReq.tenantName = encodeURIComponent(this.formReq.tenantName); // not in draft and allow edit
      }
      if (
        this.viewData.form.formStatus !== Constants.formStatusDraft &&
        isBlank(this.form.allowEdit) &&
        this.viewData.facing !== Constants.practitioner &&
        this.viewData.facing !== Constants.patientValue
      ) {
        this.formReq.roleId = Number(
          this.formStatus === Constants.formPendingStatus ? this.userData.group : this.userData.registration_type
        ); // user roleId,group value, not in draft,allow edit and practitioner
      }
      if (this.formStatus !== Constants.formPendingStatus) {
        this.formReq.confirmActionPrefill =
          this.form.formStatus === Constants.formStatusDraft
            ? JSON.parse(this.viewData.form.tag_meta).confirmActionPrefill
            : this.viewData.form.confirmActionPrefill;
      }
      if (!isBlank(this.form.allowEdit) || this.viewData.form.formStatus === Constants.formStatusDraft) {
        this.formReq.savedDraftid = this.viewData.form.form_submission_id;
        this.formReq.draftPopulate =
          this.form.formStatus === Constants.formStatusDraft
            ? JSON.parse(this.viewData.form.tag_meta).populatePreviousSubmission
            : undefined;
      }
      if (this.viewData.recipient) {
        this.formReq.recipientset =
          this.viewData.facing === Constants.patientValue
            ? this.form.alternateSelectedId
              ? this.form.alternateSelectedId
              : this.viewData.recipient.userid
            : this.viewData.recipient.userid; // add recipientset
        if (this.viewData.facing === Constants.patientValue && this.viewData.form.alternateSelectedId) {
          this.formReq.loginUserId = this.formReq.patientId = this.viewData.form.alternateSelectedId;
        }
      } else {
        if (!isBlank(this.form.practitioner_id) && !isBlank(this.form.recepient_id)) {
          this.formReq.recipientset = this.form.practitioner_id ? this.form.practitioner_id : this.form.recepient_id;
        }
      }
      if (
        Number(this.viewData?.form?.staff_facing) === Constants.practitionerFacingValue &&
        this.formStatus === Constants.formPendingStatus &&
        this.viewData?.form?.associated_user_id
      ) {
        this.formReq.patientId = this.viewData.form.associated_user_id;
      }
      if (this.viewData?.recipient?.compainedId) {
        this.formReq.loginUserId = this.viewData.recipient.compainedId;
        this.formReq.patientId = this.viewData.recipient.compainedId;
        this.formReq.recipientset = this.viewData.recipient.compainedId;
      }
      this.formReq.formName = encodeURIComponent(this.formReq.formName);
      this.formReq.toName = encodeURIComponent(this.formReq.toName);
      if (
        this.form.staff_submission_id === Constants.emptySubmissionId &&
        this.formStatus !== Constants.formDraftStatus &&
        this.sharedService.loggedUserIsPatient()
      ) {
        this.formReq.patientId = undefined;
      }
      if (this.viewData.facing === Constants.practitioner && !this.viewData.recipient.patientAssociation) {
        this.formReq.receipientCount = Constants.clearFormFalseValue;
        this.formReq.recipientset = this.viewData.practitionerId;
      }
      if (this.viewData.facing === Constants.staffValue && !this.viewData.recipient.patientAssociation) {
        this.formReq.receipientCount = Constants.clearFormFalseValue;
        this.formReq.recipientset = Constants.clearFormFalseValue;
        this.formReq.roleId = Constants.clearFormFalseValue;
      }
      if (
        staffFacing == Constants.falseAsString &&
        this.formReq?.patientId !== this.form?.practitioner_id &&
        this.form?.formStatus === Constants.formStatusDraft
      ) {
        this.formReq.recipientset = `${this.form?.practitioner_id}--${this.formReq?.patientId}`;
      }
    } else {
      /**************************Consolo Form Send *******************************************/
      this.formReq = {
        id: this.formId,
        admissionId: this.viewData.form.admissionId,
        patientId: Number(this.viewData.form.patientId),
        loginUserId: Number(this.viewData.form.patientId),
        tenantId: Number(this.userData.tenantId),
        toId: Number(this.userData.userId),
        environment: environment.alias,
        formId: this.formId,
        apiVersion: encodeURIComponent(this.sharedService.localConfig.apiVersion),
        toName: this.userData.displayName,
        fromName: this.userData.displayName,
        sentId: null,
        staffFacing: true,
        fromMob: true,
        clientId: this.clientId,
        serverBaseUrl: encodeURIComponent(this.serverBaseUrl),
        authenticationToken: this.sharedService.userData.authenticationToken,
        uniqueFormIdentity: this.sharedService.guidCreate(this.formId), // unique identity
        patientMRN: this.viewData.form.patientMRN,
        patientData: this.viewData.form.patientData,
        externalDocumentId: this.viewData.form.externalDocumentId,
        consoloSubmission: 1,
        version: this.sharedService.localConfig.version,
        bundleIdentifier: theme.bundleIdentifier,
        completedFormsRecipients: Array.isArray(this.viewData?.copyOfRecipient)
          ? this.viewData?.copyOfRecipient.join('---')
          : ''
      };
    }
    this.formReq.create_send = !(isPresent(this.formReq.sentId) || isPresent(this.formReq.savedDraftid));
    if (isPresent(this.appLessResponse?.isReminder)) {
      this.formReq.submissionSource = this.appLessResponse?.isReminder;
    }
    this.formEmbedUrl = formEmbedUrl;
    this.formViewFrame(this.formReq.AllowEdit);
    const formReqString = new URLSearchParams(this.formReq).toString();
    this.formUrl = this.sanitizer.bypassSecurityTrustResourceUrl(unescape(`${formEmbedUrl}?${formReqString}`));
    if (isBlank(this.viewData.recipient)) {
      const viewActivitydata = {
        displayName: this.userData.displayName,
        formName: this.formStatus === Constants.formDraftStatus ? this.viewData.form.form_name : this.form.formName,
        formId: this.formId,
        recipientName:
          this.formStatus === Constants.formPendingStatus
            ? this.viewData.form.fromName
            : this.viewData.form.patientName,
        recipientId:
          this.form.formStatus === Constants.formStatusDraft
            ? this.viewData.form.patient_id
            : this.viewData.form.fromId === Number(this.userData.userId)
              ? Number(this.userData.userId)
              : this.viewData.form.fromId
      };
      const viewFormDes = this.sharedService.userData.appLessSession ? Activity.viewFormDesAppless : Activity.viewFormDes;
      this.sharedService.trackActivity({
        type: Activity.forms,
        name: this.formStatus === Constants.formDraftStatus ? Activity.viewDraftForm : Activity.viewForm,
        des: {
          data: viewActivitydata,
          desConstant: this.formStatus === Constants.formDraftStatus ? Activity.viewDraftFormDes : viewFormDes
        }
      });
    }

    if (this.mobileView) {
      if (!(this.formUrl && this.form)) {
        if (this.viewData.facing) {
          this.resetPage();
          this.common.redirectToPage(PageRoutes.formFlow);
        } else {
          this.resetPage();
          this.formRedirect();
        }
        return;
      }
      /* InApp Browser */
      let pageHeading = this.formName || '';
      pageHeading = pageHeading.replace(/"/g, '&quot;');
      pageHeading = pageHeading.replace(/'/g, '&#039;');
      let message = '';
      const completedRecipientsName = this.form?.completedFormRecipients
        ? `<span><b>&nbsp;${this.common.getTranslateData('LABELS.COMPLETED_FORMS_RECIPIENTS')}:&nbsp;</b>${this.form?.completedFormRecipients}</span>`
        : '';
      if (this.viewData.recipient && this.viewData.facing !== Constants.patientValue) {
        const patientName =
          this.viewData?.facing === Constants.practitioner && this.viewData?.associatedPatient && this.viewData?.associatedPatient?.displayname
            ? this.viewData?.associatedPatient?.displayname
            : this.viewData?.recipient?.displayname || '';
        message = `<span><b>&nbsp;${this.common.getTranslateData('GENERAL.ASSOCIATED_PATIENT')}:&nbsp;</b>${patientName}</span>`;
      }
      if (isPresent(this.viewData?.recipient) && this.viewData.recipient?.patientAssociation === false) {
        message = '';
      }
      if (this.form.formStatus === Constants.formStatusDraft) {
        message = `<span><b>&nbsp;${this.common.getTranslateData('LABELS.PATIENT_NAME')}:&nbsp;</b>${this.form?.patientName || ''}</span>`;
      }
      if (isPresent(this.form?.message)) {
        message = `<span><b>&nbsp;${this.common.getTranslateData('LABELS.MESSAGE')}:&nbsp;</b>${this.form?.message}</span>`;
      }
      if (completedRecipientsName) {
        message = `${message}<br><br>${completedRecipientsName}<br><br>`;
      }
      message = message.replace(/"/g, '&quot;');
      message = message.replace(/'/g, '&#039;');
      this.sharedService.inAppBrowserData = new InAppBrowserData();
      const logoutLabel = this.common.getTranslateData('MENU.LOGOUT');
      const continueSessionLabel = this.common.getTranslateData('LABELS.CONTINUE_SESSION');
      const exec = this.sharedService.inAppBrowserData.inAppBrowserExec({
        exitConfirm: true,
        clearButtonHide: this.isFormViewFrame,
        pageHeading,
        isFormViewFrame: this.isFormViewFrame,
        message,
        isAndroid: this.sharedService.platformValue !== 'ios',
        labels: { logoutLabel, continueSessionLabel }
      });
      this.sharedService.browser = this.inAppBrowser.create(
        this.formUrl.changingThisBreaksApplicationSecurity,
        '_blank',
        {
          clearcache: 'yes',
          zoom: 'no',
          location: 'no',
          toolbar: 'no' //TODO: Hide footer with done button in ios device
        }
      );

      this.sharedService.browser.on('loadstart').subscribe((event) => {
        this.sharedService.isLoading = false;
        if (this.viewFormInterval) {
          clearInterval(this.viewFormInterval);
          this.sharedService.browser.executeScript({ code: this.sharedService.inAppBrowserData.setSessionClear });
          this.sharedService.browser.executeScript({ code: this.sharedService.inAppBrowserData.setCloseButton });
        }
      });
      this.sharedService.browser.on('loadstop').subscribe((event) => {
        this.hideLoader();
        this.sharedService.browser.insertCSS({ code: exec.cssData });
        this.sharedService.browser.executeScript({ code: exec.executeScript });
        this.sharedService.browser.executeScript({
          code: this.sharedService.inAppBrowserData.resetSaveAsDraftOnSessionTimeOut
        });
        let retryCounter = Constants.saveAsDraftMaxRetry * Constants.saveAsDraftMaxRetryOffset;
        this.viewFormInterval = setInterval(() => {
          this.sharedService.browser
            .executeScript({ code: this.sharedService.inAppBrowserData.getSessionClear })
            .then((values) => {
              if (values && values.indexOf('true') > -1 && !this.sharedService.sessionTimedOutInInappBrowser) {
                this.sharedService.browser.executeScript({ code: this.sharedService.inAppBrowserData.setSessionClear });
                this.sharedService.resetSessionTimeout();
              }
            });
          this.sharedService.browser
            .executeScript({ code: this.sharedService.inAppBrowserData.getForceLogout })
            .then((values) => {
              if (values && values.indexOf('true') > -1) {
                clearInterval(this.viewFormInterval);
                this.sharedService.logout();
              }
            });
          this.sharedService.browser
            .executeScript({ code: this.sharedService.inAppBrowserData.getCloseButton })
            .then((values) => {
              if (values && values.indexOf('close') > -1) {
                this.sharedService.browser.executeScript({ code: this.sharedService.inAppBrowserData.setCloseButton });
                this.sharedService.browser.close();
                clearInterval(this.viewFormInterval);
              }
            });
          // Check for clearForm flag
          this.sharedService.browser.executeScript({ code: this.sharedService.inAppBrowserData.getClearForm }).then((values) => {
            if (values && values.indexOf('true') > -1) {
              this.sharedService.browser.executeScript({
                code: this.sharedService.inAppBrowserData.resetClearForm
              });
              // Refresh the form by reloading it with clearForm parameter
              this.clearFormInBrowser();
            }
          });
          this.sharedService.browser
            .executeScript({ code: this.sharedService.inAppBrowserData.getSaveAsDraftOnSessionTimeOut })
            .then((values) => {
              if (this.sharedService.sessionTimedOutInInappBrowser) {
                if (values.indexOf('1') > -1 || retryCounter === 0) {
                  this.sharedService.closeInAppOnSessionTimeout(retryCounter > 0);
                  return;
                } else if (values.indexOf('0') > -1 && retryCounter > 0) {
                  retryCounter--;
                  if (retryCounter % Constants.saveAsDraftMaxRetryOffset === 0) {
                    this.sharedService.executeSaveAsDraft(
                      Math.floor(retryCounter / Constants.saveAsDraftMaxRetryOffset)
                    );
                  }
                }
              }
            });
        }, 1000);
      });
      this.sharedService.browser.on('exit').subscribe((event) => {
        if (this.sharedService.sessionTimedOutInInappBrowser) {
          this.sharedService.closeInAppOnSessionTimeout();
          return;
        }
        if (this.sharedService.userData) {
          if (!isBlank(this.viewData) && this.viewData?.facing) {
            this.resetPage();
            this.common.redirectToPage(PageRoutes.formFlow);
          } else {
            this.formRedirect();
          }
        } else {
          this.resetPage();
        }
      });
      this.sharedService.browser.on('loaderror').subscribe((event) => {
        if (this.sharedService.sessionTimedOutInInappBrowser) {
          this.sharedService.closeInAppOnSessionTimeout();
          return;
        }
        this.sharedService.browser.close();
        this.sharedService.browser = undefined;
        this.formRedirect();
      });
    }
  }
  ionViewDidLeave(): void {
    this.resetPage();
    if (this.viewFormInterval) {
      clearInterval(this.viewFormInterval);
    }
  }
  resetPage(): void {
    this.form = undefined;
    this.formUrl = undefined;
    if (!this.consoloFlag) {
      this.formName = undefined;
    }
    this.interactionChannel = undefined;
    this.formSendMode = undefined;
    this.viewData = undefined;
    this.form = undefined;
    this.formId = undefined;
    this.sharedService.closeInAppSession();
    this.sharedService.isLoading = false;
    this.socketSubscriptons?.saveAsDraft?.unsubscribe();
    this.socketSubscriptons?.sendrecpResponse?.unsubscribe();
    this.socketSubscriptons?.submissionId?.unsubscribe();
    this.socketSubscriptons?.activityTrack?.unsubscribe();
    this.socketSubscriptons?.submittedData?.unsubscribe();
    window.removeEventListener('message', null);
    if (this.consoloFlag) {
      this.sharedService.consoloAppExit();
    }
  }
  pageLoad() {
    this.hideLoader();
    if (this.isFormViewFrame) {
      document.getElementById('form-view-frame').style.pointerEvents = 'none';
    }
  }

  hideLoader() {
    timer(1500).subscribe(() => {
      this.sharedService.isLoading = false;
      document.getElementById('iframe-div-top')?.scrollIntoView();
    });
  }
  /**
   * Callback function used for window message event.
   * @param data Callback data
   */

  postMessageCallback(data: any): void {
    setTimeout(() => {
      if (data.data) {
        const postData = data.data;
        if (postData.formIframeHeight && !this.mobileView) {
          document.getElementById('form-view-frame').style.height = postData.formIframeHeight + 'px';
          document.getElementById('iframe-overlay').style.height = +postData.formIframeHeight + 60 + 'px';
        }
        if (postData?.AllowEditFormSubmitted) {
          this.sharedService.closeInAppSession();
          this.formRedirect();
        }
        if (typeof postData === 'string' && postData.split('=').length > 0 && !this.mobileView) {
          const postDataParts = postData.split('=');
          if (postDataParts[0] === Constants.iframeHeight) {
            document.getElementById('form-view-frame').style.height = `${+postDataParts[1] + 20}px`;
            document.getElementById('iframe-overlay').style.height = `${+postDataParts[1] + 60}px`;
          } else if (postDataParts[0] === Constants.scrollTopformsubmit) {
            document.getElementById('form-view-frame').scrollIntoView();
            document.getElementById('iframe-div-top').scrollIntoView();
          } else if (postDataParts[0] == Constants.loaderOnSubmitForm) {
            document.getElementById('loader-message').textContent = Constants.loaderMessages.submittingMessage;
            document.getElementById('iframe-overlay').classList.remove('hide');
            if (this.sharedService.isAppLessHomeLoggedIn()) {
              const formId = this.form?.id;
              setTimeout(() => {
                if (!this.appLessHomeRedirected) this.redirectToAppLessHome(formId);
              }, 2000);
            }
          } else if (postDataParts[0] == Constants.hideFormSubmitLoader) {
            document.getElementById('iframe-overlay').classList.add('hide');
          } else if (postDataParts[0] === Constants.iframeLanguage) {
            document.documentElement.lang = postDataParts[1] || 'en';
          } else if (postDataParts[0] === Constants.iframePageTitle) {
            document.title = decodeURIComponent(postDataParts[1]).replace(/\+/g, ' ') || '';
            this.iframeTitle = decodeURIComponent(postDataParts[1]).replace(/\+/g, ' ') || '';
          }
        }
      }
    });
  }

  formViewFrame(allowEdit: any): void {
    this.isFormViewFrame = !(
      (isPresent(allowEdit) &&
        this.formStatus === Constants.formPendingStatus &&
        this.form.fromId === Number(this.userData.userId)) ||
      this.formStatus === Constants.formDraftStatus ||
      (Number(this.form.recipient_grp_id) === Constants.patientGroupId &&
        this.form.recipient_id === Number(this.userData.userId))
    );

    if (this.viewData.form.fromId !== Number(this.userData.userId) && (isPresent(this.viewData.facing) || this.formSendMode == Constants.appless)) {
      this.isFormViewFrame = false;
    }
    //practitioner form: Enable form fileds
    if (
      !isBlank(this.form?.staff_facing) &&
      Number(this.form.staff_facing) === Constants.practitionerFacingValue &&
      this.formStatus === Constants.formPendingStatus &&
      Number(this.form.recipient_id) === Number(this.userData.userId)
    ) {
      this.isFormViewFrame = false;
    }
  }

  clearFormData(): void {
    // TODO: Check shared service condition.
    const title = this.common.getTranslateData('MESSAGES.ARE_YOU_SURE');
    const message = this.common.getTranslateData('MESSAGES.CLEAR_FORM');
    this.common.showAlert({ message, header: title }).then((confirmation) => {
      if (confirmation) {
        const formEmbedUrl = `${environment.machFormUrl}${APIs.embed}`;
        this.formReq.clearForm = Constants.clearFormTrueValue;
        const formReqString = new URLSearchParams(this.formReq).toString();
        this.formUrl = this.sanitizer.bypassSecurityTrustResourceUrl(unescape(`${formEmbedUrl}?${formReqString}`));
      }
    });
  }
  viewFormDetails(): void {
    // Completed and Archived Forms Details View
    this.formDetails = true;
    this.sharedService.trackActivity({
      type: Activity.forms,
      name: Activity.viewFormReport,
      des: {
        data: {
          viewedUser: this.form.fromName,
          formName: this.form.formName,
          formId: this.formId,
          filledBy: this.form.patientName,
          id: this.form.recipient_id
        },
        desConstant: Activity.viewFormReportDes
      }
    });
    this.sharedService.isLoading = true;
    const payload = {
      formId: this.formId,
      submissionId: this.form.form_submission_id
    };
    this.httpService
      .doPost({
        endpoint: APIs.structuredFormData,
        payload: {},
        extraParams: payload
      })
      .subscribe(
        (response) => {
          this.sharedService.isLoading = false;
          if (response) {
            this.formInfo = { ...response.entries, patientAssociation: response?.patientAssociation };
            this.submittedOn =
              this.formStatus === Constants.formDraftStatus ? this.form.updatedtimestamp : this.form.sent;
            this.structuredFormData = response.structuredFormResult;
            let parser = new DOMParser();
            this.structuredFormData = this.structuredFormData
              ? this.structuredFormData.filter((item: any) => item !== null)
              : [];

            if (this.structuredFormData.length) {
              this.structuredFormData.forEach((element: any, index: number) => {
                const elementType = [Constants.formElementTypes.media, Constants.formElementTypes.section, Constants.formElementTypes.signature, Constants.formElementTypes.file];
                if (!elementType.includes(element.element_type)) {
                  const doc = parser.parseFromString(element.value, 'text/html');
                  const bodyElement = doc.body;
                  element.value = bodyElement.innerText;
                }
              });
            }
          }
        },
        () => {
          this.sharedService.isLoading = false;
        }
      );
  }
  checkDraftForm(): void {
    /* Check draft if the form exists in drafts*/
    const payload = {
      formId: this.formId,
      patientId: this.viewData.form.patientId,
      formName: this.viewData.form.formName,
      patientName: this.sharedService.automaticLinkedItems?.patient?.displayName,
      admissionId: this.sharedService.automaticLinkedItems?.admissionId,
    };
    this.sharedService.isLoading = true;
    this.sharedService.checkDraftForm(payload, this.activityData).then((res: any) => {
      this.sharedService.isLoading = false;
      if (res.status) {
        this.viewFormByUrl();
      }
    });
  }

  clearFormInBrowser(): void {
    if (this.mobileView && this.sharedService?.browser) {
      // Create a new URL with clearForm=1 parameter
      // The uniqueFormIdentity is dynamically generated to make it load as a new form after clearing the form
      const formReqCopy = {
        ...this.formReq,
        uniqueFormIdentity: this.sharedService.guidCreate(this.formId),
        clearForm: Constants.clearFormTrueValue,
        mf_page: 1
      };
      const formReqString = new URLSearchParams(formReqCopy).toString();
      const clearFormUrl = decodeURIComponent(`${this.formEmbedUrl}?${formReqString}`);
      // Navigate to the new URL in the InAppBrowser
      this.sharedService.browser?.executeScript({
        code: `window.location.href = '${clearFormUrl}';`
      });
    }
  }
}
