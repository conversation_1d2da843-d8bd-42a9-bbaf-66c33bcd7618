import { Config } from 'src/app/constants/config';
import { Component, OnDestroy } from '@angular/core';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { isBlank, formatDate, arraysMatch, deepCopyJSON } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Buttons } from 'src/app/constants/buttons';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Permissions } from 'src/app/constants/permissions';
import { Activity } from 'src/app/constants/activity';
import { Signature } from 'src/app/constants/signature';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Socket } from 'src/app/constants/socket';
import { environment } from 'src/environments/environment';
import { ConfigValues } from 'src/assets/config/config';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-document-types',
  templateUrl: './document-types.page.html',
  styleUrls: ['./document-types.page.scss']
})
export class DocumentTypesPage implements OnDestroy{
  documents = [];
  subHead: string;
  documentType: string;
  documentStatus: string;
  isPageLoaded = false;
  searchKey: string;
  button: any = {};
  buttonsList: typeof Buttons;
  limit = Constants.defaultLimit;
  offset = Constants.defaultOffset;
  isList = true;
  extraData: any = {};
  buttons: any = [];
  searchText: string;
  searchDate: string;
  showLoadMore: boolean;
  selectedSiteIds: any = [];
  isEnableMultiSite = false;
  selectedDateOptions = Constants.filterSelectedOptions.lastMonth;
  dateRange = null;
  siteLabel = 'LABELS.SITE';
  private subscription: Subscription;

  constructor(
    private readonly graphqlService: GraphqlService,
    private readonly common: CommonService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    public readonly sharedService: SharedService,
    private permissionService: PermissionService,
    private readonly socketService: SocketService,
    private readonly persistentService: PersistentService
  ) {
    this.buttonsList = Buttons;
    this.buttonsList.archive.permission = Permissions.allowArchiveDocuments;
    this.buttonsList.restore.permission = Permissions.allowArchiveDocuments;
    this.buttonsList.cancel.permission = Permissions.cancelSignatureRequest;
    this.route.paramMap.subscribe((paramMap) => {
      this.documentType = paramMap.get('type');
      this.setButtonAndStatus();
    });
    this.isEnableMultiSite = this.sharedService.isEnableConfig(Config.enableMultiSite);
    this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITES');
    if (Number(this.sharedService.userData?.group) === Constants.patientGroupId) {
      // TODO! CHP-3597
      this.button = {
        iconCustom: false,
        type: 'new-chat',
        buttonType: 'ion-button',
        buttonIcon: 'chatbox-ellipses',
        colorTheme: 'de-york'
      };
    } else {
      this.button = {
        buttonType: 'ion-button',
        iconCustom: true,
        buttonIcon: 'assets/icon/button/signature.svg',
        customClass: 'document-center-btn',
        colorTheme: 'de-york',
        ...this.common.setActionButton('BUTTONS.REQUEST_SIGNATURE', 'request-signature.png')
      };
    }
    this.extraData = {
      image: 'icon/documents/document-grid.png',
      type: 'documents',
      trackBy: 'id',
      showNoDataMessage: false,
      search: {}
    };
    this.getStoredData();
  }

  ionViewWillEnter() {
    this.limit = Constants.defaultLimit;
    this.offset = Constants.defaultOffset;
    this.getDocuments(true);
    this.subscription = this.sharedService.documentPollingEvent.subscribe((data: any) => {
      if(data && data[0]['notifyOnSubmit']){
        this.getDocuments(false, data);
      }
    });
  }
  getStoredData(): void {
    this.dateRange = this.persistentService.getPersistentData(Constants.storageKeys.dateRangeFilterMyDocuments) || { from: '', to: '' };
    this.selectedDateOptions = !isBlank(this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyDocuments))
      ? this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyDocuments)
      : Constants.filterSelectedOptions.lastMonth;
  }

  getDocuments(refetch = false, event?: any): any {
    this.extraData.showNoDataMessage = false;
    let showFilterAppliedMessage = false;
    const obtainSignPollingData = event ? event[0] : null;
  let params = {};
    if (!this.isPageLoaded) {
      this.isPageLoaded = true;
      this.getStoredData();
      const searchStored = this.persistentService.getPersistentData(this.searchKey);
      const storedSearchText = searchStored && searchStored.searchText ? searchStored.searchText.trim() : '';
      this.searchText = storedSearchText;
      if (!isBlank(storedSearchText) || this.selectedDateOptions !== Constants.filterSelectedOptions.lastMonth) {
        showFilterAppliedMessage = true;
      }
    }

    this.common.notifySearchFilterApplied(showFilterAppliedMessage);
    this.persistentService.setPersistentData(this.searchKey, { searchText: this.searchText });
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterMyDocuments, this.dateRange);
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyDocuments, this.selectedDateOptions);
  
    if(obtainSignPollingData && obtainSignPollingData.documentId){
      if(obtainSignPollingData.notifyOnSubmit){
        params = {
          siteId: this.selectedSiteIds.length > 0 ? this.selectedSiteIds.toString() : '0',
          tenantId: this.sharedService.userData?.tenantId,
          crossTenantId: this.sharedService.userData?.crossTenantId,
          signatureRequestFilterInput: {
            signatureStatus: this.documentStatus,
            searchText: this.searchText ? this.searchText : ''
          },
          documentId: obtainSignPollingData.documentId,
          notifyOnSubmit:true
        }

      }
    }
    else{
      params = {
        siteId: this.selectedSiteIds.length > 0 ? this.selectedSiteIds.toString() : '0',
        tenantId: this.sharedService.userData?.tenantId,
        crossTenantId: this.sharedService.userData?.crossTenantId,
        signatureRequestFilterInput: {
          signatureStatus: this.documentStatus,
          searchText: this.searchText ? this.searchText : ''
        },
        ...this.sharedService.getFilterDateRange(this.selectedDateOptions, this.dateRange),
        paginationInput: {
          fetchCount: true,
          limit: this.limit,
          offset: this.offset,
          orderData: '',
          orderby: Constants.sortOrderAsc
        }
      };
    }
    if (!this.documents.length) {
      this.sharedService.isLoading = true;
    }
    this.graphqlService.getDocuments(params)?.subscribe(
      ({ data }) => {
        if (event) {
          if(event.target){
            event.target.complete();
          }
        } else {
          this.sharedService.isLoading = false;
        }
        if (refetch) {
          this.documents = [];
        }
        this.createList(data,obtainSignPollingData?.notifyOnSubmit);
      },
      () => {
        this.showLoadMore = false;
        if (event) {
          event.target.complete();
        }
        if (this.documents.length === 0) {
          this.extraData.showNoDataMessage = true;
        }
      }
    );
  }

  closeSearch() {
    this.persistentService.removePersistentData(this.searchKey);
  }

  createList(data: any,isNotifyOnSubmit = false): void {
    if (!isBlank(data.mySignatureRequest.signatureRequest)) {
      const docs = data.mySignatureRequest.signatureRequest.map((r) => ({
        ...r,
        displayLabel: r.displayText.text,
        swipeButtons: this.showDocumentSwipeButton(r)
      }));
      if (isNotifyOnSubmit) {
        this.documents = [...docs, ...this.documents];
      } else {
        this.documents = this.documents.concat(docs);
      }
      this.showLoadMore = docs.length >= Constants.defaultLimit;
      if (this.sharedService.automaticLinkedItems) {
        const document = this.documents.filter((x) => x?.id === this.sharedService.automaticLinkedItems?.documentId);
        if (document[0]) {
          this.viewDocument(document[0]);
        } else {
          //TODO A page for something went wrong
          const message = this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
          this.common.showMessage(message);
        }
      }
    } else {
      this.showLoadMore = false;
      this.extraData.search.date = this.searchDate ? formatDate(this.searchDate) : this.searchDate;
      this.extraData.search.text = this.searchText;
      this.extraData.showNoDataMessage = true;
    }
  }

  pullRefresh(event: any): void {
    this.offset = Constants.defaultOffset;
    this.getDocuments(true, event);
  }

  doAction(): void {
    this.common.redirectToPage('/document-center/request-signature');
  }

  selectAction(e: any): void {
    switch (e.action) {
      case 'click':
        this.viewDocument(e.item);
        break;
      case 'resend':
        this.doResend(Number(e.item.id), e.index);
        break;
      case 'cancel':
        this.doCancel(Number(e.item.id), e.index);
        break;
      case 'archive':
        this.doArchive(Number(e.item.id), e.index, 'ARCHIVE');
        break;
      case 'restore':
        this.doArchive(Number(e.item.id), e.index, 'RESTORE');
        break;
      default:
        break;
    }
  }

  searchDocuments(searchData: any): void {
    this.documents = [];
    this.searchText = searchData.text;
    this.searchDate = searchData.date;
    this.limit = Constants.defaultLimit;
    this.offset = Constants.defaultOffset;
    this.sharedService.trackActivity({
      type: Activity.signatureRequest,
      name: Activity.searchDocuments,
      des: {
        data: {
          displayName: this.sharedService.userData.displayName,
          documentType: this.documentType,
          keyWord: this.searchText,
          selectedDate: formatDate(this.searchDate)
        },
        desConstant: Activity.searchDocumentDes
      }
    });
    this.getDocuments();
  }

  viewDocument(item: any): void {
    this.router.navigate([`document-center/view-document/${item.id}/${item.senderTenant}`], {
      state: { documentInfo: item },
      skipLocationChange: true
    });
  }

  loadDocuments(): void {
    this.documents = [];
    this.searchText = undefined;
    this.searchDate = undefined;
    this.limit = Constants.defaultLimit;
    this.offset = Constants.defaultOffset;
    this.getDocuments();
  }

  doResend(id: number, index: number): void {
    const alertData = {
      message: 'MESSAGES.GOING_TO_RESEND_THIS_DOC',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        this.graphqlService.resendSignatureDocument(id).subscribe(
          ({ data }) => {
            this.documents[index].createdOn = data.resendSignatureDocument.createdOn;
            this.obtainSignResendPollingToServerData(data, index);
          },
          (error) => {
            this.sharedService.errorHandler(error);
          }
        );
      }
    });
  }

  obtainSignResendPollingToServerData(data: any, index: number) {
    const document = this.documents[index];
    if (document) {
      const users = [];
      if (document.signatureByUsers) {
        users.push({
          userId: document.signatureByUsers.userId,
          displayName: document.signatureByUsers.displayName
        });
        users.push({
          userId: document.ownerId,
          displayName: document.owner
        });
      }
      if (document.associateSignatureByUsers) {
        users.push({
          userId: document.associateSignatureByUsers.userId,
          displayName: document.associateSignatureByUsers.displayName
        });
      }
      const obtainSignResendPollingData: any = {
        notifyOnSubmitSignatureUsers: document.notifyOnSubmitSignatureUsers?.split(','),
        notifyOnSubmitSignatureRoles: document.notifyOnSubmitSignatureRoles?.split(','),
        environment: environment.alias,
        serverBaseUrl: environment.apiServer,
        apiVersion: ConfigValues.config.apiVersion,
        documentId: data.resendSignatureDocument.id,
        createdOn: data.resendSignatureDocument.createdOn,
        ownerId: parseInt(document.ownerId, 10),
        notifyUsers: users,
        workflow: Constants.nonAppless
      };
      this.socketService.emitEvent(Socket.obtainSignResendPollingToServer, obtainSignResendPollingData);
    }
  }

  doCancel(id: number, index: number): void {
    const docCopy = deepCopyJSON(this.documents[index]);
    this.sharedService.cancelDocumentOrForm('DOCUMENTS', docCopy?.displayLabel, 'PENDING').then((cancelReason) => {
      if (cancelReason) {
        this.processCancellation(id, index, cancelReason);
      }
    });
  }

  // Helper method to process the actual cancellation with reason
  private processCancellation(id: number, index: number, reason: string): void {
    this.sharedService.isLoading = true;
    this.graphqlService.cancelSignatureDocumentTenant(id, reason).subscribe({
      next: ({ data }) => {
        this.sharedService.isLoading = false;
        if (isBlank(data?.cancelSignatureDocument)) {
          const message = data.errors[0]?.message ?? this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
          this.common.showToast({ message, color: 'danger' });
        } else {
          this.common.showToast({ message: this.common.getTranslateData('SUCCESS_MESSAGES.CANCEL_DOCUMENT_SUCCESS') });
          const document = deepCopyJSON(this.documents[index]);
          this.sharedService.trackActivity({
            type: Activity.signatureRequest,
            name: Activity.signatureCancel,
            linkageId: id,
            des: {
              data: {
                displayName: this.sharedService.userData.displayName,
                userId: this.sharedService.userData.userId,
                document: document.displayLabel,
                status: document.signatureStatus
              },
              desConstant: Activity.signatureCancelDes
            }
          });
          this.removeDocumentFromList(index);
        }
      },
      error: (error) => {
        this.common.showToast({ message: this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'), color: 'danger' });
        this.sharedService.errorHandler(error);
      }
    });
  }
  removeDocumentFromList(index: number) {
    this.documents.splice(index, 1);
    this.sharedService.isLoading = false;
    if (isBlank(this.documents)) {
      this.extraData.showNoDataMessage = true;
    }
  }
  doArchive(id: number, index: number, action: string): void {
    const alertMessage = action === 'ARCHIVE' ? 'MESSAGES.GOING_TO_ARCHIVE_THIS_DOC' : 'MESSAGES.GOING_TO_RESTORE_THIS_DOC';
    const alertData = {
      message: alertMessage,
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        const crossTenantId = Number(this.sharedService.userData.crossTenantId);
        this.sharedService.isLoading = true;
        this.graphqlService.archiveSignatureDocumentTenant(id, crossTenantId, action).subscribe(
          () => {
            this.removeDocumentFromList(index);
          },
          (error) => {
            this.sharedService.errorHandler(error);
          }
        );
      }
    });
  }

  showDocumentSwipeButton(item: any): any {
    const newButtons = JSON.parse(JSON.stringify(this.buttons));

    this.buttons.forEach((button: any, index: number) => {
      switch (button.action) {
        case 'resend':
          newButtons[index].show =
            this.permissionService.userHasPermission(Permissions.allowArchiveDocuments) &&
            String(item.ownerId) === String(this.sharedService.userData.userId) &&
            item.signatureStatus === Signature.signatureStatus.signaturePendingStatus &&
            (Number(item.associateSignatureByUsers.userId) !== 0 || Number(item.signatureByUsers.userId) !== 0);
          break;
        case 'restore':
          newButtons[index].show = !item.accountLevelArchived;
          break;
        case 'archive':
          newButtons[index].show = this.permissionService.userHasPermission(Permissions.allowArchiveDocuments);
          break;
        default:
          // cancel
          newButtons[index].show =
            !this.sharedService.loggedUserIsPatient() && this.permissionService.userHasPermission(Permissions.cancelSignatureRequest);
          break;
      }
    });
    return newButtons;
  }

  loadData(event: any) {
    this.offset += this.limit;
    this.getDocuments(false, event);
  }

  filterSitesData(data: []): void {
    if (!arraysMatch(this.selectedSiteIds, data)) {
      this.selectedSiteIds = deepCopyJSON(data);
      this.offset = Constants.defaultOffset;
      this.limit = Constants.defaultLimit;
      this.documents = [];
      this.getDocuments(true);
    }
  }

  initialSiteData(data: any): void {
    this.selectedSiteIds = data;
  }

  setButtonAndStatus(): void {
    switch (this.documentType) {
      case 'pending-documents':
        this.subHead = 'LABELS.PENDING_DOCUMENTS';
        this.buttons = [this.buttonsList.resend, this.buttonsList.cancel];
        this.documentStatus = Signature.signatureStatus.signaturePendingStatus;
        this.searchKey = Constants.storageKeys.searchMyDocPending;
        break;
      case 'completed-documents':
        this.subHead = 'LABELS.COMPLETED_DOCUMENTS';
        this.buttons = [this.buttonsList.archive];
        this.documentStatus = Signature.signatureStatus.signatureSignedStatus;
        this.searchKey = Constants.storageKeys.searchMyDocCompleted;
        break;
      case 'archived-documents':
        this.subHead = 'LABELS.ARCHIVED_DOCUMENTS';
        this.buttons = [this.buttonsList.restore];
        this.documentStatus = Signature.signatureStatus.signatureArchiveStatus;
        this.searchKey = Constants.storageKeys.searchMyDocArchived;
        break;
      default:
        this.common.redirectToPage('not-found');
        break;
    }
  }
  loadFilterData(value) {
    this.selectedDateOptions = value.text !== '' ? value.text : 0;
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyDocuments, this.selectedDateOptions.toString());
    if (value.text === Constants.filterSelectedOptions.custom) {
      this.dateRange.from = value.dates.from;
      this.dateRange.to = value.dates.to;
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterMyDocuments, value.dates);
    }
    this.getDocuments(true);
  }
  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
