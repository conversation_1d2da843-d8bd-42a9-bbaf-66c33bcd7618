import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { AdmissionDetailsPageRoutingModule } from './admission-details-routing.module';

import { AdmissionDetailsPage } from './admission-details.page';
import { AddressListComponent } from '../address-list/address-list.component';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, AdmissionDetailsPageRoutingModule, SharedModule, AddressListComponent],
  declarations: [AdmissionDetailsPage]
})
export class AdmissionDetailsPageModule {}
