<app-header [headerTitle]="headerValue"></app-header>
<div class="admission-details-tabs">
  <ion-segment mode="ios" class="tab-nav-contents" [value]="selectedValue | translate" (ionChange)="changeSegment($event.detail.value)" id="segment">
    <ion-segment-button value="admission-details" id="admission-details">
      <ion-label class="segment-label-font">{{ 'ADMISSION.LABELS.ADMISSION_DETAILS' | translate }}</ion-label>
    </ion-segment-button>

    <ion-segment-button value="address-details" id="address-details">
      <ion-label class="segment-label-font">{{ 'TITLES.ADDRESS_DETAILS' | translate }}</ion-label>
    </ion-segment-button>
  </ion-segment>
</div>
<ion-content class="stacked-input-padding">
  @if (selectedIndex === 1) {
    <ion-row>
      <ion-col>
        <ion-input
          type="text"
          id="lineOfService"
          label="{{ 'ADMISSION.LABELS.LINE_OF_BUSINESS' | translate }}"
          label-placement="stacked"
          fill="outline"
          [value]="admissionDetails?.lineOfService"
          name="lineOfService"
          autocapitalize="on"
          [disabled]="true"
          class="readonly-input"
          mode="md"
        ></ion-input>
      </ion-col>
    </ion-row>

    <ion-row *ngIf="!sharedService.showAdmissionTabInProfile">
      <ion-col>
        <ion-input
          type="text"
          id="externalAdmissionId"
          label="{{ 'ADMISSION.LABELS.ADMISSION_ID' | translate }}"
          label-placement="stacked"
          fill="outline"
          [value]="admissionDetails?.externalAdmissionId"
          name="externalAdmissionId"
          autocapitalize="on"
          [disabled]="true"
          class="readonly-input"
          mode="md"
        ></ion-input>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col>
        <ion-input
          type="text"
          id="mrn"
          label="{{ 'ADMISSION.LABELS.ADMISSION_MRN' | translate }}"
          label-placement="stacked"
          fill="outline"
          [value]="admissionDetails?.mrn"
          name="mrn"
          autocapitalize="on"
          [disabled]="true"
          class="readonly-input"
          mode="md"
        ></ion-input>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col>
        <ion-input
          type="text"
          id="status"
          label="{{ 'ADMISSION.LABELS.ADMISSION_STATUS' | translate }}"
          label-placement="stacked"
          fill="outline"
          [value]="admissionDetails?.status"
          name="status"
          autocapitalize="on"
          [disabled]="true"
          class="readonly-input"
          mode="md"
        ></ion-input>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col>
        <ion-input
          type="text"
          id="startDate"
          label="{{ 'LABELS.START_DATE' | translate }}"
          label-placement="stacked"
          fill="outline"
          [value]="admissionDetails?.startDate | date: constants.dateFormat.mmddyyyy || '-'"
          name="startDate"
          autocapitalize="on"
          [disabled]="true"
          class="readonly-input"
          mode="md"
        ></ion-input>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col>
        <ion-input
          type="text"
          id="endDate"
          label="{{ 'LABELS.END_DATE' | translate }}"
          label-placement="stacked"
          fill="outline"
          [value]="admissionDetails?.endDate | date: constants.dateFormat.mmddyyyy || '-'"
          name="endDate"
          autocapitalize="on"
          [disabled]="true"
          class="readonly-input"
          mode="md"
        ></ion-input>
      </ion-col>
    </ion-row>

    <ion-row *ngIf="!sharedService.showAdmissionTabInProfile">
      <ion-col>
        <ion-input
          type="text"
          id="siteName"
          label="{{ 'LABELS.ADMISSION_SITE' | translate }}"
          label-placement="stacked"
          fill="outline"
          [value]="admissionDetails?.siteName"
          name="siteName"
          autocapitalize="on"
          [disabled]="true"
          class="readonly-input"
          mode="md"
        ></ion-input>
      </ion-col>
    </ion-row>
  } @else {
    <app-address-list
      [patientID]="routerData?.patientID"
      [patientActivityDetails]="routerData?.patientActivityDetails"
      [admissionID]="routerData?.admission.admissionId"
    >
    </app-address-list>
  }
</ion-content>
<app-footer></app-footer>
