<ion-row>
  <ion-col size="12">
    <app-search-bar
      (seachAction)="searchAdmission($event)"
      (closeSearch)="searchAdmission({ text: '' })"
      (closeDateFilter)="loadFilterDate($event)"
      [selectedDateOptions]="selectedDateOptions"
      [isDateFilter]="true"
      [dateRange]="dateRange"
      [monthTypes]="monthTypes"
    ></app-search-bar>
  </ion-col>
  <ion-col size="12">
    <ion-list>
      @for (item of admissionList; track item.admissionId; let i = $index; let l = $last) {
        <ion-item
          lines="none"
          color="light"
          class="ion-no-padding patient-item ion-margin-bottom"
          id="item-{{ i }}"
          [class.status-active]="item?.citusStatus === admissionRecordStatus.ACTIVE"
          [class.status-inactive]="item?.citusStatus === admissionRecordStatus.INACTIVE"
          detail="true"
          (click)="viewAdmissionDetails(item)"
        >
          <ion-label>
            <p>{{ 'ADMISSION.LABELS.LINE_OF_BUSINESS' | translate }}: {{ item?.lineOfService || '-' }}</p>
            <p>{{ 'ADMISSION.LABELS.ADMISSION_STATUS' | translate }}: {{ item?.status || '-' }}</p>
            <p>{{ 'LABELS.START_DATE' | translate }}: {{ item?.startDate | date: 'MM/dd/YYYY' || '-' }}</p>
          </ion-label>
        </ion-item>
      } @empty {
        <ion-row>
          <ion-col size="12" class="ion-text-center">
            {{ noDataText | translate }}
          </ion-col>
        </ion-row>
      }
    </ion-list>
    @if (showLoadMore && admissionList.length > 0) {
      <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)" id="infinit-scroll">
        <ion-infinite-scroll-content loadingSpinner="dots"> </ion-infinite-scroll-content>
      </ion-infinite-scroll>
    }
  </ion-col>
</ion-row>
