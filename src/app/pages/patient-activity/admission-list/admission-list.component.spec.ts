import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { AdmissionListComponent } from './admission-list.component';

describe('AdmissionListComponent', () => {
  let component: AdmissionListComponent;
  let fixture: ComponentFixture<AdmissionListComponent>;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [],
      imports: [IonicModule.forRoot(), AdmissionListComponent, HttpClientTestingModule, RouterModule.forRoot([]), TranslateModule.forRoot()],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AdmissionListComponent);

    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call loadData', () => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    component.loadData(spy);
    component.admissionParams.currentPage += 1;
    fixture.detectChanges();
    expect(component.loadData).toBeDefined();
  });

  describe('loadFilterDate', () => {
    it('should load filter data with empty text', () => {
      const value = { text: '', dates: { from: '2022-01-01', to: '2022-01-31' } };
      spyOn(component, 'getAdmissionList').and.callThrough();
      component.loadFilterDate(value);
      expect(component.selectedDateOptions).toEqual(0);
      expect(component.getAdmissionList).toHaveBeenCalled();
    });

    it('should load filter data with custom date range', () => {
      const value = { text: Constants.filterSelectedOptions.custom, dates: { from: '2022-01-01', to: '2022-01-31' } };
      spyOn(localStorage, 'setItem');
      spyOn(component, 'getAdmissionList').and.callThrough();
      component.loadFilterDate(value);
      expect(component.selectedDateOptions).toEqual(Constants.filterSelectedOptions.custom);
      expect(component.getAdmissionList).toHaveBeenCalled();
    });
  });
});
