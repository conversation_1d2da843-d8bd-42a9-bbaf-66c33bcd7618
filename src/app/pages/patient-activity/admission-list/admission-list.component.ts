import { SearchBarModule } from 'src/app/components/search-bar/search-bar.module';
import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { APIs } from 'src/app/constants/apis';
import { PageRoutes } from 'src/app/constants/page-routes';
import { AdmissionDetails, AdmissionList } from 'src/app/interfaces/common-interface';
import { AdmissionRecordStatus, Constants } from 'src/app/constants/constants';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { isBlank } from 'src/app/utils/utils';

@Component({
  selector: 'app-admission-list',
  templateUrl: './admission-list.component.html',
  styleUrls: ['./admission-list.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, TranslateModule, SearchBarModule]
})
export class AdmissionListComponent implements OnChanges {
  noDataText = '';
  admissionList: any = [];
  @Input() patientID: string;
  @Input() patientActivityDetails: any;
  admissionRecordStatus = AdmissionRecordStatus;
  searchText = '';
  admissionParams = {
    currentPage: 0,
    rowsPerPage: 25,
    sortBy: 'startDate',
    sortDirection: 'DESC'
  };
  selectedDateOptions = Constants.filterSelectedOptions.lastThreeMonth;
  dateRange = JSON.parse(JSON.stringify(Constants.resetSelectedDateRange));
  showLoadMore = true;
  monthTypes = Constants.monthTypes.admissions;
  constructor(
    private httpService: HttpService,
    private sharedService: SharedService,
    private readonly router: Router
  ) {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.patientID && changes.patientID?.currentValue && changes.patientID?.currentValue !== changes.patientID?.previousValue) {
      this.admissionParams.currentPage = 0;
      this.getAdmissionList();
    }
  }

  getAdmissionList(event?): void {
    this.setNoDataText(true);
    const params = {
      ...this.admissionParams,
      patientIds: [this.patientID],
      searchKey: '',
      ...this.sharedService.getFilterDateRange(this.selectedDateOptions, this.dateRange, Constants.dateFormat.ymd)
    };
    if (!isBlank(this.searchText)) {
      params.searchKey = `%${this.searchText}%`;
    }
    const url = APIs.getAdmissionSearchList;
    this.httpService
      .doPost({
        endpoint: url,
        payload: params,
        loader: !event
      })
      .subscribe({
        next: (response: AdmissionList) => {
          if (this.admissionParams.currentPage === 0) {
            this.admissionList = [];
          }
          this.admissionList = this.admissionParams.currentPage === 0 ? response.content : [...this.admissionList, ...response.content];
          this.showLoadMore = this.admissionList.length !== response.page.totalElements;
        },
        error: () => {
          if (event) {
            event.target.complete();
          }
          this.admissionList = [];
          this.setNoDataText(false);
        },
        complete: () => {
          if (event) {
            event.target.complete();
          }
          this.setNoDataText(false);
        }
      });
  }

  viewAdmissionDetails(item: AdmissionDetails): void {
    const navigationExtras: NavigationExtras = {
      state: {
        admission: item,
        patientID: this.patientID,
        patientActivityDetails: this.patientActivityDetails
      }
    };
    this.router.navigate([PageRoutes.pahActivityAdmissionDetails], navigationExtras);
  }

  loadData(event): void {
    this.admissionParams.currentPage += Constants.pageCount;
    this.getAdmissionList(event);
  }

  searchAdmission(searchData: { text: string }): void {
    this.admissionParams.currentPage = 0;
    this.searchText = searchData.text;
    this.getAdmissionList();
  }

  loadFilterDate(value): void {
    this.admissionParams.currentPage = 0;
    this.selectedDateOptions = value.text !== '' ? value.text : 0;
    if (value.text === Constants.filterSelectedOptions.custom) {
      this.dateRange.from = value.dates.from;
      this.dateRange.to = value.dates.to;
    }
    this.getAdmissionList();
  }

  setNoDataText(beforeApiCall: boolean) {
    if (this.admissionList.length === 0) {
      if (beforeApiCall) {
        this.noDataText = 'ADMISSION.MESSAGES.ADMISSION_WAIT';
      } else if (this.searchText) {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_SEARCH';
      } else if (this.selectedDateOptions === Constants.filterSelectedOptions.lastThreeMonth) {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER_LAST_NINETY_DAYS';
      } else if (this.selectedDateOptions === Constants.filterSelectedOptions.custom) {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER';
      } else {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE';
      }
    }
  }
}
