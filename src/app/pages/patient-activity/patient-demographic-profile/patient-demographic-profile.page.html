<app-header [headerTitle]="headerValue"></app-header>
<div class="demographic-profile-tabs">
  <ion-segment mode="ios" class="tab-nav-contents" [value]="selectedValue | translate" (ionChange)="changeSegment($event.detail.value)" id="segment">
        <ion-segment-button value="profile" id="profile">
            <ion-label class="segment-label-font">{{'TITLES.DEMOGRAPHICS' | translate}}</ion-label>
        </ion-segment-button>
        @if (isMultiAdmissionsEnabled) {
        <ion-segment-button value="admission-details" id="admission-details">
            <ion-label class="segment-label-font">{{'ADMISSION.LABELS.ADMISSION_DETAILS' | translate}}</ion-label>
        </ion-segment-button>
        }
        <ion-segment-button value="address" id="address">
            <ion-label class="segment-label-font">{{'TITLES.ADDRESS_DETAILS' | translate}}</ion-label>
        </ion-segment-button>
        @if (patientActivityDetails?.patientCustomFields.length) {
        <ion-segment-button value="more" id="more">
            <ion-label class="segment-label-font">{{'TITLES.MORE_DETAILS' | translate}}</ion-label>
        </ion-segment-button>
        }
    </ion-segment>
</div>
<ion-content class="patientDemographic common-start-padding" [class.ion-padding-top]="selectedIndex !== 4">
    <container-element [ngSwitch]="selectedIndex">
        <ng-template [ngSwitchCase]=1>
            <ion-row class="ion-margin-bottom demographic-details-row-padding">
                <ion-col size="4">{{'LABELS.FIRST_NAME' |translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.firstName}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding">
                <ion-col size="4">{{'LABELS.LAST_NAME' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.lastName}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding">
                <ion-col size="4">{{'LABELS.DATE_OF_BIRTH' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.dateOfBirth | date:'MM/dd/yyyy'}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="patientActivityDetails?.gender">
                <ion-col size="4">{{'LABELS.GENDER' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.gender}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding">
                <ion-col size="4">{{'LABELS.MRN'| translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.patientIdentity?.IdentityValue}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.START_OF_CARE' | translate}}:</ion-col>
                <ion-col size="8">{{startOfCare}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.WORK_PHONE'| translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientWorkPhone}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.HOME_PHONE' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientHomePhone}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding">
                <ion-col size="4">{{'LABELS.CELL_PHONE' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.mobile ?
                    (patientActivityDetails?.externalInfo[0]?.mobile):
                    (patientActivityDetails?.mobile)}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding">
                <ion-col size="4">{{'LABELS.EMAIL'| translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.email}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.STREET_ADDRESS' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientAddress ?
                    (patientActivityDetails?.externalInfo[0]?.patientAddress):
                    (patientActivityDetails?.address)}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding">
                <ion-col size="4">{{'LABELS.STATUS' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientStatus ?
                    (patientActivityDetails?.externalInfo[0]?.patientStatus):
                    (patientActivityDetails?.status)}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.CITY' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientCity ?
                    (patientActivityDetails?.externalInfo[0]?.patientCity):
                    (patientActivityDetails?.city)}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.SITE_TEAM_COMPANY'| translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientSite}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.STATE' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientState ?
                    (patientActivityDetails?.externalInfo[0]?.patientState):
                    (patientActivityDetails?.state)}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="everDemographic">
                <ion-col size="4">{{'LABELS.ZIP' | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.externalInfo[0]?.patientZip ?
                    (patientActivityDetails?.externalInfo[0]?.patientZip):
                    (patientActivityDetails?.zip)}}</ion-col>
            </ion-row>
            <ion-row class="ion-margin-bottom demographic-details-row-padding" *ngIf="!everDemographic">
                <ion-col size="4">{{"LABELS.COMMENTS" | translate}}:</ion-col>
                <ion-col size="8">{{patientActivityDetails?.comments[0]?.comment}}</ion-col>
            </ion-row>
            @if(isMultiPartMRNEnabled){
                <ion-row class="ion-margin-bottom demographic-details-row-padding">
                    <ion-col size="4">{{"LABELS.EXTERNAL_USER_IDENTIFIER" | translate}}:</ion-col>
                    <ion-col size="8" class="ion-align-self-center">{{patientActivityDetails?.patientIdentity?.guId ||
                        ''}}</ion-col>
                </ion-row>
                }
        </ng-template>

        <ng-template [ngSwitchCase]=2>
            <ng-container>
                <app-address-list [patientID]="patientID" [patientActivityDetails]="patientActivityDetails">
                </app-address-list>
            </ng-container>
        </ng-template>
        <ng-template [ngSwitchCase]=3>
            <ion-row *ngFor="let item of patientActivityDetails?.patientCustomFields; let l = last;let f = first;"
                class="more-details-row-padding" [ngClass]="l ? 'set-border-bottom' : f ? 'set-margin-top' : '' ">
                <ion-col size="4">{{item?.fieldName}}</ion-col>
                <ion-col size="8">{{item?.fieldValue}}</ion-col>
            </ion-row>
        </ng-template>
        <ng-template [ngSwitchCase]=4>
            <app-admission-list [patientID]="patientID" [patientActivityDetails]="patientActivityDetails"></app-admission-list>
        </ng-template>
    </container-element>
</ion-content>
<app-footer></app-footer>