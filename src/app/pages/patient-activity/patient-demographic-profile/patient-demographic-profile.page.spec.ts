import { of, throwError } from 'rxjs';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { Apollo } from 'apollo-angular';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, AlertController } from '@ionic/angular';
import { TestConstants } from '../../../constants/test-constants';
import { CommonService } from '../../../services/common-service/common.service';
import { HttpService } from '../../../services/http-service/http.service';

import { PatientDemographicProfilePage } from './patient-demographic-profile.page';

describe('PatientDemographicProfilePage', () => {
  let component: PatientDemographicProfilePage;
  let fixture: ComponentFixture<PatientDemographicProfilePage>;
  let graphqlService: GraphqlService;
  let alertController: AlertController;
  const { alertSpy } = TestConstants;
  let common: CommonService;
  let httpService: HttpService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [PatientDemographicProfilePage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PatientDemographicProfilePage);
    component = fixture.componentInstance;

    graphqlService = TestBed.inject(GraphqlService);
    common = TestBed.inject(CommonService);
    alertController = TestBed.inject(AlertController);
    httpService = TestBed.inject(HttpService);
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    spyOn(alertController, 'dismiss').and.stub();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('Load Patient DetailsData', () => {
    spyOn(graphqlService, 'patientDetails').and.returnValue(throwError(''));
    component.loadPatientDetailsData();
    expect(component.loadPatientDetailsData).toBeDefined();
  });

  it('should call changeSegment', () => {
    component.changeSegment('Patient Profile');
    component.changeSegment('Address Details');
    component.changeSegment('More Details');
    component.changeSegment('');
    expect(component.changeSegment).toBeDefined();
  });
});
