.patientDemographic {
    ion-avatar {
        --border-radius: 0px;
        width: 30px;
        height: 30px;
    }
    .setLabelCenter {
        display: flex;
        align-items: center;
    }
    .set-input-border {
        border: 1px solid;
        color: #000;
    }
    ion-select {
        width: 100%;
    }
    .md {
        .set-start-padding {
            padding-left: 8px;
        }
        ion-select {
            --padding-start: 8px;
        }
    }
    .ios {
        ion-select {
            --padding-start: 0px;
        }
    }
    .more-details-row-padding {
        padding: 5px;
        border-style: solid solid none solid;
        border-width: 1px 1px 0px 1px;
        border-color: var(--ion-color-gray);
        margin: 0px 5px;
    }
    .set-border-bottom {
        border-bottom: 1px solid var(--ion-color-gray);
    }
    .set-margin-top {
        margin-top: 5px;
    }
    .demographic-details-row-padding {
        padding: 5px;
        border-style: solid;
        border-width: 1px;
        border-color: var(--ion-color-gray);
        margin: 0px 5px;
    }
}
.demographic-profile-tabs {
    margin-top: 40px;
    margin-left: 5px;
    margin-right: 5px;
    list-style-type: none;
    overflow: hidden;
    background: #ffffff;
    border: 1px solid var(--ion-color-skin-secondary-bright);
    border-radius: 10px;
    ion-segment {
        --background: #ffffff;
        border-bottom: 1px solid #e2e2e2;
        ion-segment-button {
            --background: #ffffff;
            --color: var(--ion-color-skin-secondary-bright);
            --background-checked: var(--ion-color-skin-secondary-bright);
            --color-checked: #ffffff;
            --background-hover: var(--ion-color-skin-secondary-bright);
            --ion-background-color: var(--ion-color-skin-secondary-bright);
        }
        ion-label {
            white-space: normal;
        }
        .segment-label-font {
            font-size: 12px;
        }
    }
}

