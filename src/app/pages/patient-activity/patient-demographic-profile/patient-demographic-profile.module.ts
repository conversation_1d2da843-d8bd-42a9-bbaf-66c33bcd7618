import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { SharedModule } from '../../../shared.module';

import { PatientDemographicProfilePageRoutingModule } from './patient-demographic-profile-routing.module';

import { PatientDemographicProfilePage } from './patient-demographic-profile.page';
import { AdmissionListComponent } from '../admission-list/admission-list.component';
import { AddressListComponent } from '../address-list/address-list.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    PatientDemographicProfilePageRoutingModule,
    SharedModule,
    AdmissionListComponent,
    AddressListComponent
  ],
  declarations: [PatientDemographicProfilePage]
})
export class PatientDemographicProfilePageModule {}
