import { CommonService } from 'src/app/services/common-service/common.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { ActivatedRoute } from '@angular/router';
import { Component, NgZone, OnInit } from '@angular/core';
import * as moment from 'moment';
import { PatientProfileDetails } from 'src/app/interfaces/patient-activity';
import { Constants } from 'src/app/constants/constants';
import { Config } from 'src/app/constants/config';
import { deepCopyJSON } from 'src/app/utils/utils';

@Component({
  selector: 'app-patient-demographic-profile',
  templateUrl: './patient-demographic-profile.page.html',
  styleUrls: ['./patient-demographic-profile.page.scss']
})
export class PatientDemographicProfilePage implements OnInit {
  selectedValue = 'profile';
  headerValue = 'TITLES.PATIENT_PROFILE';
  everDemographic = false;
  patientActivityDetails: any;
  patientID;
  selectedIndex = 1;
  startOfCare = '';
  isMultiAdmissionsEnabled = false;
  isMultiPartMRNEnabled = false;
  constructor(
    private route: ActivatedRoute,
    private sharedService: SharedService,
    private graphqlService: GraphqlService,
    private ngZone: NgZone,
    private common: CommonService
  ) {
    this.route.paramMap.subscribe((paramMap) => {
      this.patientID = paramMap.get('id');
    });
    this.everDemographic = this.sharedService.isEnableConfig(Config.enablePatientInfoFormThirdPartyApp);
  }

  ngOnInit(): void {
    this.isMultiAdmissionsEnabled = this.sharedService.isMultiAdmissionsEnabled;
    this.isMultiPartMRNEnabled = this.sharedService.isEnableConfig(Config.enableMultiPartMRNEnabled);
    this.loadPatientDetailsData();
  }

  changeSegment(value: string): void {
    this.selectedValue = value;
    switch (this.selectedValue) {
      case 'profile':
        this.selectedIndex = 1;
        this.headerValue = 'TITLES.PATIENT_PROFILE';
        break;
      case 'address':
        this.selectedIndex = 2;
        this.headerValue = 'TITLES.ADDRESS_DETAILS';
        break;
      case 'more':
        this.selectedIndex = 3;
        this.headerValue = 'TITLES.MORE_DETAILS';
        break;
      case 'admission-details':
        this.selectedIndex = 4;
        this.headerValue = 'ADMISSION.LABELS.ADMISSION_DETAILS';
        break;
      default:
        this.selectedIndex = 1;
        this.headerValue = 'TITLES.PATIENT_PROFILE';
        break;
    }
  }

  loadPatientDetailsData(): void {
    const params = {
      sessionToken: this.sharedService?.userData?.authenticationToken,
      patientUsersId: +this.patientID
    };
    this.sharedService.isLoading = true;
    this.graphqlService?.patientDetails(params)?.subscribe(
      (response: PatientProfileDetails) => {
        this.sharedService.isLoading = false;
        if (response.data.getSessionTenant.patientUsers.length) {
          this.ngZone.run(() => {
            this.patientActivityDetails = deepCopyJSON(response.data.getSessionTenant.patientUsers[0]);
            let email = '';
            if (this.patientActivityDetails.externalInfo.length) {
              const getStartDate = new Date(this.patientActivityDetails.externalInfo[0].patientRefStartDate).getTime();
              this.startOfCare = moment.utc(getStartDate).format(Constants.dateFormat.mdy);

              email = this.patientActivityDetails.externalInfo[0].patientEmail || '';

              if (email.includes('unknown')) {
                email = '';
              } else if (!this.sharedService.validateEmail(email)) {
                email = '';
              }
              this.patientActivityDetails['email'] = email;
            } else {
              email = this.patientActivityDetails.emails[0].value || '';
              if (email.includes('unknown')) {
                email = '';
              } else if (!this.sharedService.validateEmail(email)) {
                email = '';
              }
              this.patientActivityDetails['email'] = email;
              let status = this.common.getTranslateData('GENERAL.ACTIVE');
              if (this.patientActivityDetails.status !== this.common.getTranslateData('GENERAL.VIRTUAL_USER')) {
                status = this.patientActivityDetails.status;
              }
              this.patientActivityDetails.status = status;
            }
          });
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }

  ionViewWillLeave() {
    this.sharedService.resetSelectedDateFilterData();
  }
}
