import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-patient-activity-profile',
  templateUrl: './patient-activity-profile.page.html',
  styleUrls: ['./patient-activity-profile.page.scss']
})
export class PatientActivityProfilePage implements OnInit {
  public patientMRN: string;
  public patientID: number;
  public patientActivityDetails: any = {};
  constructor(
    private readonly route: ActivatedRoute,
    private sharedService: SharedService,
    private graphqlService: GraphqlService
  ) {
    this.route.paramMap.subscribe((paramMap) => {
      this.patientID = Number(paramMap.get('id'));
    });
  }

  ngOnInit() {
    this.loadPatientDetailsData();
  }

  loadPatientDetailsData() {
    const params = {
      sessionToken: this.sharedService.userData?.authenticationToken,
      patientUsersId: this.patientID
    };
    this.sharedService.isLoading = true;
    this.graphqlService.patientDetails(params)?.subscribe(
      (response: any) => {
        this.sharedService.isLoading = false;
        if (response.data.getSessionTenant?.patientUsers?.length) {
          this.patientActivityDetails = response.data.getSessionTenant.patientUsers[0];
          if (this.patientActivityDetails.patientIdentity) {
            this.patientMRN = this.patientActivityDetails.patientIdentity.IdentityValue.toString();
          }
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }
}
