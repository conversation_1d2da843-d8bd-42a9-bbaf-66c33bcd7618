<app-header headerTitle="TITLES.PATIENT_PROFILE"></app-header>

<ion-content class="patientActivityProfile">
  <ion-card mode="ios">
    <ion-item lines="none" class="showPatientDetails">
      <ion-thumbnail slot="start" class="setHightWidth">
        <img src="assets/images/no-avatar.png" *ngIf="!patientActivityDetails?.avatar" alt="no-avatar" />
      </ion-thumbnail>
      <ion-label>
        {{patientActivityDetails?.displayName}}
        <p *ngIf="patientActivityDetails?.gender">
          <ion-text color="blumine">
            {{patientActivityDetails?.gender }} /
            {{patientActivityDetails?.dateOfBirth |
            countYear}} {{'LABELS.YRS' | translate}}
          </ion-text>
        </p>
        <p *ngIf="patientActivityDetails?.dateOfBirth && !patientActivityDetails?.gender">
          <ion-text color="blumine">
            {{patientActivityDetails?.dateOfBirth | countYear}} {{'LABELS.YRS' | translate}}
          </ion-text>
        </p>
        <p>
          <ion-text color="blumine">{{'LABELS.MRN' | translate}} {{patientMRN || '-'}}</ion-text>
        </p>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-list>
    <ion-item detail="true" lines="full" class="ion-padding-bottom" id="demographic-profile"
      routerLink="/patient-activity/demographic-profile/{{patientActivityDetails?.id}}">
      <ion-avatar slot="start">
        <img src="assets/icon/demographic-profile.png" class="setIcon" alt="demographic">
      </ion-avatar>
      <ion-label>{{'LABELS.DEMOGRAPHIC_PROFILE' | translate}}</ion-label>
    </ion-item>
    <ion-item detail="true" lines="full" class="ion-padding-bottom" id="documents"
      routerLink="/patient-activity/documents/{{patientActivityDetails?.id}}">
      <ion-avatar slot="start">
        <img src="assets/icon/documents.png" class="setIcon" alt="documents">
      </ion-avatar>
      <ion-label>{{'LABELS.DOCUMENTS' | translate}}</ion-label>
    </ion-item>
  </ion-list>
</ion-content>
<app-footer></app-footer>