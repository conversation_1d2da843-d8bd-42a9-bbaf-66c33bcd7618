import { of, throwError } from 'rxjs';
import { Apollo } from 'apollo-angular';
import { TranslateModule } from '@ngx-translate/core';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { PatientActivityProfilePage } from './patient-activity-profile.page';

describe('PatientActivityProfilePage', () => {
  let component: PatientActivityProfilePage;
  let fixture: ComponentFixture<PatientActivityProfilePage>;
  let graphqlService: GraphqlService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [PatientActivityProfilePage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(PatientActivityProfilePage);
    component = fixture.componentInstance;
    graphqlService = TestBed.inject(GraphqlService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('Load Patient DetailsData', () => {
    component.loadPatientDetailsData();
    expect(component.loadPatientDetailsData).toBeDefined();
  });
  it('Load Patient DetailsData', () => {
    spyOn(graphqlService, 'patientDetails').and.returnValue(throwError(''));
    component.loadPatientDetailsData();
    expect(component.loadPatientDetailsData).toBeDefined();
  });

  it('should load patient details data successfully', () => {
    const mockResponse: any = of({
      data: {
        getSessionTenant: {
          patientUsers: [
            {
              patientIdentity: {
                IdentityValue: 12345
              }
            }
          ]
        }
      }
    });
    spyOn(graphqlService, 'patientDetails').and.returnValue(mockResponse);

    component.patientID = 123;

    component.loadPatientDetailsData();
    expect(component.loadPatientDetailsData).toBeDefined();
  });
});
