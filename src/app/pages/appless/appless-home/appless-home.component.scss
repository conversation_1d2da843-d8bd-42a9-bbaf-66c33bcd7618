.card {
  border: 1px solid #ccc;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
}

h2 {
  margin-top: 0;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  padding: 8px 0;
}

ion-card {
  margin-bottom: 16px;
}

ion-card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}

.ml-auto {
  margin-left: auto;
}

ion-card-title ion-icon {
  font-size: 1.5rem;
  color: var(--ion-color-light);
}

ion-card-title img {
  margin-right: 12px; /* Adjust space between icon and text */
  width: 30px;
  height: 30px;
}

ion-card-title h2 {
  font-size: 1.3rem;
  margin: 0;
  color: #FFF;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Add text shadow for better contrast */
}

ion-spinner {
  display: block;
  margin: 16px auto;
}

ion-list {
  padding: 0;
}

ion-item {
  --padding-start: 0;
  font-size: 1rem;
  --background-hover: var(--ion-color-light-shade); /* Ensure background color on hover */
  --background-activated: var(--ion-color-primary); /* Ensure background color on click */
  --color-activated: var(--ion-color-light); /* Ensure text color on click */
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  width: 100%;
  
  .item-details {
    width: 100%;
    text-align: center;
    
    .item-header {
      display: flex;
      justify-content: center;
      align-items: center;
      
      .item-name {
        color: #666;
        font-size: 1rem;
      }
    }
  }
}

.scrollable {
  max-height: 300px; /* Adjust this value as needed */
  overflow-y: auto;
}

.hp-btn1 {
  background: var(--ion-color-hpbtn1);
  color: var(--ion-color-light); /* Ensure font color matches the background */
  flex-grow: 1;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Add text shadow for better contrast */
}

.hp-btn2 {
  background: var(--ion-color-hpbtn2);
  color: var(--ion-color-dark); /* Ensure font color matches the background */
  flex-grow: 2;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Add text shadow for better contrast */
}

.hp-btn3 {
  background: var(--ion-color-hpbtn3);
  color: var(--ion-color-light); /* Ensure font color matches the background */
  flex-grow: 1;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Add text shadow for better contrast */
}

.hp-btn4 {
  background: var(--ion-color-hpbtn4);
  color: var(--ion-color-light); /* Ensure font color matches the background */
  flex-grow: 1;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Add text shadow for better contrast */
}

.appless-row {
  .page-content {
    max-width: 900px;
    margin: 1.5rem auto;
  }
}

.pending-list {
  .item.read.set-grey-background {
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 8px;
    padding-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease, transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .item.read.set-grey-background:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
  }

  .data-list-title {
    font-size: 1rem;
    color: #333;
    margin-bottom: 4px;
    gap: 5px;
  }

  .item-name {
    font-size: 1.1rem;
    display: block;
  }

  .item-info {
    display: flex;
    align-items: center;
    flex-direction: row;
  }

  .font-weight-lite {
    font-weight: 300;
    margin-top: 8px;
  }

  .set-align-center {
    vertical-align: middle;
  }

  .item-details {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0.5rem 0;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
  }

  .item-footer {
    display: flex;
    align-items: center;
    color: #999;
    font-size: 0.9rem;
  }

  .item-datetime {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #666;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// Add message-specific styles
.message-display {
  --padding-start: 0;
  --inner-padding-end: 0;

  &.unread {

    border-left: 5px solid #64c28d;
    --background: #ecf8f1;
  }
  
  .inbox-message-details {
    display: flex;
    width: 100%;
    padding: 12px 1rem;
    gap: 12px;
    align-items: flex-start;
  }

  .avatar {
    .avatar-container {
      position: relative;
      width: 40px;
      height: 40px;

      
      
      img {
        width: 40px;
        height: 40px;
        border-radius: 100%;
        -webkit-border-radius: 100%;
        -moz-border-radius: 100%;
        -ms-border-radius: 100%;
        -webkit-touch-callout: none;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        object-fit: cover;
        background: #e6e5eb;
        border: 1px solid #e6e6e6;
      }

      .unread-count {
        position: absolute;
        top: -5px;
        right: -5px;
        background: var(--ion-color-danger);
        color: white;
        border-radius: 50%;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        
        span {
          padding: 0 4px;
        }
      }
    }

  }

  .chat-details-middle {
    flex: 1;
    min-width: 0;

    .wrap-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .inbox-per-name {
      font-weight: 600;
      color: #4b4949;
    }

    .inbox-mes-name {
      color: var(--ion-color-medium);
      font-size: 0.9em;
    }
  }

  .date-block {
    min-width: 70px;
    text-align: right;
    color: var(--ion-color-medium);
    font-size: 0.8em;
  }
}

// Add unread message styling
.unread {
  font-weight: 500;
}

.unread-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ion-color-danger, #eb445a);
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  font-size: 12px;
  margin-left: 8px;
  padding: 0 6px;
  font-weight: bold;
}

.message-date-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 5px;
  margin-bottom: 6px;
}

.unread-indicator {
  display: flex;
  align-items: center;
  color: var(--ion-color-danger);
  margin-right: 5px;
}

.unread-icon {
  font-size: 16px;
}

.unread-counter {
  font-weight: bold;
  color: var(--ion-color-danger, #eb445a);
  font-size: 12px;
  margin-right: 2px;
}

.date-block {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

// Responsive design
@media (max-width: 768px) {
  .item-header, .item-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .item-header span, .item-info span {
    margin-bottom: 4px;
  }
}

@media (min-width: 769px) {
  .item-header, .item-info {
    flex-direction: row;
    align-items: center;
  }

  .item-header span, .item-info span {
    margin-bottom: 0;
  }
}

.background-white {
  --ion-background-color: #FFF; 
}

.card-content {
  padding: 0px;
}

// Add these styles to your existing SCSS file

.message-body.compact {
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-size: 0.85rem;
  line-height: 1.2;
  max-width: 100%;
  
  .message-icon {
    margin-right: 5px;
    color: var(--ion-color-medium);
    opacity: 0.7;
  }

  .truncate-message {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
  }

  .preview-text {
    font-weight: 400;
    color: var(--ion-color-medium-shade);
  }

  .pending-status {
    font-style: italic;
    opacity: 0.8;
  }
}

.item-container:hover {
  background-color: rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.download-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(66, 180, 101, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  padding: 0;
  border: none;
  
  ion-icon {
    font-size: 1.4rem;
    color: var(--ion-color-success);
    opacity: 0.9;
    transition: all 0.2s ease;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    
    ion-icon {
      color: white;
      opacity: 1;
    }
  }
}

// Adjust the date block to pull it closer to the download icon
.download-icon + .date-block {
  margin-left: 0; // Ensure no extra margin between download icon and date
  padding-left: 0; // Remove any padding that might create space
}