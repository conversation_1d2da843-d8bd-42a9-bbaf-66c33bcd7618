import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { PageRoutes } from 'src/app/constants/page-routes';
import { SessionService } from 'src/app/services/session-service/session.service';
import { Observable, Subscription } from 'rxjs';
import { finalize, map } from 'rxjs/operators';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { APIs } from 'src/app/constants/apis';
import { FormWorkListPayload } from 'src/app/interfaces/common-interface';
import * as moment from 'moment';
import { Config } from 'src/app/constants/config';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Location } from '@angular/common';
import { MessageInboxPayload } from 'src/app/interfaces/messages';
import { Signature } from 'src/app/constants/signature';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { isPresent } from 'src/app/utils/utils';
import { FormService } from '../../form-center/services/form.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { UnicodeConvertPipe } from 'src/app/pipes/unicodeConvert/unicode-convert.pipe';
import { environment } from 'src/environments/environment';

interface ListItem {
  id: number;
  name?: string;
  content?: string;
  sentDate: Date;
  form?: any;
  chatroomId?: any;
}

interface DownloadFormItem {
  formId: number;
  formSubmissionId: number;
  formName: string;
  patientName: string;
  submittedDate: string;
  patientId: number;
}

interface DownloadFormParams {
  formId: number;
  formSubmissionId: number;
}

interface DownloadFormsResponse {
  content: DownloadFormItem[];
  totalElements?: number;
  totalPages?: number;
  size?: number;
  number?: number;
}

interface CardConfig {
  type: string;
  title: string;
  icon: string;
  btnClass: string;
  data: ListItem[];
  expanded: boolean;
  loading: boolean;
}

@Component({
  selector: 'app-appless-home',
  templateUrl: './appless-home.component.html',
  styleUrls: ['./appless-home.component.scss'],
  providers: [UnicodeConvertPipe]
})
export class ApplessHomeComponent implements OnInit, OnDestroy {
  pendingForms: any[] = [];
  pendingDocuments: any[] = [];
  messages: any[] = [];
  dateRange = {
    from: '',
    to: ''
  };
  selectedDateOptions = Constants.filterSelectedOptions.lastMonth;
  messageTypes = Constants.messageListTypes;
  private readonly subscriptions = new Subscription();

  cards: CardConfig[] = [
    {
      type: 'form',
      title: 'LABELS.PENDING_FORMS',
      icon: '../../assets/icon/home/<USER>',
      btnClass: 'hp-btn2',
      data: [],
      expanded: true,
      loading: true
    },
    {
      type: 'downloadForm',
      title: 'LABELS.DOWNLOAD_FORMS',
      icon: '../../assets/icon/home/<USER>',
      btnClass: 'hp-btn4',
      data: [],
      expanded: true,
      loading: true
    },
    {
      type: 'document',
      title: 'LABELS.PENDING_DOCUMENTS',
      icon: '../../assets/icon/home/<USER>',
      btnClass: 'hp-btn3',
      data: [],
      expanded: true,
      loading: true
    },
    {
      type: 'downloadDocument',
      title: 'LABELS.DOWNLOAD_DOCUMENTS',
      icon: '../../assets/icon/home/<USER>',
      btnClass: 'hp-btn1',
      data: [],
      expanded: true,
      loading: true
    },
    {
      type: 'message',
      title: 'TITLES.MESSAGES',
      icon: '../../assets/icon/home/<USER>',
      btnClass: 'hp-btn4',
      data: [],
      expanded: true,
      loading: true
    }
  ];

  constructor(
    private readonly router: Router, 
    private readonly route: ActivatedRoute, 
    public sharedService: SharedService, 
    public sessionService: SessionService,
    public readonly persistentService: PersistentService,
    private readonly permissionService: PermissionService,
    private readonly httpService: HttpService,
    private readonly location: Location,
    private readonly graphqlService: GraphqlService,
    private readonly formService: FormService,
    public readonly common: CommonService
  ) {
    this.sessionService.sessionLoader = false;
  } // Inject HttpClient

  ngOnInit() {
    if (!this.sharedService.userData) {
      sessionStorage.removeItem(Constants.storageKeys.appLessHomeLoggedIn);
      this.location.back();
      return;
    }
    this.cards.forEach(card => this.loadData(card));
    this.setupPollingSubscriptions();
    this.subscriptions.add(this.sharedService.appLessHomeNext.subscribe((data: { id: number; type: string, message: string }) => {
      if (!data.id) return;
      this.sharedService.appLessHomeNext.next({id: 0, type: '', message: ''});
      const card = this.cards.find(c => c.type === data.type);
      if (card) {
        card.loading = true;
        this.loadData(card);
      }
      const prioritizedTypes = [...new Set([data.type, 'form', 'document'])];
      let isAlertShown = false;
      
      // Find first type with pending items
      for (const type of prioritizedTypes) {
        const typeData = this.sharedService.appLessHomeData?.[type];
        
        // Skip if no data for this type
        if (!typeData) continue;
        
        // Find pending items (excluding current item)
        const pendingItems = typeData.filter(item => parseInt(item.id) !== data.id);
        if (pendingItems.length === 0) continue;
        
        // Show alert for the first type with pending items
        const title = data.message || this.common.getTranslateData('MESSAGES.SUBMISSION_IS_SAVED');
        const itemsText = `${pendingItems.length} ${this.common.getTranslateData('MESSAGES.MORE')} ${type}(s)`;
        const message = this.common.getTranslateDataWithParam('MESSAGES.NEXT_FORM_OR_HOME', { more: itemsText });
        
        const buttons = [
          {
            text: this.common.getTranslateData('BUTTONS.CANCEL'),
            class: 'warn-btn alert-cancel'
          },
          {
            text: this.common.getTranslateData('BUTTONS.NEXT'),
            class: 'warn-btn alert-ok'
          }
        ];
        isAlertShown = true;
        this.common.showAlert({
          message, 
          header: title, 
          buttons,
          cssClass: 'show-button-center',
          mode: 'ios'
        })
        .then((confirmation) => {
          if (confirmation) {
            this.navigateToDetail(type, pendingItems[0]);
          }
        });
        
        break; // Only show alert for the first matching type
      }
      this.showFeedbackMessage(isAlertShown, data.message);
    }));
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  private setupPollingSubscriptions() {
    this.sharedService.enableFormPolling();
    this.sharedService.enableDocumentPolling();

    this.subscriptions.add(this.sharedService.messageFormCountUpdated.subscribe((pollingData) => {
      if (pollingData.countType === Constants.countTypes.forms && pollingData.isPolling) {
        this.refreshCardsByType(['form']);
      }
    }));

    this.subscriptions.add(this.sharedService.documentCountUpdated.subscribe(() => {
      this.refreshDocumentCards();
    }));

    this.subscriptions.add(this.sharedService.documentPollingEvent.subscribe((data: any) => {
      if (data?.[0]?.['notifyOnSubmit']) {
        this.refreshDocumentCards();
      }
    }));
  }

  private refreshCardsByType(cardTypes: string[]): void {
    cardTypes.forEach(type => {
      const card = this.cards.find(card => card.type === type);
      if (card) {
        card.loading = true;
        this.loadData(card);
      }
    });
  }

  private refreshDocumentCards(): void {
    this.refreshCardsByType(['document', 'downloadDocument']);
  }

  private handleApiCall(apiCall: Observable<any>, card: CardConfig) {
    apiCall
      .pipe(
        finalize(() => {
          card.loading = false;
        })
      )
      .subscribe({
        next: (data) => {
          card.data = data;
          this.sharedService.appLessHomeData[card.type] = data;
          if (card.type === 'message') {
            this.sharedService.messageList = data;
            this.sortMessages();
          }
        },
        error: (error) => {
          card.data = [];
        }
      });
  }

  private getFormData(card: CardConfig) {
    const endpoint = APIs.getMyFormWorklist;
    const body: FormWorkListPayload = {
      roleid: this.sharedService.userData.roleId,
      zone: moment.tz.guess(),
      isForms: true,
      isPrivilege:
        this.permissionService.userHasPermission(Permissions.viewFormEntries) ||
        this.permissionService.userHasPermission(Permissions.manageTenants),
      limit: Constants.offset,
      offset: Constants.offset * Constants.defaultPageCount,
      searchText: '',
      orderData: Constants.orderData.sentOn,
      orderby: Constants.sortOrderDesc,
      isScheduled: Constants.noValue,
      archived: false,
      pending: true,
      completed: false,
      draft: false,
      accessSecurityEnabled: this.sharedService.userData.accessSecurityEnabled,
      startDate: '',
      endDate: '',
      ...this.sharedService.getFilterDateRange(this.selectedDateOptions, this.dateRange),
      citusRoleId: this.sharedService.userData.group,
      enableIntegrationStatus: false,
      enableSftpIntegration: false
    };

    if (this.sharedService.isEnableConfig(Config.enableNursingAgencies)) {
      body.nursingAgencies = this.sharedService.userData.nursing_agencies;
    }

    const apiCall = this.httpService.doGet({ endpoint, extraParams: body, loader: false }).pipe(
      map((response) => {
        const formateResponse = this.formService.generalizeResponse(
          response,
          Constants.formTypes.pending,
          Constants.formPendingStatus
        );
        return formateResponse.map((form) => ({
          id: form.id,
          name: form.form_name,
          initiatorName: form.createdUser,
          patientName: form.patientName,
          tenantName: form.tenantName,
          patientDob: form.patientDob,
          sentDate: form.sentDate,
          form
        }));
      })
    );

    this.handleApiCall(apiCall, card);
  }

  private getMessageData(card: CardConfig) {
    const messageReqBody: MessageInboxPayload = {
      archived: false,
      viewInventory: true,
      showChatHistory: true,
      pageCount: Constants.defaultPageCount,
      searchKeyword: '',
      flagValue: 0,
      priorityValue: 0,
      siteIds: [],
      isShowLoader: true,
      unread: false,
      filterTags: [],
      mentionUsers: false,
      selectedDateOptions: this.selectedDateOptions,
      dateRange: this.dateRange,
      chatThreadTypes: [],
    };

    const apiCall = this.sharedService.fetchAllMessages(messageReqBody).pipe(
      map((response) => {
        return response.message.map((message) => ({
          ...message,
          chatroomId: message.chatroomId
        }));
      })
    );

    this.handleApiCall(apiCall, card);
    this.sharedService.enableMessageSocket();
    if (!this.sharedService.messageListUpdated.observed) {
      this.subscriptions.add(this.sharedService.messageListUpdated.subscribe((value: any) => {
        this.updateMessagesOnEvents(value);
      }));
    }
  }

  getDocumentData(card: CardConfig, signatureStatus: string) {
    const params = {
      siteId: '0',
      tenantId: this.sharedService.userData?.tenantId,
      crossTenantId: this.sharedService.userData?.crossTenantId,
      signatureRequestFilterInput: {
        signatureStatus,
        searchText: ''
      },
      ...this.sharedService.getFilterDateRange(this.selectedDateOptions, this.dateRange),
      paginationInput: {
        fetchCount: true,
        orderData: '',
        orderby: Constants.sortOrderAsc
      }
    };

    const apiCall = this.graphqlService.getDocuments(params).pipe(
      map(({ data }) => {
        return data?.mySignatureRequest?.signatureRequest?.filter((doc) => doc.signatureStatus === signatureStatus).map((doc) => ({
          id: doc.id,
          name: doc.displayText.text,
          sentDate: doc.createdOn,
          initiatorName: doc.owner,
          downloadUrl: doc.downloadUrl,
          senderTenant: doc.senderTenant ?? doc.tenantDetails?.senderTenant,
          displayLabel: doc.displayText.text,
          ...doc
        }));
      })
    );

    this.handleApiCall(apiCall, card);
  }

  loadData(card: CardConfig) {
    if (!card) {
      return;
    }
    card.loading = true;
    switch (card.type) {
      case 'form':
        this.getFormData(card);
        break;
      case 'downloadForm':
        this.getDownloadFormData(card);
        break;
      case 'document':
        this.getDocumentData(card, Signature.signatureStatus.signaturePendingStatus);
        break;
      case 'downloadDocument':
        this.getDocumentData(card, Signature.signatureStatus.signatureSignedStatus);
        break;
      case 'message':
        this.getMessageData(card);
        break;
      default:
        return;
    }
  }

  toggleSection(card: any): void {
    if (!card) {
      return; // Gracefully handle null or undefined card
    }
    card.expanded = !card.expanded;
  }

  navigateToDetail(type: string, item: any, index?: number) {
    this.sharedService.userData.appLessSession = true;
    const navigationExtras: NavigationExtras = {
      skipLocationChange: true
    };
    if (type === 'form') {
      const form = item.form;
      form.interactionChannel = 0;
      navigationExtras.state = {
        viewData: { form },
        formStatus: Constants.formPendingStatus
      };
      navigationExtras.queryParams = { id: form.id }; 
      this.router.navigate([`.${PageRoutes.viewForms}`], navigationExtras);
    } else if (type === 'document' || type === 'downloadDocument') {
      const documentInfo = {
        ...item,
        id: item.id,
        downloadUrl: item.downloadUrl,
        senderTenant: item.senderTenant ?? item.tenantDetails?.senderTenant ?? this.sharedService.userData?.tenantId,
        displayLabel: item.name ?? item.displayLabel ?? item.displayText?.text,
        signatureStatus: type === 'downloadDocument' ? 
          Signature.signatureStatus.signatureSignedStatus : 
          Signature.signatureStatus.signaturePendingStatus,
          enableApplessWorkflow: '0',
          integrationStatus: null
      };
      
      navigationExtras.state = { documentInfo };
      if (type === 'downloadDocument') {
        navigationExtras.skipLocationChange = false;
      }
      this.router.navigate([`/document-center/view-document/${item.id}/${documentInfo.senderTenant}`], navigationExtras);
    } else if (type === 'message') {
      this.sessionService.applessMessagingFlow = true;
      this.router.navigate([`/message-center/messages/active/chat/${item.chatroomId}`], {
        skipLocationChange: false
      });
    }
  }

  loadFilterData(value: { text: number; dates?: { from: string; to: string } }): void {
    if (value.text === Constants.filterSelectedOptions.custom && isPresent(value?.dates?.from) && isPresent(value?.dates?.to)) {
      this.dateRange = value.dates;
    }
    this.selectedDateOptions = value.text;
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterArchivedMessages, value.dates);
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsArchivedMessages, value.text);
    this.cards.forEach(card => this.loadData(card));
  }

  refreshCard(event: Event, card: CardConfig) {
    if (!card) {
      return; // Gracefully handle null or undefined card
    }
    event.stopPropagation(); // Prevent the card from collapsing
    card.loading = true;
    this.loadData(card);
  }

  get messageList() {
    return this.cards.find((card) => card.type === 'message')?.data || [];
  }

  set messageList(value: any) {
    const card = this.cards.find((card) => card.type === 'message');
    if (card) {
      card.data = value;
    }
  }

  updateMessagesOnEvents(value: any): void {
    if (value?.chatRoomId) {
      this.updateLoadingStatus(value.chatRoomId);
    } else if (value.message ?? value.maskedParent) {
      this.handleMessageThread(value);
    }
    this.sharedService.appLessHomeData.message = this.messageList;
  }

  private updateLoadingStatus(chatRoomId: number): void {
    const index = this.messageList.findIndex((x) => +x.chatroomId === +chatRoomId);
    if (index !== -1) {
      this.messageList[index].loading = true;
    }
  }

  private handleMessageThread(value: any): void {
    const messageThread = value?.maskedParent || value?.message;
    const index = this.messageList.findIndex((x) => +x.chatroomId === +messageThread.chatroomId);
    let { incrementCount } = value;

    if (index === -1 && !value?.removeThread) {
      this.addNewMessageThread(messageThread);
      incrementCount = messageThread?.maskedSubCount > 0 ? messageThread?.maskedUnreadCount : messageThread.unreadCount;
    } else if (value?.removeThread) {
      this.removeMessageThread(index);
    } else {
      this.updateExistingMessageThread(index, messageThread);
    }

    if (isPresent(incrementCount)) {
      this.updateUnreadCount(incrementCount);
    }

    this.sortMessages();
  }

  private addNewMessageThread(messageThread: any): void {
    this.messageList ??= [];
    this.messageList.unshift(messageThread);
  }

  private removeMessageThread(index: number): void {
    this.messageList.splice(index, 1);
  }

  private updateExistingMessageThread(index: number, messageThread: any): void {
    Object.assign(this.messageList[index], messageThread);
  }

  private updateUnreadCount(incrementCount: number): void {
    this.sharedService.messageUnreadCount += incrementCount;
    if (this.sharedService.messageUnreadCount < 0) {
      this.sharedService.messageUnreadCount = 0;
    }
  }

  sortMessages() {
    this.messageList.sort((a, b) => {
      const sentComparison = Number(b.messageOrder) - Number(a.messageOrder);
      if (a.pinnedStatus && !b.pinnedStatus) {
        return -1;
      }
      if (!a.pinnedStatus && b.pinnedStatus) {
        return 1;
      }
      return sentComparison;
    });
  }

  getCardItems(type: string): any[] {
    if (!this.sharedService?.appLessHomeData) {
      return [];
    }
    return this.sharedService.appLessHomeData[type] ?? [];
  }

  private showFeedbackMessage(isAlertShown: boolean, message: string): void {
    if (!isAlertShown && message) {
      setTimeout(() => {
        this.common.showMessage(message);
      }, 0);
    }
  }

  downloadDocument(event: Event, item: any): void {
    event.stopPropagation();
    event.preventDefault();
    if (!item.downloadUrl) {
      this.common.showMessage(this.common.getTranslateData('MESSAGES.NO_DOWNLOAD_URL'));
      return;
    }
    
    try {
      window.open(item.downloadUrl, '_blank');
    } catch (error) {
      console.error('Error downloading document:', error);
      this.common.showMessage(this.common.getTranslateData('MESSAGES.DOWNLOAD_ERROR'));
    }
  }


  private getDownloadFormData(card: CardConfig) {
    // Use the Java API endpoint for Download forms
    const endpoint = APIs.getDownloadForms;
    const params = {
      rowsPerPage: 100,
      currentPage: 0,
      sortDirection: 'DESC',
      sortBy: 'submitted_date'
    };

    const apiCall = this.httpService
      .doPost({
        endpoint,
        payload: params,
        loader: false,
        version: Constants.apiVersions.apiV5
      })
      .pipe(
        map((response: DownloadFormsResponse) => {
          return response.content.map((form: DownloadFormItem, index: number) => ({
            id: index,
            formId: form.formId,
            formSubmissionId: form.formSubmissionId,
            name: form.formName,
            patientName: form.patientName,
            sentDate: form.submittedDate,
            form: {
              id: index,
              formId: form.formId,
              formSubmissionId: form.formSubmissionId,
              formName: form.formName,
              patientName: form.patientName,
              sentDate: form.submittedDate,
              patientId: form.patientId
            }
          }));
        })
      );

    this.handleApiCall(apiCall, card);
  }

  downloadForm(event: Event, item: DownloadFormParams): void {
    event.stopPropagation();
    event.preventDefault();
    const tokenEndpoint = 'generate-structured-form-data-pdf.php';
    const payload = {
      formId: item.formId,
      submissionId: item.formSubmissionId
    };

    const params = {
      type: 2,
      zone: moment.tz.guess()
    };

    this.httpService
      .doPost({
        endpoint: tokenEndpoint,
        payload,
        extraParams: params,
        loader: true,
        version: Constants.apiVersions.apiV4,
        responseType: 'text'
      })
      .subscribe({
        next: (token: string) => {
          if (token) {
            const downloadUrl = `${environment.apiBasePath}/${Constants.apiVersions.apiV4}/form-download.php?filetoken=${token}`;

            try {
              window.open(downloadUrl, '_blank');
            } catch {
              this.common.showMessage(this.common.getTranslateData('MESSAGES.DOWNLOAD_ERROR'));
            }
          } else {
            this.common.showMessage(this.common.getTranslateData('MESSAGES.NO_DOWNLOAD_URL'));
          }
        },
        error: () => {
          this.common.showMessage(this.common.getTranslateData('MESSAGES.DOWNLOAD_ERROR'));
        }
      });
  }
}
