<app-header headerTitle="TITLES.APPLESS_HOME" [hideSocketButton]="true" [hideHelpButton]="true"></app-header>
<ion-content [fullscreen]="true" class="background-white">
  <ion-header collapse="condense" mode="ios">
    <ion-toolbar mode="ios"></ion-toolbar>
  </ion-header>
  
  <div class="appless-row">
    <div class="page-content">
      <app-search-bar 
        (closeDateFilter)="loadFilterData($event)"
        [selectedDateOptions]="selectedDateOptions"
        [isDateFilter]="true"
        [showSearch]="false"
        [searchText]=""
        [dateRange]="dateRange">
      </app-search-bar>
      <ion-card *ngFor="let card of cards" mode="ios">
        <ion-card-header [class]="card.btnClass" (click)="toggleSection(card)" (keydown)="toggleSection(card)" tabindex="0">
          <ion-card-title>
            <img [src]="card.icon" [alt]="card.title | translate" aria-hidden="true" />
            <h2>{{ card.title | translate }}
            </h2>
            <ion-icon name="refresh-outline" (click)="refreshCard($event, card)" (keypress)="refreshCard($event, card)" aria-label="Refresh" class="ml-auto"></ion-icon>
            <ion-icon [name]="card.expanded ? 'chevron-up-outline' : 'chevron-down-outline'"></ion-icon>
          </ion-card-title>
        </ion-card-header>
        <ion-card-content *ngIf="card.expanded" class="card-content">
          <ion-spinner *ngIf="card.loading" [attr.aria-label]="card.title | translate"></ion-spinner>
          
          <!-- Unified display for all card types -->
          <div *ngIf="!card.loading" class="scrollable">
            <ion-list>
              <ion-item *ngFor="let item of card.data; let i = index" class="message-display item-container hover-effect"
                      [ngClass]="{ unread: card.type === 'message' && item.hasUnreadMessages && (item.maskedUnreadCount > 0 || item.unreadCount > 0)  }"
                      (click)="navigateToDetail(card.type, item, i)" (keydown)="navigateToDetail(card.type, item, i)" tabindex="0">
                <div class="row inbox-message-details">
                  <div class="avatar" *ngIf="card.type === 'message'">
                    <div class="avatar-container">
                      <img
                        draggable="false"
                        appAvatar
                        #avatarDirective="avatarDirective"
                        [src]="avatarDirective.avatarSrc"
                        (error)="avatarDirective.onImageError($event)"
                        [avatar]="item.chatAvatar"
                        [messageCategory]="item.messageCategory"
                        alt=""
                        outOfOfficeStatus
                        [oooInfo]="item.oooInfo"
                        [customClass]="'chat-list-red-badge'"
                      />
                      <div class="unread-count" *ngIf="item.hasUnreadMessages && (item.maskedUnreadCount > 0 || item.unreadCount > 0)">
                        <span>{{ item.maskedSubCount > 0 ? item.maskedUnreadCount : item.unreadCount }}</span>
                      </div>
                      <ion-icon
                        id="expand-button"
                        *ngIf="item.maskedSubCount > 0"
                        class="masked-caret"
                        [name]="item.childExpanded ? 'chevron-up-outline' : 'chevron-down-outline'"
                      ></ion-icon>
                    </div>
                  </div>
                  <div class="chat-details-middle wrap-ellipsis">
                    <!-- Handle form and document items -->
                    <div
                      *ngIf="card.type === 'form' || card.type === 'downloadForm' || card.type === 'document' || card.type === 'downloadDocument'"
                      class="inbox-message-from wrap-ellipsis"
                    >
                      <div class="from-name wrap-ellipsis">
                        <div class="inbox-per-name wrap-ellipsis">{{ item.name }}</div>
                      </div>
                    </div>
                    <!-- Handle message items -->
                    <div *ngIf="card.type === 'message'" class="inbox-message-from wrap-ellipsis">
                      <div class="from-name wrap-ellipsis">
                        <div class="inbox-per-name wrap-ellipsis" *ngIf="item.chatHeading">{{ item.chatHeading }}</div>
                      </div>
                    </div>
                    <!-- Subheading: patient name for forms, chat subheading for messages -->
                    <!-- Message content for unread messages -->
                    <div *ngIf="card.type === 'message'; else elseBlock" class="message-preview">
                      <div class="message-preview-content">
                        <div
                          class="inbox-mes-name wrap-ellipsis"
                          *ngIf="item.chatSubject && (+item.messageType === +messageTypes.messageGroup || +item.messageType === +messageTypes.pdg)"
                        >
                          {{ 'LABELS.SUBJECT' | translate }}: {{ item.chatSubject }}
                        </div>
                        <!-- Self message indicator with proper spacing -->
                        
                        
                        <span *ngIf="+item.messageType !== +messageTypes.pdg" class="message-body compact">
                          <ion-icon name="chatbubble-outline" class="message-icon" size="small" aria-hidden="true"></ion-icon>
                          <span
                            [dirParseMessageToHtml]="item.message | unicodeConvert"
                            class="preview-text truncate-message"
                            [ngClass]="{ 'pending-status': +item.messageStatus === 0 }">
                          </span>
                        </span>
                      </div>
                    </div>
                    <ng-template #elseBlock>
                      <div *ngIf="item.initiatorName" class="inbox-mes-name wrap-ellipsis">{{ item.initiatorName }}</div>
                    </ng-template>
                    
                  </div>

                  <div class="date-block">
                    <div class="message-date-container">
                      <div class="message-date">
                        <ng-container *ngIf="card.type === 'message'">
                          {{ (+item.messageStatus === 1 ? item.messageOrder * 1000 : item.messageDeletedTime) | shortDateTime }}
                        </ng-container>
                        <ng-container *ngIf="card.type !== 'message'">
                          {{ (item.sentDate < 10000000000 ? item.sentDate * 1000 : item.sentDate) | shortDateTime }}
                        </ng-container>
                      </div>
                    </div>
                    
                    <!-- Download button for both completed documents and forms -->
                    <button class="download-icon" 
                            *ngIf="card.type === 'downloadDocument'" 
                            (click)="downloadDocument($event, item)" 
                            (keydown.enter)="downloadDocument($event, item)"
                            tabindex="0"
                            [attr.aria-label]="'LABELS.DOWNLOAD_DOCUMENT' | translate">
                      <ion-icon name="download-outline" color="success"></ion-icon>
                    </button>
                    <!-- For download forms card -->
                    <button class="download-icon" 
                            *ngIf="card.type === 'downloadForm'" 
                            (click)="downloadForm($event, item)" 
                            (keydown.enter)="downloadForm($event, item)"
                            tabindex="0"
                            [attr.aria-label]="'BUTTONS.DOWNLOAD_FORMS' | translate">
                      <ion-icon name="download-outline" color="success"></ion-icon>
                    </button>
                  </div>
                </div>
              </ion-item>

              <!-- No data state -->
              <ion-item *ngIf="card.data && card.data.length === 0" class="flex ios no-data">
                <div class="item-details">
                  <div class="item-header">
                    <span class="item-name">{{ 'MESSAGES.NO_DATA_FOUND' | translate }}</span>
                  </div>
                </div>
              </ion-item>
            </ion-list>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>