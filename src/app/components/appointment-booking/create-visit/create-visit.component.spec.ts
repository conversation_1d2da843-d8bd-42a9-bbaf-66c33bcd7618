import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IonicModule, ToastController } from '@ionic/angular';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common-service/common.service';
import { of } from 'rxjs';
import { IonModal } from '@ionic/angular/common';
import { CreateVisitComponent } from './create-visit.component';
import { SharedService } from '../../../services/shared-service/shared.service';
import { ApiService } from '../../../services/api-service/api.service';

describe('CreateVisitComponent', () => {
  let component: CreateVisitComponent;
  let fixture: ComponentFixture<CreateVisitComponent>;
  let commonService: jasmine.SpyObj<CommonService>;
  let apiService: jasmine.SpyObj<ApiService>;
  let sharedService: jasmine.SpyObj<SharedService>;
  let toastController: jasmine.SpyObj<ToastController>;
  let router: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['showToast', 'getTranslateData']);
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['getSlotsApi', 'getAvailability']);
    const sharedServiceSpy = jasmine.createSpyObj('SharedService', ['setCalendarPickerMaxYear', 'redirectPage'], { isLoading: false });
    const toastControllerSpy = jasmine.createSpyObj('ToastController', ['create']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const modalSpy = jasmine.createSpyObj('IonModal', ['present']);

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, FormsModule, TranslateModule.forRoot(), IonicModule.forRoot(), CreateVisitComponent],
      providers: [
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: SharedService, useValue: sharedServiceSpy },
        { provide: ToastController, useValue: toastControllerSpy },
        { provide: Router, useValue: routerSpy },
        { provide: IonModal, useValue: modalSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CreateVisitComponent);
    component = fixture.componentInstance;
    commonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    sharedService = TestBed.inject(SharedService) as jasmine.SpyObj<SharedService>;
    toastController = TestBed.inject(ToastController) as jasmine.SpyObj<ToastController>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    apiService.getSlotsApi.and.returnValue(of({}));
  });

  beforeEach(() => {
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call redirectPage if patientInfo is blank', () => {
    spyOn(sessionStorage, 'getItem').and.callFake((key: string) => {
      if (key === 'patientInfo') {
        return null;
      }
      if (key === 'timeZoneInfo') {
        return JSON.stringify({ timeZone: 'America/New_York' });
      }
      return null;
    });
    component.ngOnInit();
    expect(sharedService.redirectPage).toHaveBeenCalledWith('appointment-booking');
  });

  it('should set timeZoneCity and timeZoneName correctly', () => {
    spyOn(sessionStorage, 'getItem').and.callFake((key: string) => {
      if (key === 'patientInfo') {
        return 'someValue';
      }
      if (key === 'timeZoneInfo') {
        return JSON.stringify({ city: 'America/New_York', name: 'Eastern Time' });
      }
      return null;
    });
    component.ngOnInit();
    expect(component.timeZoneCity).toEqual('America/New_York');
    expect(component.timeZoneName).toEqual('Eastern Time');
  });

  it('should set formattedToday on visitDate form control', () => {
    const today = new Date();
    today.setDate(today.getDate() + 1);
    const formattedToday = today.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    spyOn(sessionStorage, 'getItem').and.callFake((key: string) => {
      if (key === 'patientInfo') {
        return 'someValue';
      }
      if (key === 'timeZoneInfo') {
        return JSON.stringify({ timeZone: 'America/New_York' });
      }
      return null;
    });
    component.ngOnInit();
    expect(component.visitForm.get('visitDate')?.value).toEqual(formattedToday);
  });

  it('should call getSlots with today date', () => {
    const today = new Date();
    today.setDate(today.getDate() + 1);
    spyOn(sessionStorage, 'getItem').and.callFake((key: string) => {
      if (key === 'patientInfo') {
        return 'someValue';
      }
      if (key === 'timeZoneInfo') {
        return JSON.stringify({ timeZone: 'America/New_York' });
      }
      return null;
    });
    spyOn(component, 'getSlots');
    component.ngOnInit();
    expect(component.getSlots).toHaveBeenCalledWith(jasmine.any(Date));
  });

  it('should have visitDate element in the DOM', () => {
    const fixture = TestBed.createComponent(CreateVisitComponent);
    fixture.detectChanges();
    const visitDateElement = fixture.nativeElement.querySelector('#visitDate');
    expect(visitDateElement).toBeTruthy();
  });

  it('should set maxYear', () => {
    const maxYear = '2024';
    spyOn(sessionStorage, 'getItem').and.callFake((key: string) => {
      if (key === 'patientInfo') {
        return 'someValue';
      }
      if (key === 'timeZoneInfo') {
        return JSON.stringify({ timeZone: 'America/New_York' });
      }
      return null;
    });
    sharedService.setCalendarPickerMaxYear.and.returnValue(maxYear);
    component.ngOnInit();
    expect(component.maxYear).toBe(maxYear);
  });

  it('should set timeZoneCity and timeZoneName from sessionStorage', () => {
    const timeZoneInfo = JSON.stringify({ city: 'Kolkata', name: 'Asia/Kolkata' });
    spyOn(sessionStorage, 'getItem').and.callFake((key: string) => {
      if (key === 'timeZoneInfo') {
        return timeZoneInfo;
      }
      if (key === 'patientInfo') {
        return 'someValue';
      }
      return null;
    });
    component.ngOnInit();
    expect(component.timeZoneCity).toEqual('Kolkata');
    expect(component.timeZoneName).toEqual('Asia/Kolkata');
  });

  it('should create a form group with visitDate control', () => {
    const formGroup = component.createVisitForm();
    expect(formGroup).toBeTruthy();
    expect(formGroup.contains('visitDate')).toBeTrue();
    expect(formGroup.get('visitDate')?.value).toBe('');
  });

  it('should update visitDate control with formatted date from event', () => {
    const testDate = new Date('2024-08-15T00:00:00Z');
    const event = { detail: { value: testDate.toISOString() } };
    const formattedDate = testDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    spyOn(component, 'calculateNextDates').and.returnValue([]);
    spyOn(component, 'getSlots');
    component.getSelectedDate(event);
    expect(component.visitForm.get('visitDate')?.value).toEqual(formattedDate);
    expect(component.calculateNextDates).toHaveBeenCalledWith(testDate);
    expect(component.getSlots).toHaveBeenCalledWith(testDate);
  });

  it('should update visitDate control with formatted date from startDay when no event is provided', () => {
    const testStartDay = new Date();
    component.startDay = testStartDay;
    const formattedDate = testStartDay.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    spyOn(component, 'calculateNextDates').and.returnValue([]);
    spyOn(component, 'getSlots');
    component.getSelectedDate();
    expect(component.visitForm.get('visitDate')?.value).toEqual(formattedDate);
    expect(component.calculateNextDates).toHaveBeenCalledWith(testStartDay);
    expect(component.getSlots).toHaveBeenCalledWith(testStartDay);
  });

  it('should calculate the next three dates correctly', () => {
    const startDate = new Date('2024-08-15T00:00:00Z');
    const expectedDates = [
      {
        day: 'THU',
        date: new Date('2024-08-15T00:00:00Z'),
        active: true
      },
      {
        day: 'FRI',
        date: new Date('2024-08-16T00:00:00Z'),
        active: false
      },
      {
        day: 'SAT',
        date: new Date('2024-08-17T00:00:00Z'),
        active: false
      }
    ];

    const result = component.calculateNextDates(startDate);
    expect(result.length).toBe(3);
    result.forEach((item, index) => {
      expect(item.date).toEqual(expectedDates[index].date);
      expect(item.day).toBe(expectedDates[index].day);
      expect(item.active).toBe(expectedDates[index].active);
    });
  });

  it('should update selectedCard and form control, and call getSlots with the selected date', () => {
    const startDate = new Date('2024-08-15T00:00:00Z');
    const displayedDates = component.calculateNextDates(startDate);
    component.displayedDates = displayedDates;
    const dateToSelect = displayedDates[1];
    const selectedIndex = 1;
    const formattedDate = new Date(dateToSelect.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    spyOn(component, 'getSlots');
    component.selectDate(dateToSelect, selectedIndex);
    expect(component.selectedCard).toBe(selectedIndex);
    expect(component.displayedDates.filter((d) => !d.active && d.slots === null).length).toBe(component.displayedDates.length - 1);
    expect(component.displayedDates[selectedIndex].active).toBeTrue();
    expect(component.visitForm.get('visitDate')?.value).toBe(formattedDate);
    expect(component.getSlots).toHaveBeenCalledWith(new Date(dateToSelect.date));
  });

  it('should call getSlotsApi with formatted date', () => {
    const dateInput = new Date();
    const formattedDate = dateInput.toISOString().slice(0, 10);
    apiService.getSlotsApi.and.returnValue(of({ status: 'SUCCESS', data: [] }));
    component.getSlots(dateInput);
    expect(apiService.getSlotsApi).toHaveBeenCalledWith(formattedDate);
  });

  it('should set isLoading to false and display slots on successful API response', () => {
    const dateInput = new Date();
    const slotsData = [{ slotId: 1 }, { slotId: 2 }];
    apiService.getSlotsApi.and.returnValue(of({ status: 'SUCCESS', data: slotsData }));
    spyOn(component, 'displaySlots');
    component.getSlots(dateInput);
    expect(sharedService.isLoading).toBeFalse();
    expect(component.displaySlots).toHaveBeenCalledWith(slotsData);
  });

  it('should call modal.present() when summary() is called and modal is defined', () => {
    const modalSpy = jasmine.createSpyObj('IonModal', ['present']);
    component.modal = modalSpy;
    component.summary();
    expect(modalSpy.present).toHaveBeenCalled();
  });

  it('should not call modal.present() when summary is called and modal is undefined', () => {
    component.modal = undefined;
    const modalSpy = jasmine.createSpyObj('IonModal', ['present']);
    component.summary();
    expect(modalSpy.present).not.toHaveBeenCalled();
  });

  it('should call commonService.showToast with the correct arguments', () => {
    const translatedMessage = 'Slot is unavailable';
    commonService.getTranslateData.and.returnValue(translatedMessage);
    component.bookedSlotMessageToast();
    expect(commonService.showToast).toHaveBeenCalledWith({
      message: translatedMessage,
      color: 'danger',
      position: 'middle',
      duration: 4000,
      cssClass: 'barlow-regular'
    });
    expect(commonService.getTranslateData).toHaveBeenCalledWith('MESSAGES.SLOT_UNAVAILABLE');
  });

  it('should format and filter the slots correctly', () => {
    const data = [
      { startTime: '08:00', endTime: '09:00', availability: true },
      { startTime: '14:00', endTime: '15:00', availability: false }
    ];
    const selectedCard = 0;
    component.selectedCard = selectedCard;
    component.displayedDates = [{ slots: false }];
    component.selectedDate = '2024-08-13';
    component.startDay = new Date('2024-08-12');
    component.displaySlots(data);
    expect(component.displayedDates[selectedCard].slots).toBeTrue();
    expect(component.availability.length).toBe(2);
    expect(component.availability[0]).toEqual(
      jasmine.objectContaining({
        from: '08:00 AM',
        to: '09:00 AM',
        availability: true,
        isAvailable: true
      })
    );
    expect(component.availability[1]).toEqual(
      jasmine.objectContaining({
        from: '02:00 PM',
        to: '03:00 PM',
        availability: false,
        isAvailable: false
      })
    );
  });

  it('should sort the slots correctly', () => {
    const data = [
      { startTime: '14:00', endTime: '15:00', availability: true },
      { startTime: '08:00', endTime: '09:00', availability: true }
    ];
    component.displaySlots(data);
    expect(component.availability[0].from).toBe('08:00 AM');
    expect(component.availability[1].from).toBe('02:00 PM');
  });

  it('should set isLoading to false after processing slots', () => {
    const data = [{ startTime: '08:00', endTime: '09:00', availability: true }];
    component.displaySlots(data);
    expect(sharedService.isLoading).toBeFalse();
  });

  it('should set noSlots to false if slots are available', () => {
    const data = [{ startTime: '08:00', endTime: '09:00', availability: true }];
    component.displaySlots(data);
    expect(component.noSlots).toBeFalse();
    expect(component.availability.length).toBe(1);
  });

  it('should navigate to visit-summary on SUCCESS response', () => {
    const slot = { startTime: '08:00', endTime: '09:00', date: '2024-08-13' };
    apiService.getAvailability.and.returnValue(of({ status: 'SUCCESS' }));
    component.checkAvailabiltyTime(slot, 0);
    expect(router.navigate).toHaveBeenCalledWith(['appointment-booking/visit-summary'], {
      state: {
        slot
      }
    });
  });

  it('should mark slot as unavailable and show toast on FAILURE response', () => {
    const slot = { startTime: '08:00', endTime: '09:00', date: '2024-08-13' };
    const index = 0;
    component.availability = [{ isAvailable: true }];
    commonService.getTranslateData.and.returnValue('Slot has been booked');
    apiService.getAvailability.and.returnValue(of({ status: 'FAILURE' }));
    component.checkAvailabiltyTime(slot, index);
    expect(component.availability[index].isAvailable).toBeFalse();
    expect(commonService.showToast).toHaveBeenCalledWith({
      message: 'Slot has been booked',
      color: 'danger',
      duration: 5000,
      position: 'middle',
      cssClass: 'barlow-regular'
    });
  });
});
