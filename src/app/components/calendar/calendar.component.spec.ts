import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { AlertController, IonicModule } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { CalendarComponent } from './calendar.component';
import { CommonService } from 'src/app/services/common-service/common.service';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import { of } from 'rxjs';

describe('CalendarComponent', () => {
  let component: CalendarComponent;
  let fixture: ComponentFixture<CalendarComponent>;
  let service: SharedService;
  let alertController: AlertController;
  const { alertSpy } = TestConstants;
  let common: CommonService;
  let visitScheduleService: VisitScheduleService;
  const mockItem = [
    {
      color: '#a84c4c',
      end: '2022-06-28 13:00:00',
      end_date: '2022-06-28 13:00:00',
      eventId: '3667',
      eventVisitTitle: 'Title',
      id: '3667',
      myVisitFor: 'visitScheduler',
      patientName: 'QA2 Patient',
      patientVisitStatus: 'Not Confirmed',
      staffVisitStatus: 'Pending Staff/Partner Assignment',
      start: '2022-06-28 12:00:00',
      start_date: '2022-06-28 12:00:00',
      title: 'Title',
      visitId: '1135',
      visitTitle: 'Title'
    }
  ];
  let mockEvent: any = {
    backgroundColor: '#a84c4c',
    el: {
      style: {
        backgroundColor: '',
        display: '',
        flexWrap: '',
        overflow: '',
        padding: '',
        color: '',
        zIndex: ''
      }
    },
    view: {
      type: '',
      currentStart: '2022-06-28 12:00:00',
      currentEnd: '2022-06-29 12:00:00'
    }
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [CalendarComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), HttpClientModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        Idle,
        IdleExpiry,
        Keepalive
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    service = TestBed.inject(SharedService);
    common = TestBed.inject(CommonService);
    visitScheduleService = TestBed.inject(VisitScheduleService);
    service.userData = {
      ...service.userData,
      mySites: [{ id: 1, name: 'Atlanta' }]
    };
    service.userData.siteConfigs = {
      ...service.userData.siteConfigs,
      timezoneName: 'Eastern time'
    };
    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    spyOn(alertController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(CalendarComponent);
    component = fixture.componentInstance;
    spyOn(component, 'ngOnChanges').and.callThrough();
    fixture.detectChanges();
    component.item = mockItem;
    component.ngOnChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('eventDidMount should be defined', () => {
    component.eventDidMount(mockEvent);
    expect(component.eventDidMount).toBeDefined();
  });

  it('eventMouseHover should be defined', () => {
    component.eventMouseHover(mockEvent);
    expect(component.eventMouseHover).toBeDefined();
  });

  it('eventMouseHover should check inlineFlex case', () => {
    component.flex = '';
    component.eventMouseHover(mockEvent);
    expect(component.eventMouseHover).toBeDefined();
  });

  it('eventClickFunction should be defined', () => {
    const objParam = {
      id: '3742',
      start: '2022-06-30T19:30:00+05:30',
      title: 'Test Event',
      backgroundColor: '#a84c4c',
      borderColor: '#a84c4c',
      end: '2022-06-30T20:30:00+05:30',
      extendedProps: {
        end_date: '2022-06-30 20:30:00',
        eventId: '3742',
        eventVisitTitle: 'Test event',
        start_date: '2022-06-30 19:30:00',
        tasks: 'Visit Task',
        visitId: '1206',
        visitTitle: 'Test'
      },
      _instance: {
        view: {
          end: '2022-06-30 19:30:00',
          start: '2022-06-30 20:30:00'
        }
      }
    };
    const args: any = {
      event: {
        ...objParam,
        toJSON: () => {
          return objParam;
        }
      }
    };
    component.eventClickFunction(args);
    expect(component.eventClickFunction).toBeDefined();
  });

  it('datesSetup should be defined', () => {
    component.datesSetup(mockEvent);
    expect(component.datesSetup).toBeDefined();
  });

  it('datesSetup should be defined with view type', () => {
    mockEvent.view.type = 'timeGridWeek';
    component.datesSetup(mockEvent);
    expect(component.datesSetup).toBeDefined();
    expect(component.currentCalendarType).toBe('timeGridWeek');
  });
  describe('setDefaultView', () => {
    it('setDefaultView should be defined timeGridWeek', () => {
      component.currentCalendarType = 'timeGridWeek';
      spyOn(common, 'showAlert').and.resolveTo(false);
      component.setDefaultView();
      expect(component.setDefaultView).toBeDefined();
    });

    it('setDefaultView should be defined dayGridMonth', () => {
      component.currentCalendarType = 'dayGridMonth';
      spyOn(common, 'showAlert').and.resolveTo(true);
      spyOn(visitScheduleService, 'setDefaultView').and.throwError('');
      component.setDefaultView();
      expect(component.setDefaultView).toBeDefined();
    });

    it('setDefaultView should be defined timeGridDay', () => {
      component.currentCalendarType = 'timeGridDay';

      spyOn(common, 'showAlert').and.resolveTo(true);
      const response = {
        success: true
      };
      component.setDefaultView();
      spyOn(visitScheduleService, 'setDefaultView').and.returnValue(of(response));
      visitScheduleService.setDefaultView('timeGridDay').subscribe();
      expect(component.setDefaultView).toBeDefined();
    });

    it('setDefaultView should be defined month', () => {
      const response = {
        success: false
      };
      spyOn(visitScheduleService, 'setDefaultView').and.returnValue(of(response));
      spyOn(common, 'showMessage').and.stub();
      component.setDefaultView();
      expect(component.setDefaultView).toBeDefined();
    });
  });
  it('loading should be defined', () => {
    component.loading('', '');
    expect(component.loading).toBeDefined();
  });
  it('eventDidMount should be defined', () => {
    mockEvent = {
      ...mockEvent,
      isEnd: true,
      isStart: true
    };
    component.eventDidMount(mockEvent);
    expect(component.eventDidMount).toBeDefined();
  });
  it('should call ngOnInit with view visit page', () => {
    component.visitPage = 'view_visit';
    component.ngOnInit();
    expect(component.ngOnInit).toBeDefined();
  });

  it('should call ngOnInit with tenant time zone', () => {
    component.tenantTimezone = 'Asia/Kolkata';
    component.ngOnInit();
    expect(component.ngOnInit).toBeDefined();
  });
  describe('ngOnInit', () => {
    it('should call ngOnInit with visit default week view', () => {
      component.visitPage = 'view_visit';
      service.userData.userVisitDefaultView = 'week';
      component.ngOnInit();
      expect(component.ngOnInit).toBeDefined();
    });
    it('should call ngOnInit with visit default month view', () => {
      component.visitPage = 'view_visit';
      service.userData.userVisitDefaultView = 'month';
      component.ngOnInit();
      expect(component.ngOnInit).toBeDefined();
    });
    it('should call ngOnInit with visit default timeGridDay view', () => {
      component.visitPage = 'view_visit';
      service.userData.userVisitDefaultView = 'timeGridDay';
      component.ngOnInit();
      expect(component.ngOnInit).toBeDefined();
    });
  });
});
