import { Component, OnInit, Input, <PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { getComputedStyleProperty, isBlank } from 'src/app/utils/utils';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Activity } from 'src/app/constants/activity';
import Elevio from 'elevio/lib/client';
import { Config } from 'src/app/constants/config';
import { Constants, UserGroup } from 'src/app/constants/constants';
import { SocketPopoverComponent } from '../socket-popover/socket-popover.component';
import { PopoverController } from '@ionic/angular';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { isVirtual } from 'src/app/utils/storage-utils';
import { PageRoutes } from 'src/app/constants/page-routes';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit {
  @Input() headerTitle: string;
  @Input() subHead: string;
  @Input() headerImage = false;
  @Input() hideSocketButton = false;
  @Input() hideHelpButton = false;
  showHelpButton = false;
  showSocketButton = false;
  scrollTitle = false;
  socketStatus = true;
  helpCenterStatus = false;
  currentPopover = null;
  isVirtual = false;
  showTutorialButton = false;
  tutorialLink = '';
  @Input() closeSocketPopover;
  @ViewChild('titleClick') titleInput: ElementRef;
  constructor(
    public sharedService: SharedService,
    private socketService: SocketService,
    private popoverController: PopoverController,
    private readonly ngZone: NgZone
  ) {
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.showHelpButton = this.showElevio;
      this.showTutorialButton = this.showTutorial;
      this.tutorialLink = this.sharedService.getConfigValue(Config.talentlmsUrl);
    });
  }

  ngOnInit(): void {
    this.isVirtual = isVirtual();
    this.sharedService.pageAccess(this.subHead ? this.subHead : this.headerTitle);
    this.showHelpButton = this.showElevio;
    this.showTutorialButton = this.showTutorial;
    this.tutorialLink = this.sharedService.getConfigValue(Config.talentlmsUrl);
    this.showSocketButton =
      (!this.isVirtual || location.href.includes(PageRoutes.applessURLPathMessage)) && !this.hideSocketButton;
    this.socketStatus = this.socketService.status;
    this.socketService.socketStatusUpdated.subscribe(() => {
      this.socketStatus = this.socketService.status;
    });
    if (this.showElevio) {
      this.initializeElevio();
    }
  }
  get showTutorial(): boolean {
    const isOktaEnabled = localStorage.getItem(Constants.storageKeys.oktaLogin) && this.sharedService.isEnableConfig(Config.enableIDM);
    const isSSOEnabled = sessionStorage.getItem('isSSOLogin') === 'true';
    return this.sharedService.isEnableConfig(Config.showTalentlmsTutorial) &&
     +this.sharedService?.userData.group !== UserGroup.PATIENT &&
      (isOktaEnabled || isSSOEnabled);
  }
  get showElevio(): boolean {
    return this.sharedService.isEnableConfig(Config.showElevio) && !this.isVirtual && !this.hideHelpButton;
  }
  get checkPath(): boolean {
    const pathCheck = ['/home', '/message-center/messages/active'].includes(location.pathname);
    if (pathCheck) {
      this.sharedService.hideMessageCount = true;
    }
    return !pathCheck;
  }
  initializeElevio(): void {
    let configGroups = [];
    const appType = this.sharedService.platformValue === Constants.platform.web ? Constants.platform.desktop : Constants.platform.mobile;
    let groups = [Constants.platformUser, `${Constants.appType}:${appType}`];
    const isPatient = this.sharedService.userData?.group;
    if (this.sharedService.userData?.config && this.sharedService.isEnableConfig(Config.showElevio)) {
      if (Number(isPatient) === Constants.patientGroupId) {
        groups.push(`${Constants.userTypeKey}:${Constants.inviteTab.patient}`);
        if (!isBlank(this.sharedService.getConfigValue(Config.elevioPatient))) {
          configGroups = this.sharedService.getConfigValue(Config.elevioPatient).split(',');
        }
      }
      if (Number(isPatient) !== Constants.patientGroupId && Number(isPatient) !== Constants.partnerGroupId) {
        groups.push(`${Constants.userTypeKey}:${Constants.inviteTab.staff}`);
        if (!isBlank(this.sharedService.getConfigValue(Config.elevioStaff))) {
          configGroups = this.sharedService.getConfigValue(Config.elevioStaff).split(',');
        }
      }
      if (Number(isPatient) === Constants.partnerGroupId) {
        groups.push(`${Constants.userTypeKey}:${Constants.inviteTab.partner}`);
        if (!isBlank(this.sharedService.getConfigValue(Config.elevioPartner))) {
          configGroups = this.sharedService.getConfigValue(Config.elevioPartner).split(',');
        }
      }
      groups = groups.concat(configGroups);
      groups = groups.filter((item, pos) => groups.indexOf(item) === pos);
    }

    // all CSS styles
    const mainColor = getComputedStyleProperty('--ion-color-elvio-header');
    const userIdE = `${this.sharedService.userData?.userId} - ${this.sharedService.userData?.tenantName}`;
    this.ngZone.runOutsideAngular(() => {
      this.ngZone.run(() => {
        Elevio.load(Constants.elevioKey).then(() => {
          Elevio.on('load', (elev) => {
            elev.setSettings({
              hideLauncher: true,
              disableDevelopmentWarnings: true,
              main_color: mainColor
            });
            elev.setUser({
              first_name: userIdE,
              groups
            });
          });
        });
      });
    });
  }
  enableHelpCenter(): void {
    this.helpCenterStatus = true;
    this.ngZone.runOutsideAngular(() => {
      this.ngZone.run(() => {
        Elevio.enable();
        (window as any)._elev?.open();
      });
    });
    this.sharedService.trackActivity({
      type: Activity.openHelpCenter,
      name: Activity.helpCenter,
      des: {
        data: {
          firstName: this.sharedService.userData.firstName,
          secondName: this.sharedService.userData.secondName,
          userName: this.sharedService.userData.userName
        },
        desConstant: Activity.helpCenterDes
      }
    });
  }
  checkTitleScroll(): void {
    this.scrollTitle = this.titleInput.nativeElement.offsetWidth < this.titleInput.nativeElement.scrollWidth;
  }
  async socketPopover(ev?: any): Promise<any> {
    if (this.helpCenterStatus) {
      (window as any)._elev.close();
      this.helpCenterStatus = false;
    }
    this.socketService.isConnected();
    this.socketStatus = this.socketService.status;
    const popover = await this.popoverController.create({
      id: 'socket-status',
      component: SocketPopoverComponent,
      event: ev,
      cssClass: 'event-popover',
      componentProps: {},
      showBackdrop: true,
      translucent: false,
      backdropDismiss: false,
      mode: 'ios'
    });
    this.currentPopover = popover;
    await popover.present();
  }
}
