import { TranslateModule } from '@ngx-translate/core';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, PopoverController } from '@ionic/angular';
import { HeaderComponent } from './header.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';
import Elevio from 'elevio/lib/client';
import { fakeAsync, tick } from '@angular/core/testing';
import { Config } from 'src/app/constants/config';
import { Constants, UserGroup } from 'src/app/constants/constants';

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;
  let sharedService: SharedService;
  let popoverController: PopoverController;
  const { popoverSpy } = TestConstants;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [HeaderComponent],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        SharedService,
        SocketService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;

    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popoverSpy;
    });
    spyOn(popoverController, 'dismiss').and.stub();
    popoverSpy.present.and.stub();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return false and set hideMessageCount to true if pathCheck is true', () => {
    const result = component.checkPath;
    component.sharedService.hideMessageCount = true;
    expect(result).toBe(true);
  });

  it('should return true if pathCheck is false', () => {
    component['pathCheck'] = true;
    const result = component.checkPath;

    expect(result).toBe(true);
    expect(sharedService.hideMessageCount).toBeDefined();
  });

  it('should set scrollTitle to false if the title does not overflow', () => {
    component.checkTitleScroll();
    expect(component.scrollTitle).toBe(false);
  });

  it('should call socketPopover', () => {
    component.socketPopover();
    expect(component.socketPopover).toBeDefined();
  });

  it('should set showTutorialButton and tutorialLink on configValuesUpdated', () => {
    sharedService.configValuesUpdated.next(true);
    expect(component.showTutorialButton).toBe(component.showTutorial);
    expect(component.tutorialLink).toBe(sharedService.getConfigValue(TestConstants.talentlmsUrl));
  });

  it('should initialize Elevio if showElevio is true', () => {
    spyOn(component, 'initializeElevio');
    spyOnProperty(component, 'showElevio', 'get').and.returnValue(true);
    component.ngOnInit();
    expect(component.initializeElevio).toHaveBeenCalled();
  });

  it('should not initialize Elevio if showElevio is false', () => {
    spyOn(component, 'initializeElevio');
    spyOnProperty(component, 'showElevio', 'get').and.returnValue(false);
    component.ngOnInit();
    expect(component.initializeElevio).not.toHaveBeenCalled();
  });
  
  it('should set scrollTitle to true if the title overflows', () => {
    const titleInputElement = component.titleInput.nativeElement;
    spyOnProperty(titleInputElement, 'offsetWidth', 'get').and.returnValue(100);
    spyOnProperty(titleInputElement, 'scrollWidth', 'get').and.returnValue(200);
    component.checkTitleScroll();
    expect(component.scrollTitle).toBe(true);
  });

  it('should handle tutorial button click', () => {
    const originalWindowOpen = window.open;
    spyOn(window, 'open').and.callFake(() => window);
    component.showTutorialButton = true;
    component.tutorialLink = 'https://example.com/tutorial';
    fixture.detectChanges(); // Ensure the DOM is updated

    const tutorialButton = fixture.debugElement.nativeElement.querySelector('#tutorial-icon');
    expect(tutorialButton).toBeTruthy(); // Ensure the button exists

    tutorialButton.click();
    fixture.whenStable().then(() => {
      expect(window.open).toHaveBeenCalledWith('https://example.com/tutorial', '_blank');
      window.open = originalWindowOpen; // Restore original function
    });
  });

  describe('showTutorial getter', () => {
    beforeEach(() => {
      localStorage.clear();
      sessionStorage.clear();
    });

    afterEach(() => {
      localStorage.clear();
      sessionStorage.clear();
    });

    it('should return true when all conditions are met with OKTA login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBe(true);
    });

    it('should return true when all conditions are met with SSO login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');
      expect(component.showTutorial).toBe(true);
    });

    it('should return true when both OKTA and SSO are enabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      sessionStorage.setItem('isSSOLogin', 'true');
      expect(component.showTutorial).toBe(true);
    });

    it('should return false when TalentLMS tutorial is disabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return false;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBe(false);
    });

    it('should return false when user is a patient', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PATIENT.toString() },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBe(false);
    });

    it('should return false when neither OKTA nor SSO login is present', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      expect(component.showTutorial).toBeFalsy();
    });

    it('should return false when OKTA login is empty but IDM is disabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBeFalsy();
    });

    it('should return true when OKTA login is empty but SSO is enabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return false when IDM authentication is disabled and no SSO', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(component.showTutorial).toBe(false);
    });

    it('should throw error when userData is undefined', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: undefined,
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(() => component.showTutorial).toThrowError();
    });

    it('should return false when multiple conditions are not met', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return false;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PATIENT.toString() },
        writable: true,
        configurable: true
      });

      expect(component.showTutorial).toBe(false);
    });

    it('should return true for partner user with OKTA login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PARTNER.toString() },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return true for partner user with SSO login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PARTNER.toString() },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return true for staff user (group 1) with OKTA login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '1' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return true for staff user (group 1) with SSO login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '1' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return false when SSO login is not true', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'false');

      expect(component.showTutorial).toBe(false);
    });
  });
});
