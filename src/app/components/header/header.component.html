<ion-header class="common-header" mode="md">
    <ion-toolbar>
        <ion-buttons slot="start" id="header-menu-buttons" class="header-menu" *ngIf="sharedService?.userData && !sharedService.disableSideMenu && !sharedService.isAppLessHomeLoggedIn()">
            <ion-menu-button id="header-menu-button"> </ion-menu-button>
            <span *ngIf="sharedService.messageCount && !sharedService.hideMessageCount && checkPath && !isVirtual" class="hp-count">
                {{ sharedService.messageCount }}
            </span>
        </ion-buttons>
        <ion-title class="header-ion-title" (click)="checkTitleScroll()">
            <div [ngClass]="{'pull':scrollTitle}">
                <h1 #titleClick class="header-title" *ngIf="!headerImage" [innerHTML]="headerTitle | translate"></h1>
            </div>
            <ion-img *ngIf="headerImage" [src]="headerTitle" class="header-image" alt="..."></ion-img>
        </ion-title>

        <ion-buttons slot="end">
            <ion-button (click)="socketPopover()" id="socket-icon" class="socket-icon" tappable
                [class.ion-padding-end]="!showHelpButton" *ngIf="showSocketButton">
                <ion-img [ngClass]="{'disconnect':!socketStatus}" src="/assets/images/connection-icon.png" alt="...">
                </ion-img>
            </ion-button>
            <ion-button id="help-icon" class="help-icon" tappable (click)="enableHelpCenter()" *ngIf="showHelpButton">
                <ion-icon name="help-circle"></ion-icon>
            </ion-button>
            <ion-button *ngIf="showTutorialButton" id="tutorial-icon" class="help-icon" [href]="tutorialLink" target="_blank">
                <ion-icon name="book"></ion-icon>
            </ion-button>
        </ion-buttons>
    </ion-toolbar>
</ion-header>