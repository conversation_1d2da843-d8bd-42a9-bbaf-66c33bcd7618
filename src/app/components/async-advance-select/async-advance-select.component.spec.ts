import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IonicModule, ModalController } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { of } from 'rxjs';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import { AsyncAdvanceSelectComponent } from './async-advance-select.component';

describe('AsyncAdvanceSelectComponent', () => {
  let component: AsyncAdvanceSelectComponent;
  let fixture: ComponentFixture<AsyncAdvanceSelectComponent>;
  let scheduleCenterService: VisitScheduleService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [AsyncAdvanceSelectComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), HttpClientModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [
        VisitScheduleService,
        ModalController,
        NgxPermissionsService,
        NgxPermissionsStore,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        Idle,
        IdleExpiry,
        Keepalive,
        FormBuilder
      ]
    }).compileComponents();
    scheduleCenterService = TestBed.inject(VisitScheduleService);
    fixture = TestBed.createComponent(AsyncAdvanceSelectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    component.loadDataFunction = scheduleCenterService.searchVisitLocations;
    expect(component).toBeTruthy();
  });

  it('loadAll should be defined', () => {
    const mockResp = {
      data: [
        {
          id: 1,
          name: 'Ben'
        }
      ]
    };
    component.loadDataFunction = scheduleCenterService.searchVisitLocations;
    spyOn(scheduleCenterService, 'searchVisitLocations').and.returnValue(of(mockResp));
    component.loadAllItems();
    expect(component.loadAllItems).toBeDefined();
  });
  it('loadall should be defined and append with new response data with old record', () => {
    const mockResp = {
      data: [
        {
          id: 2,
          name: 'Kiran'
        }
      ]
    };
    component.searchParams = {
      currentPage: 2
    };
    component.loadDataFunction = scheduleCenterService.searchVisitLocations;
    spyOn(scheduleCenterService, 'searchVisitLocations').and.returnValue(of(mockResp));
    component.loadAllItems();
    expect(component.loadAllItems).toBeDefined();
  });

  it('loadItemsOnSearch should be defined', () => {
    component.loadItemsOnSearch();
    expect(component.loadItemsOnSearch).toBeDefined();
  });

  it('should call loadMoreItems function', () => {
    component.loadData(undefined);
    expect(component.loadData).toBeTruthy();
  });

  it('should call onCancel function', () => {
    component.onCancel();
    expect(component.onCancel).toBeTruthy();
  });

  it('should call dismiss function', () => {
    component.dismiss();
    expect(component.dismiss).toBeTruthy();
  });
  it('should call dismiss function and should check the item is selected or not', () => {
    component.dismiss();
    expect(component.dismiss).toBeTruthy();
  });

  it('should call select Item', () => {
    const data = {
      id: '1',
      locationName: 'Roy',
      dob: '12-01-2023'
    };
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    component.selectItem(data as any);
    expect(component.selectItem).toBeTruthy();
  });
});
